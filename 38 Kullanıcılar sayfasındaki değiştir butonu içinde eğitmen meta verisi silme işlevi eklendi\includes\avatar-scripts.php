<?php
/**
 * Avatar Stilleri ve Scriptleri
 *
 * <PERSON><PERSON> dosya, avatar stilleri, başlık animasyonları ve diğer scriptleri WordPress'e kaydetmek için kullanılır.
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Avatar stillerini, başlık animasyonlarını ve diğer scriptleri kaydet
 */
function tutor_avatar_enqueue_scripts() {
    // Avatar stilleri
    wp_enqueue_style(
        'tutor-avatar-styles',
        plugin_dir_url(__FILE__) . '../assets/css/avatar-styles.css',
        array(),
        time()
    );

    // Admin yazısı ve yıldızları gizleme stilleri
    wp_enqueue_style(
        'tutor-hide-admin-stars',
        plugin_dir_url(__FILE__) . '../assets/css/hide-admin-stars.css',
        array(),
        time()
    );

    // Başlık animasyonlarını devre dışı bırakma stilleri
    wp_enqueue_style(
        'tutor-disable-title-animations',
        plugin_dir_url(__FILE__) . '../assets/css/disable-title-animations.css',
        array(),
        time()
    );

    // Sayfa geçiş animasyonlarını koruma stilleri
    wp_enqueue_style(
        'tutor-preserve-page-transitions',
        plugin_dir_url(__FILE__) . '../assets/css/preserve-page-transitions.css',
        array(),
        time()
    );

    // Başlık animasyonlarını devre dışı bırakma scriptleri
    wp_enqueue_script(
        'tutor-load-title-fix',
        plugin_dir_url(__FILE__) . '../assets/js/load-title-fix.js',
        array('jquery'),
        time(),
        true
    );

    // Dashboard sayfasında kullanılacak verileri script'e aktar
    wp_localize_script(
        'tutor-load-title-fix',
        'tutor_dashboard_spotlight_data',
        array(
            'plugin_url' => plugin_dir_url(__FILE__) . '../',
            'site_name' => get_bloginfo('name')
        )
    );
}

// Dashboard sayfasında stilleri ve scriptleri kaydet
add_action('wp_enqueue_scripts', 'tutor_avatar_enqueue_scripts');
