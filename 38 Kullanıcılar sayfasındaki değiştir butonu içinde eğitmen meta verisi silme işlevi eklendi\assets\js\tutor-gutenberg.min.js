(()=>{var t={8296:()=>{(function(t,r){var e=r.i18n.__;var n="tutor-frontend-builder-trigger";var o='\n        <a id="'.concat(n,'" class="tutor-btn tutor-btn-primary tutor-btn-sm tutor-text-nowrap" href="').concat(tutorInlineData.frontend_dashboard_url,'" target="_blank">\n        ').concat(e("Edit with Frontend Course Builder","tutor"),"\n        </a>\n    ");var a=document.getElementById("editor");if(!a){return}var u=r.data.subscribe((function(){setTimeout((function(){if(!document.getElementById(n)){var t=a.querySelector(".edit-post-header-toolbar");if(t instanceof HTMLElement){t.insertAdjacentHTML("beforeend",o)}}}),100)}))})(window,wp)}};var r={};function e(n){var o=r[n];if(o!==undefined){return o.exports}var a=r[n]={exports:{}};t[n](a,a.exports,e);return a.exports}(()=>{e.n=t=>{var r=t&&t.__esModule?()=>t["default"]:()=>t;e.d(r,{a:r});return r}})();(()=>{e.d=(t,r)=>{for(var n in r){if(e.o(r,n)&&!e.o(t,n)){Object.defineProperty(t,n,{enumerable:true,get:r[n]})}}}})();(()=>{e.o=(t,r)=>Object.prototype.hasOwnProperty.call(t,r)})();var n={};(()=>{"use strict";var t=e(8296);var r=e.n(t)})()})();