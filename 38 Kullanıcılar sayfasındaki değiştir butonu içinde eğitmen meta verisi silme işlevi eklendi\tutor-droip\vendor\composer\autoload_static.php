<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit3be4cc09de4ef73671b8bca8d9117f0b
{
    public static $prefixLengthsPsr4 = array (
        'T' => 
        array (
            'TutorLMSDroip\\' => 14,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'TutorLMSDroip\\' => 
        array (
            0 => __DIR__ . '/../..' . '/backend',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit3be4cc09de4ef73671b8bca8d9117f0b::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit3be4cc09de4ef73671b8bca8d9117f0b::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit3be4cc09de4ef73671b8bca8d9117f0b::$classMap;

        }, null, ClassLoader::class);
    }
}
