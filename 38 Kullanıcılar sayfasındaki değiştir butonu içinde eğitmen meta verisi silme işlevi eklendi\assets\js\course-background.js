/**
 * JavaScript to add a specific class to body for course viewing pages
 */

document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on a course viewing page
    const isCourseViewingPage = 
        document.querySelector('.tutor-course-single-content-wrapper') || 
        document.querySelector('.tutor-single-lesson-wrap') ||
        document.querySelector('.tutor-single-course-wrap') ||
        document.querySelector('.tutor-course-single-sidebar-wrapper');
    
    // If we're on a course viewing page, add a specific class to body
    if (isCourseViewingPage) {
        document.body.classList.add('tutor-course-viewing-page');
    }
});
