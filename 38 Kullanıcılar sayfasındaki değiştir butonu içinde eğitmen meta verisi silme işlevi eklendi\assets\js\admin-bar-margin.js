/**
 * Admin Bar Margin Adjuster for Tutor LMS
 *
 * Bu script WordPress admin barının varlığını kontrol eder ve
 * .tutor-course-single-sidebar-title elementine uygun margin-top değerini ekler.
 * Admin bar varsa masaüstünde 30px, mobil/tablet'te 50px margin-top ekler, yoksa margin-top eklemez.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Admin barın varlığını kontrol et
    const adminBarExists = document.getElementById('wpadminbar') !== null;

    // Sidebar başlık elementini bul
    const sidebarTitleElements = document.querySelectorAll('.tutor-course-single-sidebar-title');

    // Ekran genişliğini kontrol et (1199px ve altı mobil/tablet olarak kabul edilir)
    const isMobileOrTablet = window.innerWidth <= 1199;

    // Her sidebar başlık elementine uygun margin-top değerini ekle
    sidebarTitleElements.forEach(function(element) {
        if (adminBarExists) {
            if (isMobileOrTablet) {
                // Mobil/tablet cihazlarda admin bar varsa 50px margin-top ekle
                element.style.marginTop = '50px';
            } else {
                // Masaüstü cihazlarda admin bar varsa 30px margin-top ekle
                element.style.marginTop = '30px';
            }
        } else {
            // Admin bar yoksa margin-top'ı sıfırla
            element.style.marginTop = '0px';
        }
    });

    // Ekran boyutu değiştiğinde margin'i güncelle
    window.addEventListener('resize', function() {
        const isNowMobileOrTablet = window.innerWidth <= 1199;

        // Ekran boyutu kategorisi değiştiyse margin'i güncelle
        if (isNowMobileOrTablet !== isMobileOrTablet && adminBarExists) {
            sidebarTitleElements.forEach(function(element) {
                if (isNowMobileOrTablet) {
                    element.style.marginTop = '50px';
                } else {
                    element.style.marginTop = '30px';
                }
            });
        }
    });
});
