/**
 * Tutor Sidebar Overlay
 * Bu script, mobil/tablet görünümünde sidebar açıldığında overlay ekler ve
 * overlay'e tıklandığında sidebar'ı kapatır
 */

(function($) {
    'use strict';

    // DOM yüklendiğinde çalışacak
    $(document).ready(function() {
        initSidebarOverlay();
    });

    /**
     * Sidebar overlay işlevselliğini başlat
     */
    function initSidebarOverlay() {
        // Overlay elementi yoksa oluştur
        if (!$('.tutor-sidebar-overlay').length) {
            $('body').append('<div class="tutor-sidebar-overlay"></div>');
        }

        // Hamburger menü butonlarını seç
        const hamburgerButtons = $('[tutor-course-topics-sidebar-toggler], [tutor-course-topics-sidebar-offcanvas-toggler]');
        const sidebar = $('.tutor-course-single-sidebar-wrapper');
        const overlay = $('.tutor-sidebar-overlay');
        const closeButton = $('[tutor-hide-course-single-sidebar]');

        // Hamburger butonlarına tıklama olayı ekle
        hamburgerButtons.on('click', function(e) {
            // Sadece mobil/tablet görünümünde overlay'i göster
            if (window.matchMedia('(max-width: 1199px)').matches) {
                // Sidebar açılıyor mu kontrol et
                if (!sidebar.hasClass('tutor-lesson-sidebar-show')) {
                    // Sidebar açılıyor, overlay'i göster ve sidebar'a sınıf ekle
                    sidebar.addClass('tutor-lesson-sidebar-show');
                    overlay.addClass('tutor-sidebar-overlay-show');
                    // Hamburger butonuna aktif sınıfı ekle
                    $(this).addClass('tutor-hamburger-active');
                    $('.tutor-mobile-hamburger-btn').addClass('tutor-hamburger-active');
                } else {
                    // Sidebar kapanıyor, overlay'i gizle ve sidebar'dan sınıfı kaldır
                    sidebar.removeClass('tutor-lesson-sidebar-show');
                    overlay.removeClass('tutor-sidebar-overlay-show');
                    // Hamburger butonundan aktif sınıfı kaldır
                    $('.tutor-mobile-hamburger-btn').removeClass('tutor-hamburger-active');
                    $(this).removeClass('tutor-hamburger-active');
                }
            }
        });

        // Overlay'e tıklama olayı ekle
        overlay.on('click', function(e) {
            // Overlay'e tıklandığında sidebar'ı kapat
            sidebar.removeClass('tutor-lesson-sidebar-show');
            overlay.removeClass('tutor-sidebar-overlay-show');
            // Hamburger butonundan aktif sınıfı kaldır
            hamburgerButtons.removeClass('tutor-hamburger-active');
        });

        // Kapatma butonuna tıklama olayı ekle
        closeButton.on('click', function(e) {
            // Kapatma butonuna tıklandığında overlay'i gizle
            overlay.removeClass('tutor-sidebar-overlay-show');
            // Hamburger butonundan aktif sınıfı kaldır
            hamburgerButtons.removeClass('tutor-hamburger-active');
        });
    }

})(jQuery);
