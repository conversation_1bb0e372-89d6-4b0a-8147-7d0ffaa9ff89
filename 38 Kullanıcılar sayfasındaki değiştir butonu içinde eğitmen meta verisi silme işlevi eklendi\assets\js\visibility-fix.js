/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON>ürlük Düzeltmeleri
 * Bu script, dashboard'daki bazı elementlerin görünürlük sorunlarını düzeltmek için kullanılır.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Dashboard sayfasında olup olmadığını kontrol et
    if (document.querySelector('.tutor-dashboard')) {
        // Sayfa tamamen yüklendikten sonra çalıştır
        setTimeout(function() {
            // Devam Eden Kurslar bölümünü bul
            const courseProgressSection = document.querySelector('.tutor-frontend-dashboard-course-progress');
            if (courseProgressSection) {
                // Görünürlüğünü sağla
                courseProgressSection.style.display = 'block';
                courseProgressSection.style.opacity = '1';
                courseProgressSection.style.visibility = 'visible';

                // İçindeki tüm elementlerin görünürlüğün<PERSON> sağla
                const allElements = courseProgressSection.querySelectorAll('*');
                allElements.forEach(function(element) {
                    element.style.opacity = '1';
                    element.style.visibility = 'visible';

                    // Eğer bir kart ise display özelliğini ayarla
                    if (element.classList.contains('tutor-course-card') ||
                        element.classList.contains('tutor-course-listing-item')) {
                        element.style.display = 'block';
                    }

                    // Eğer bir resim ise display özelliğini ayarla
                    if (element.tagName === 'IMG') {
                        element.style.display = 'block';
                    }

                    // Eğer bir başlık ise display özelliğini ayarla
                    if (element.classList.contains('tutor-course-listing-item-title') ||
                        element.classList.contains('tutor-course-card-title')) {
                        element.style.display = 'block';
                        element.style.margin = '15px 0 10px';
                        element.style.padding = '0 15px';
                    }

                    // Eğer bir ilerleme çubuğu ise display özelliğini ayarla
                    if (element.classList.contains('tutor-progress-bar')) {
                        element.style.display = 'block';
                        element.style.height = '8px';
                        element.style.backgroundColor = '#e9ecef';
                        element.style.borderRadius = '999px';
                        element.style.overflow = 'hidden';
                        element.style.margin = '10px 0';
                    }

                    // Eğer bir ilerleme değeri ise display özelliğini ayarla
                    if (element.classList.contains('tutor-progress-value')) {
                        element.style.display = 'block';
                        element.style.height = '100%';
                        element.style.background = 'linear-gradient(90deg, var(--tutor-color-primary), var(--tutor-color-primary))';
                        element.style.borderRadius = '999px';
                    }
                });
            }

            // Kurs kartlarını bul
            const courseCards = document.querySelectorAll('.tutor-course-card, .tutor-course-listing-item');
            courseCards.forEach(function(card) {
                card.style.display = 'block';
                card.style.opacity = '1';
                card.style.visibility = 'visible';
                card.style.backgroundColor = '#fff';
                card.style.borderRadius = '8px';
                card.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.05)';
                card.style.marginBottom = '20px';
                card.style.overflow = 'hidden';
                card.style.border = '1px solid #e9ecef';

                // İçindeki tüm elementlerin görünürlüğünü sağla
                const cardElements = card.querySelectorAll('*');
                cardElements.forEach(function(element) {
                    element.style.opacity = '1';
                    element.style.visibility = 'visible';
                });

                // Kurs resimlerini bul ve düzelt
                const thumbnail = card.querySelector('.tutor-course-thumbnail');
                if (thumbnail) {
                    thumbnail.style.display = 'block';
                    thumbnail.style.overflow = 'hidden';
                    thumbnail.style.borderRadius = '8px 8px 0 0';

                    const img = thumbnail.querySelector('img');
                    if (img) {
                        img.style.display = 'block';
                        img.style.width = '100%';
                        img.style.height = 'auto';
                        img.style.objectFit = 'cover';
                    }
                }

                // Kurs başlığını bul ve düzelt
                const title = card.querySelector('.tutor-course-listing-item-title, .tutor-course-card-title');
                if (title) {
                    title.style.display = 'block';
                    title.style.fontSize = '16px';
                    title.style.fontWeight = '600';
                    title.style.color = '#212327';
                    title.style.margin = '15px 0 10px';
                    title.style.padding = '0 15px';
                    title.style.lineHeight = '1.4';

                    const titleLink = title.querySelector('a');
                    if (titleLink) {
                        titleLink.style.textDecoration = 'none';
                        titleLink.style.color = '#212327';
                    }
                }

                // İlerleme çubuğunu bul ve düzelt
                const progressBar = card.querySelector('.tutor-progress-bar');
                if (progressBar) {
                    progressBar.style.display = 'block';
                    progressBar.style.height = '8px';
                    progressBar.style.backgroundColor = '#e9ecef';
                    progressBar.style.borderRadius = '999px';
                    progressBar.style.overflow = 'hidden';
                    progressBar.style.margin = '10px 15px';

                    const progressValue = progressBar.querySelector('.tutor-progress-value');
                    if (progressValue) {
                        progressValue.style.display = 'block';
                        progressValue.style.height = '100%';
                        progressValue.style.background = 'linear-gradient(90deg, var(--tutor-color-primary), var(--tutor-color-primary))';
                        progressValue.style.borderRadius = '999px';
                    }
                }
            });

            // Animasyon sınıflarını kaldır
            document.querySelectorAll('.tutor-animate-card').forEach(function(element) {
                element.classList.remove('tutor-animate-card');
            });

            // Tüm dashboard içeriğinin görünürlüğünü sağla
            const dashboardContent = document.querySelector('.tutor-dashboard-content-inner');
            if (dashboardContent) {
                dashboardContent.style.opacity = '1';
                dashboardContent.style.visibility = 'visible';

                // İçindeki tüm elementlerin görünürlüğünü sağla
                const contentElements = dashboardContent.querySelectorAll('*');
                contentElements.forEach(function(element) {
                    element.style.opacity = '1';
                    element.style.visibility = 'visible';
                });

                // Tabloları düzelt
                const tables = dashboardContent.querySelectorAll('table');
                tables.forEach(function(table) {
                    table.style.display = 'table';
                    table.style.width = '100%';
                    table.style.borderCollapse = 'separate';
                    table.style.borderSpacing = '0';
                    table.style.borderRadius = '8px';
                    table.style.overflow = 'hidden';
                    table.style.marginBottom = '30px';
                    table.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.05)';
                    table.style.border = '1px solid #e9ecef';

                    // Tablo satırlarını düzelt
                    const rows = table.querySelectorAll('tr');
                    rows.forEach(function(row) {
                        row.style.display = 'table-row';
                        row.style.transition = 'none';

                        // Tablo hücrelerini düzelt
                        const cells = row.querySelectorAll('td, th');
                        cells.forEach(function(cell) {
                            cell.style.display = 'table-cell';
                            cell.style.padding = '15px';
                            cell.style.opacity = '1';
                            cell.style.visibility = 'visible';

                            if (cell.tagName === 'TH') {
                                cell.style.fontWeight = '600';
                                cell.style.color = '#212327';
                                cell.style.backgroundColor = '#f8f9fa';
                                cell.style.borderBottom = '2px solid #e9ecef';
                                cell.style.textAlign = 'left';
                            } else {
                                cell.style.borderBottom = '1px solid #e9ecef';
                                cell.style.color = '#6c757d';
                                cell.style.verticalAlign = 'middle';
                            }
                        });
                    });

                    // Yıldız derecelendirmelerini düzelt
                    const starRatings = table.querySelectorAll('.tutor-star-rating-group');
                    starRatings.forEach(function(rating) {
                        rating.style.display = 'flex';
                        rating.style.alignItems = 'center';
                        rating.style.opacity = '1';
                        rating.style.visibility = 'visible';

                        const stars = rating.querySelectorAll('.tutor-icon-star-line, .tutor-icon-star-full, .tutor-icon-star-half');
                        stars.forEach(function(star) {
                            star.style.display = 'inline-block';
                            star.style.opacity = '1';
                            star.style.visibility = 'visible';

                            if (star.classList.contains('tutor-icon-star-full') || star.classList.contains('tutor-icon-star-half')) {
                                star.style.color = '#f9a134';
                            } else {
                                star.style.color = '#e9ecef';
                            }

                            star.style.fontSize = '16px';
                            star.style.marginRight = '2px';
                        });
                    });
                });
            }

            // Devam Eden Kurslar başlığını düzelt
            const courseProgressTitle = document.querySelector('.tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-text-capitalize.tutor-mb-24');
            if (courseProgressTitle) {
                courseProgressTitle.style.display = 'block';
                courseProgressTitle.style.visibility = 'visible';
                courseProgressTitle.style.opacity = '1';
                courseProgressTitle.style.marginBottom = '24px';
                courseProgressTitle.style.fontSize = '18px';
                courseProgressTitle.style.fontWeight = '500';
                courseProgressTitle.style.color = '#212327';
            }

            // Kurslarım başlığını düzelt
            const myCoursesTitle = document.querySelector('.tutor-fs-5.tutor-fw-medium.tutor-color-black');
            if (myCoursesTitle) {
                myCoursesTitle.style.display = 'block';
                myCoursesTitle.style.visibility = 'visible';
                myCoursesTitle.style.opacity = '1';
                myCoursesTitle.style.marginBottom = '24px';
                myCoursesTitle.style.fontSize = '18px';
                myCoursesTitle.style.fontWeight = '500';
                myCoursesTitle.style.color = '#212327';
            }
        }, 500);
    }
});
