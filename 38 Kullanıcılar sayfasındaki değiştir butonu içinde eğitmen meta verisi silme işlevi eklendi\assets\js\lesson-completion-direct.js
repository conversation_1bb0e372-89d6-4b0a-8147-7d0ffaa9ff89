/**
 * Ders tamamlama işlemlerini yöneten JavaScript - Doğrudan DOM manipülasyonu
 * Bu script, ders tamamlama butonunun davranışını değiştirir
 * Buton tıklandığında AJAX ile ders tamamlama/tamamlamayı geri alma işlemlerini yapar
 */

(function($) {
    'use strict';

    // DOM yüklendiğinde çalışacak
    $(document).ready(function() {
        console.log('Lesson Completion Direct JS yüklendi');
        initLessonCompletionButton();
    });

    /**
     * Ders tamamlama butonunu başlat
     */
    function initLessonCompletionButton() {
        // Sayfa yüklendiğinde buton durumunu kontrol et ve güncelle
        const initialButton = $('.lesson-completion-btn');
        if (initialButton.length) {
            // HTML özniteliğinden tamamlanma durumunu al
            const initialCompleted = initialButton.attr('data-completed') === 'true';
            console.log('Sayfa yüklendiğinde buton durumu:', initialCompleted ? 'tamamlandı' : 'tamamlanmadı');

            // Butonun data özelliğini güncelle (jQuery data cache'i güncelle)
            initialButton.data('completed', initialCompleted ? 'true' : 'false');
        }

        // Ders tamamlama butonuna tıklama olayı ekle
        $(document).on('click', '.lesson-completion-btn', function(e) {
            e.preventDefault();

            const button = $(this);
            const lessonId = button.attr('data-lesson-id');
            // HTML özniteliğinden tamamlanma durumunu al, data() yerine attr() kullan
            const isCompleted = button.attr('data-completed') === 'true';

            console.log('Ders tamamlama butonuna tıklandı:', lessonId, 'Tamamlandı mı:', isCompleted);

            // Butonun durumunu değiştir (tıklanamaz yap)
            button.prop('disabled', true);

            // AJAX isteği gönder
            $.ajax({
                url: tutor_data.ajaxurl,
                type: 'POST',
                data: {
                    action: 'toggle_lesson_completion',
                    lesson_id: lessonId,
                    is_completed: isCompleted,
                    _wpnonce: tutor_data.nonce
                },
                success: function(response) {
                    console.log('AJAX başarılı:', response);

                    if (response.success) {
                        // Butonun durumunu güncelle
                        updateButtonState(button, !isCompleted);

                        // Sidebar'daki tamamlanma işaretini güncelle - doğrudan DOM manipülasyonu
                        updateSidebarDirectly(lessonId, !isCompleted);

                        // İlerleme çubuğunu güncelle - doğrudan DOM manipülasyonu
                        updateProgressBarDirectly(response.data);
                    } else {
                        console.error('İşlem başarısız:', response.data);
                        alert('İşlem başarısız: ' + (response.data?.message || 'Bilinmeyen hata'));
                    }

                    // Butonu tekrar tıklanabilir yap
                    button.prop('disabled', false);
                },
                error: function(xhr, status, error) {
                    console.error('AJAX hatası:', status, error);
                    alert('Sunucu hatası: ' + error);

                    // Butonu tekrar tıklanabilir yap
                    button.prop('disabled', false);
                }
            });
        });
    }

    /**
     * Butonun durumunu güncelle
     *
     * @param {jQuery} button - Buton elementi
     * @param {boolean} isCompleted - Tamamlanma durumu
     */
    function updateButtonState(button, isCompleted) {
        console.log('Buton durumu güncelleniyor:', isCompleted ? 'tamamlandı' : 'tamamlanmadı');

        // Butonun data özelliğini güncelle (hem jQuery cache hem de HTML özniteliği)
        button.data('completed', isCompleted ? 'true' : 'false');
        button.attr('data-completed', isCompleted ? 'true' : 'false');

        // Butonun sınıfını güncelle
        if (isCompleted) {
            button.removeClass('tutor-btn-primary').addClass('tutor-btn-success');
            button.find('.completion-btn-text').text('Tamamlandı');
        } else {
            button.removeClass('tutor-btn-success').addClass('tutor-btn-primary');
            button.find('.completion-btn-text').text('Dersi tamamla');
        }

        // Zorla yeniden render için kısa bir süre için butonun görünürlüğünü değiştir
        button.css('opacity', '0.9');
        setTimeout(function() {
            button.css('opacity', '1');
        }, 50);
    }

    /**
     * Sidebar'daki tamamlanma işaretini doğrudan güncelle
     *
     * @param {number} lessonId - Ders ID'si
     * @param {boolean} isCompleted - Tamamlanma durumu
     */
    function updateSidebarDirectly(lessonId, isCompleted) {
        console.log('Sidebar doğrudan güncelleniyor, ders ID:', lessonId, 'Tamamlandı mı:', isCompleted);

        // Ders ID'sine göre sidebar öğesini bul
        const sidebarItem = $('a[data-lesson-id="' + lessonId + '"]').closest('.tutor-course-topic-item');

        if (sidebarItem.length) {
            console.log('Sidebar öğesi bulundu:', sidebarItem);

            // Checkbox'u bul
            const checkbox = sidebarItem.find('.tutor-form-check-input');

            if (checkbox.length) {
                console.log('Checkbox bulundu, durumu güncelleniyor');

                // Checkbox'u güncelle
                if (isCompleted) {
                    checkbox.prop('checked', true);
                    checkbox.attr('checked', 'checked');

                    // Görünürlüğü zorlamak için DOM'u yeniden oluştur
                    const parent = checkbox.parent();
                    checkbox.remove();
                    parent.prepend('<input checked="checked" type="checkbox" class="tutor-form-check-input tutor-form-check-circle" disabled readonly />');
                } else {
                    checkbox.prop('checked', false);
                    checkbox.removeAttr('checked');

                    // Görünürlüğü zorlamak için DOM'u yeniden oluştur
                    const parent = checkbox.parent();
                    checkbox.remove();
                    parent.prepend('<input type="checkbox" class="tutor-form-check-input tutor-form-check-circle" disabled readonly />');
                }
            } else {
                console.log('Checkbox bulunamadı');
            }
        } else {
            console.log('Sidebar öğesi bulunamadı, alternatif yöntem deneniyor');

            // Alternatif yöntem: Tüm ders öğelerini kontrol et
            $('a[href*="' + lessonId + '"]').each(function() {
                const item = $(this).closest('.tutor-course-topic-item');
                const checkbox = item.find('.tutor-form-check-input');

                if (checkbox.length) {
                    console.log('Alternatif yöntemle checkbox bulundu');

                    if (isCompleted) {
                        checkbox.prop('checked', true);
                        checkbox.attr('checked', 'checked');

                        // Görünürlüğü zorlamak için DOM'u yeniden oluştur
                        const parent = checkbox.parent();
                        checkbox.remove();
                        parent.prepend('<input checked="checked" type="checkbox" class="tutor-form-check-input tutor-form-check-circle" disabled readonly />');
                    } else {
                        checkbox.prop('checked', false);
                        checkbox.removeAttr('checked');

                        // Görünürlüğü zorlamak için DOM'u yeniden oluştur
                        const parent = checkbox.parent();
                        checkbox.remove();
                        parent.prepend('<input type="checkbox" class="tutor-form-check-input tutor-form-check-circle" disabled readonly />');
                    }
                }
            });
        }

        // Son çare: Sayfayı yenileme olmadan sidebar'ı yeniden yükle
        if (typeof tutor_course_builder !== 'undefined' && typeof tutor_course_builder.nonce !== 'undefined') {
            console.log('Sidebar yeniden yükleniyor');

            $.ajax({
                url: tutor_data.ajaxurl,
                type: 'POST',
                data: {
                    action: 'tutor_load_lesson_sidebar',
                    course_id: $('input[name="course_id"]').val() || $('meta[name="course_id"]').attr('content'),
                    _wpnonce: tutor_data.nonce
                },
                success: function(response) {
                    if (response.success && response.data) {
                        // Sidebar içeriğini güncelle
                        $('.tutor-course-single-sidebar-wrapper').html(response.data);
                    }
                }
            });
        }
    }

    /**
     * İlerleme çubuğunu doğrudan güncelle
     *
     * @param {Object} data - AJAX yanıtından gelen veri
     */
    function updateProgressBarDirectly(data) {
        console.log('İlerleme çubuğu doğrudan güncelleniyor:', data);

        if (!data || !data.completed_percent) {
            console.log('İlerleme verisi bulunamadı');
            return;
        }

        const progressValue = data.completed_percent;
        const completedLessons = data.completed_lessons;
        const totalLessons = data.total_lessons;
        const courseId = data.course_id;

        console.log('Tamamlanan dersler:', completedLessons, 'Toplam dersler:', totalLessons, 'Kurs ID:', courseId);

        // CSS değişkenini doğrudan güncelle
        document.documentElement.style.setProperty('--tutor-progress-value', progressValue + '%');

        // Tüm ilerleme çubuklarını güncelle
        $('.tutor-progress-bar').each(function() {
            $(this).css('--tutor-progress-value', progressValue + '%');
        });

        // Yüzde metinlerini güncelle
        $('.tutor-fs-7.tutor-color-muted, .progress-percentage .tutor-fw-medium').each(function() {
            const text = $(this).text();
            if (text.includes('%')) {
                $(this).html(progressValue + '% <span>Tamamlandı</span>');
            }
        });

        // Ana ilerleme özetini güncelle (kurs başlığının yanındaki)
        if (completedLessons !== undefined && totalLessons !== undefined) {
            // Sadece ana ilerleme özetini güncelle, topic'leri değil
            $('.tutor-course-completion-progress-bar-percentage').text(completedLessons + '/' + totalLessons);
        }

        // Topic başlıklarının yanındaki tamamlanma sayılarını güncelle
        if (completedLessons !== undefined && totalLessons !== undefined && courseId) {
            // Sadece ilgili dersin bulunduğu topic'i güncelle
            const lessonId = data.lesson_id;
            updateTopicCompletionCounts(courseId, lessonId);
        }

        // Animasyon ekle
        $('.tutor-progress-bar').addClass('tutor-progress-updated');
        setTimeout(function() {
            $('.tutor-progress-bar').removeClass('tutor-progress-updated');
        }, 300);
    }

    /**
     * Topic başlıklarının yanındaki tamamlanma sayılarını güncelle
     *
     * @param {number} courseId - Kurs ID'si
     * @param {number} lessonId - Ders ID'si (opsiyonel)
     */
    function updateTopicCompletionCounts(courseId, lessonId) {
        console.log('Topic tamamlanma sayıları güncelleniyor, kurs ID:', courseId, 'Ders ID:', lessonId || 'Yok');

        // AJAX ile topic tamamlanma sayılarını al
        $.ajax({
            url: tutor_data.ajaxurl,
            type: 'POST',
            data: {
                action: 'get_topic_completion_counts',
                course_id: courseId,
                lesson_id: lessonId || 0,
                _wpnonce: tutor_data.nonce
            },
            success: function(response) {
                console.log('Topic tamamlanma sayıları alındı:', response);

                if (response.success && response.data) {
                    // Sadece yanıtta dönen topic'leri güncelle (normalde sadece bir topic olmalı)
                    $.each(response.data, function(topicId, counts) {
                        console.log('Topic güncelleniyor, ID:', topicId, 'Tamamlanan:', counts.completed, 'Toplam:', counts.total);

                        // Önce mevcut değeri al
                        const topicSummary = $('.tutor-course-topic-' + topicId + ' .tutor-course-topic-summary');
                        const currentText = topicSummary.text();

                        // Eğer değer değiştiyse güncelle
                        if (topicSummary.length && currentText !== counts.completed + '/' + counts.total) {
                            topicSummary.text(counts.completed + '/' + counts.total);

                            // Görsel geri bildirim için kısa bir animasyon ekle
                            topicSummary.addClass('tutor-topic-updated');
                            setTimeout(function() {
                                topicSummary.removeClass('tutor-topic-updated');
                            }, 500);
                        }
                    });
                }
            },
            error: function(xhr, status, error) {
                console.error('Topic tamamlanma sayıları alınamadı:', status, error);
            }
        });
    }

})(jQuery);
