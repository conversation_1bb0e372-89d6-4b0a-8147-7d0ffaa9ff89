/**
 * Dashboard Spotlight Modu
 *
 * <PERSON>u script, Tutor LMS dashboard sayfalarında tema header ve footer'ı gizlemek için
 * gerekli sınıfları ekler ve sidebar'ı sticky yapar.
 */

jQuery(document).ready(function($) {
    // Dashboard sayfasında olup olmadığımızı kontrol et
    if ($('.tutor-dashboard').length > 0) {
        // Body'ye tutor-dashboard-page sınıfını ekle
        $('body').addClass('tutor-dashboard-page');

        // Sayfa yüklendiğinde scroll'u en üste taşı
        window.scrollTo(0, 0);

        // Logo URL'sini al ve uygula
        if (typeof tutor_dashboard_spotlight_data !== 'undefined') {
            // Logo elementini bul
            var logoElement = $('.tutor-dashboard-logo img');

            if (logoElement.length > 0) {
                // Logo elementini güncelle
                logoElement.attr('src', tutor_dashboard_spotlight_data.logo_url);
                logoElement.attr('alt', tutor_dashboard_spotlight_data.site_name);
            }
        }

        // Sol taraftaki header alanını sağ taraftaki header alanına taşı
        var leftHeader = $('.tutor-header-left-side.tutor-dashboard-header');
        var rightHeader = $('.tutor-header-right-side');

        if (leftHeader.length > 0 && rightHeader.length > 0) {
            // Sol header içeriğini kopyala ve sağ header'a ekle
            var avatarElement = leftHeader.find('.tutor-dashboard-header-avatar').clone();
            var userInfoElement = leftHeader.find('.tutor-user-info').clone();

            // Sağ header'a ekle
            rightHeader.append(avatarElement);
            rightHeader.append(userInfoElement);
        }

        // Profil alanının görünürlüğünü sağla
        $('.tutor-dashboard-header, .tutor-frontend-dashboard-header, .tutor-header-left-side, .tutor-header-right-side').css({
            'display': 'flex',
            'visibility': 'visible',
            'opacity': '1'
        });

        $('.tutor-dashboard-header-avatar').css({
            'display': 'block',
            'visibility': 'visible',
            'opacity': '1'
        });

        $('.tutor-user-info').css({
            'display': 'flex',
            'visibility': 'visible',
            'opacity': '1'
        });

        $('.tutor-dashboard-header-username, .tutor-dashboard-header-stats, .tutor-dashboard-header-ratings, .tutor-dashboard-header-display-name, .tutor-dashboard-header-greetings').css({
            'display': 'block',
            'visibility': 'visible',
            'opacity': '1'
        });

        // Mobil menü toggle butonu ekle
        if ($('.tutor-dashboard-menu-toggler').length === 0) {
            $('.tutor-frontend-dashboard-header').prepend(
                '<button class="tutor-dashboard-menu-toggler tutor-btn tutor-btn-outline-primary tutor-d-block tutor-d-lg-none">' +
                '<i class="tutor-icon-hamburger-menu"></i>' +
                '</button>'
            );

            // Mobil menü toggle işlevselliği
            $('.tutor-dashboard-menu-toggler').on('click', function() {
                $('.tutor-dashboard-left-menu').toggleClass('show');
            });

            // Sayfa dışına tıklandığında menüyü kapat
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.tutor-dashboard-left-menu').length &&
                    !$(e.target).closest('.tutor-dashboard-menu-toggler').length) {
                    $('.tutor-dashboard-left-menu').removeClass('show');
                }
            });
        }

        // Tema tarafından eklenen gereksiz wrapper'ları düzelt
        setTimeout(function() {
            $('.tutor-dashboard').parents().each(function() {
                // Ana içerik alanlarını tam genişliğe ayarla
                if ($(this).hasClass('container') ||
                    $(this).hasClass('content-area') ||
                    $(this).hasClass('site-content') ||
                    $(this).hasClass('wrapper')) {
                    $(this).css({
                        'max-width': '100%',
                        'width': '100%',
                        'padding': '0',
                        'margin': '0'
                    });
                }
            });
        }, 100);
    }
});
