/**
 * <PERSON><PERSON><PERSON><PERSON> DASHBOARD STİLLERİ
 * <PERSON><PERSON>ya, dashboard g<PERSON>rünümünü daha modern hale getirmek için kullanılır.
 * <PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON> (renkler, hover efektleri, animasyonlar vb.) değiştirir,
 * layout'a ve konumlandırmalara dokunmaz.
 */

/* ===== MODERN RENK ŞEMASI ===== */
:root {
  --tutor-primary: #4361ee;
  --tutor-primary-hover: #3a56d4;
  --tutor-secondary: #7209b7;
  --tutor-success: #06d6a0;
  --tutor-warning: #ffd166;
  --tutor-danger: #ef476f;
  --tutor-info: #118ab2;
  --tutor-light: #f8f9fa;
  --tutor-dark: #212529;
  --tutor-gray: #6c757d;
  --tutor-gray-light: #e9ecef;
  --tutor-gray-lighter: #f5f7fa;

  /* <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>nkler */
  --tutor-primary-rgb: 67, 97, 238;
  --tutor-primary-light: rgba(67, 97, 238, 0.1);
  --tutor-primary-lighter: rgba(67, 97, 238, 0.05);
  --tutor-secondary-rgb: 114, 9, 183;
  --tutor-secondary-light: rgba(114, 9, 183, 0.1);

  /* Gölgeler */
  --tutor-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --tutor-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
  --tutor-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --tutor-shadow-hover: 0 8px 15px rgba(67, 97, 238, 0.15);

  /* Geçişler */
  --tutor-transition: all 0.3s ease;
  --tutor-transition-fast: all 0.2s ease;
  --tutor-transition-slow: all 0.5s ease;

  /* Border radius */
  --tutor-radius-sm: 6px;
  --tutor-radius: 10px;
  --tutor-radius-lg: 16px;
  --tutor-radius-xl: 24px;
  --tutor-radius-full: 9999px;
}

/* ===== GENEL STIL İYİLEŞTİRMELERİ ===== */

/* Tüm elementler için geçişleri kaldır */
.tutor-dashboard * {
  transition: none !important;
}

/* Butonlar için modern stil */
.tutor-btn {
  border-radius: var(--tutor-radius) !important;
  transition: var(--tutor-transition) !important;
  box-shadow: var(--tutor-shadow-sm) !important;
  border: none !important;
  font-weight: 500 !important;
  letter-spacing: 0.3px !important;
}

.tutor-btn:hover {
  transform: none !important;
  box-shadow: var(--tutor-shadow-sm) !important;
}

.tutor-btn-primary {
  background-color: var(--tutor-color-primary) !important;
  background-image: none !important;
  border: none !important;
}

.tutor-btn-primary:hover {
  background-color: var(--tutor-color-primary) !important;
  opacity: 0.9 !important;
  background-image: none !important;
}

/* Dersi Tamamla butonu için orijinal stil */
.tutor-topbar-mark-btn.tutor-btn-primary {
  background-color: color-mix(in srgb, var(--tutor-color-primary) 10%, transparent) !important;
  background-image: none !important;
  color: var(--tutor-color-primary) !important;
  border: none !important;
  box-shadow: none !important;
}

.tutor-topbar-mark-btn.tutor-btn-primary:hover {
  background-color: color-mix(in srgb, var(--tutor-color-primary) 15%, transparent) !important;
  background-image: none !important;
  color: var(--tutor-color-primary) !important;
  transform: translateY(0) !important;
  box-shadow: none !important;
}

/* Kurs satış sayfasındaki Enroll Now butonu için orijinal stil */
.tutor-enroll-course-button {
  background-color: var(--tutor-color-primary) !important;
  background-image: none !important;
  color: #ffffff !important;
  border: none !important;
  box-shadow: none !important;
}

.tutor-enroll-course-button:hover {
  background-color: var(--tutor-color-primary) !important;
  opacity: 0.9 !important;
  background-image: none !important;
  color: #ffffff !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Tamamlandı butonu için orijinal stil */
.tutor-topbar-mark-btn.tutor-btn-success {
  background-color: color-mix(in srgb, var(--tutor-success) 10%, transparent) !important;
  background-image: none !important;
  color: var(--tutor-success) !important;
  border: none !important;
  box-shadow: none !important;
}

.tutor-topbar-mark-btn.tutor-btn-success:hover {
  background-color: color-mix(in srgb, var(--tutor-success) 15%, transparent) !important;
  background-image: none !important;
  color: var(--tutor-success) !important;
  transform: translateY(0) !important;
  box-shadow: none !important;
}

.tutor-btn-outline-primary,
.tutor-btn.tutor-btn-outline-primary {
  border: 1px solid var(--tutor-color-primary) !important;
  color: var(--tutor-color-primary) !important;
  background-color: transparent !important;
}

.tutor-btn-outline-primary:hover,
.tutor-btn.tutor-btn-outline-primary:hover {
  background-color: color-mix(in srgb, var(--tutor-color-primary) 10%, transparent) !important;
  color: var(--tutor-color-primary) !important;
}

/* Kurs Oluştur butonu için özel stil */
.tutor-btn-outline-primary[href*="kurs-olustur"],
.tutor-btn-outline-primary[href*="course-create"] {
  border: 1px solid var(--tutor-color-primary) !important;
  color: var(--tutor-color-primary) !important;
  background-color: transparent !important;
  box-shadow: none !important;
}

.tutor-btn-outline-primary[href*="kurs-olustur"]:hover,
.tutor-btn-outline-primary[href*="course-create"]:hover {
  background-color: color-mix(in srgb, var(--tutor-color-primary) 10%, transparent) !important;
  color: var(--tutor-color-primary) !important;
  transform: none !important;
  box-shadow: none !important;
}

.tutor-btn-ghost {
  background: transparent !important;
  box-shadow: none !important;
}

.tutor-btn-ghost:hover {
  background-color: var(--tutor-gray-lighter) !important;
  transform: translateY(0) !important;
  box-shadow: none !important;
}

/* ===== DASHBOARD SIDEBAR STİLLERİ ===== */

/* Sidebar arka planı */
.tutor-dashboard-left-menu {
  background: linear-gradient(180deg, var(--tutor-light), #ffffff) !important;
  box-shadow: var(--tutor-shadow) !important;
  border-right: 1px solid rgba(0, 0, 0, 0.05) !important;
}

/* Sidebar menü öğeleri */
.tutor-dashboard-menu-item-link {
  border-radius: var(--tutor-radius) !important;
  transition: var(--tutor-transition) !important;
  margin-bottom: 5px !important;
  border: none !important;
}



.tutor-dashboard-menu-item-link:hover {
  background-color: var(--tutor-primary-lighter) !important;
  color: var(--tutor-color-primary) !important;
  transform: translateX(5px) !important;
}

/* Tüm aktif menü öğeleri için stil */
.tutor-dashboard-menu-item-link.is-active,
.tutor-dashboard-menu-item-link.active,
.tutor-dashboard-menu-item.active .tutor-dashboard-menu-item-link {
  background-color: color-mix(in srgb, var(--tutor-color-primary) 20%, transparent) !important;
  color: var(--tutor-color-primary) !important;
  font-weight: 600 !important;
  border-left: 3px solid var(--tutor-color-primary) !important;
}

/* Tüm aktif menü öğelerinin ikonları için stil */
.tutor-dashboard-menu-item-link.is-active i,
.tutor-dashboard-menu-item-link.active i,
.tutor-dashboard-menu-item.active .tutor-dashboard-menu-item-link i,
.tutor-dashboard-menu-item-link.is-active .tutor-dashboard-menu-item-icon,
.tutor-dashboard-menu-item-link.active .tutor-dashboard-menu-item-icon,
.tutor-dashboard-menu-item.active .tutor-dashboard-menu-item-link .tutor-dashboard-menu-item-icon {
  color: var(--tutor-color-primary) !important;
}

/* Aktif seçeneğin hover efektini düzelt */
.tutor-dashboard-menu-item-link.is-active:hover,
.tutor-dashboard-menu-item-link.active:hover,
.tutor-dashboard-menu-item.active .tutor-dashboard-menu-item-link:hover {
  background-color: color-mix(in srgb, var(--tutor-color-primary) 25%, transparent) !important;
  color: var(--tutor-color-primary) !important;
  transform: translateX(5px) !important;
}

/* Çıkış Yap butonu için özel stil */
.tutor-dashboard-menu-item:last-child .tutor-dashboard-menu-item-link {
  transition: var(--tutor-transition) !important;
}

.tutor-dashboard-menu-item:last-child .tutor-dashboard-menu-item-link:hover {
  background-color: rgba(239, 71, 111, 0.1) !important;
  color: var(--tutor-danger) !important;
  transform: translateX(5px) !important;
}

.tutor-dashboard-menu-item:last-child .tutor-dashboard-menu-item-link.is-active,
.tutor-dashboard-menu-item:last-child .tutor-dashboard-menu-item-link.active,
.tutor-dashboard-menu-item:last-child.active .tutor-dashboard-menu-item-link {
  background-color: color-mix(in srgb, var(--tutor-danger) 20%, transparent) !important;
  color: var(--tutor-danger) !important;
  font-weight: 600 !important;
  border-left: 3px solid var(--tutor-danger) !important;
}

.tutor-dashboard-menu-item:last-child .tutor-dashboard-menu-item-link.is-active i,
.tutor-dashboard-menu-item:last-child .tutor-dashboard-menu-item-link.active i,
.tutor-dashboard-menu-item:last-child.active .tutor-dashboard-menu-item-link i,
.tutor-dashboard-menu-item:last-child .tutor-dashboard-menu-item-link.is-active .tutor-dashboard-menu-item-icon,
.tutor-dashboard-menu-item:last-child .tutor-dashboard-menu-item-link.active .tutor-dashboard-menu-item-icon,
.tutor-dashboard-menu-item:last-child.active .tutor-dashboard-menu-item-link .tutor-dashboard-menu-item-icon {
  color: var(--tutor-danger) !important;
}

.tutor-dashboard-menu-item:last-child .tutor-dashboard-menu-item-link.is-active:hover,
.tutor-dashboard-menu-item:last-child .tutor-dashboard-menu-item-link.active:hover,
.tutor-dashboard-menu-item:last-child.active .tutor-dashboard-menu-item-link:hover {
  background-color: color-mix(in srgb, var(--tutor-danger) 25%, transparent) !important;
  color: var(--tutor-danger) !important;
  transform: translateX(5px) !important;
}

/* Sidebar menü ikonları */
.tutor-dashboard-menu-item-link i,
.tutor-dashboard-menu-item-link .tutor-icon-dashboard {
  transition: var(--tutor-transition) !important;
  color: var(--tutor-color-primary) !important;
}

.tutor-dashboard-menu-item-link:hover i,
.tutor-dashboard-menu-item-link:hover .tutor-icon-dashboard,
.tutor-dashboard-menu-item-link:hover .tutor-dashboard-menu-item-icon {
  color: var(--tutor-color-primary) !important;
}

.tutor-dashboard-menu-item-link.is-active i,
.tutor-dashboard-menu-item-link.is-active .tutor-icon-dashboard {
  color: #ffffff !important;
}

/* Logo konteyner */
.tutor-dashboard-logo-container {
  border-bottom: 1px solid var(--tutor-gray-light) !important;
  /* background-color: rgba(255, 255, 255, 0.8) !important; */
}

.tutor-dashboard-logo {
  background-color: transparent !important;
  transition: var(--tutor-transition) !important;
}

.tutor-dashboard-logo:hover {
  transform: scale(1.03) !important;
}

.tutor-dashboard-logo img {
  transition: var(--tutor-transition) !important;
}

/* ===== HEADER STİLLERİ ===== */

/* Header arka planı */
.tutor-frontend-dashboard-header {
  background-color: rgba(255, 255, 255, 0.8) !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
}

/* Avatar stilini modernleştir */
.tutor-dashboard-header-avatar .tutor-avatar.tutor-avatar-xl {
  border-radius: var(--tutor-radius-full) !important;
  border: 2px solid var(--tutor-primary-light) !important;
  overflow: hidden !important;
  transition: var(--tutor-transition) !important;
}

.tutor-dashboard-header-avatar .tutor-avatar.tutor-avatar-xl:hover {
  transform: scale(1.1) !important;
  box-shadow: var(--tutor-shadow-hover) !important;
}

/* ===== DROPDOWN MENÜ STİLLERİ ===== */

/* Dropdown menü konteyneri */
.tutor-avatar-dropdown {
  border-radius: var(--tutor-radius) !important;
  box-shadow: var(--tutor-shadow-lg) !important;
  border: 1px solid var(--tutor-gray-light) !important;
  overflow: hidden !important;
}

/* Dropdown menü başlığı */
.tutor-avatar-dropdown-header {
  background-color: var(--tutor-gray-lighter) !important;
}

/* Dropdown menü öğeleri */
.tutor-avatar-dropdown-menu li a {
  transition: var(--tutor-transition-fast) !important;
}

.tutor-avatar-dropdown-menu li a:hover {
  background-color: var(--tutor-primary-lighter) !important;
  color: var(--tutor-color-primary) !important;
  transform: translateX(5px) !important;
}

/* Çıkış yap butonu */
.tutor-avatar-dropdown-menu li a.tutor-avatar-dropdown-logout:hover {
  background-color: rgba(239, 71, 111, 0.1) !important;
  color: var(--tutor-danger) !important;
}

/* ===== İÇERİK ALANI STİLLERİ ===== */

/* Kartlar için modern stil */
.tutor-card {
  border-radius: var(--tutor-radius) !important;
  border: 1px solid var(--tutor-gray-light) !important;
  box-shadow: var(--tutor-shadow-sm) !important;
  transition: var(--tutor-transition) !important;
  overflow: hidden !important;
}

.tutor-card:hover {
  transform: translateY(-5px) !important;
  box-shadow: var(--tutor-shadow-hover) !important;
}

/* İstatistik kartları */
.tutor-dashboard-overview-cards .tutor-card {
  border-radius: var(--tutor-radius) !important;
  border: none !important;
  background: linear-gradient(135deg, #ffffff, var(--tutor-gray-lighter)) !important;
  box-shadow: var(--tutor-shadow) !important;
}

.tutor-dashboard-overview-cards .tutor-card:hover {
  transform: translateY(-5px) !important;
  box-shadow: var(--tutor-shadow-hover) !important;
}

/* Kurs kartları */
.tutor-course-card,
.tutor-course-listing-item {
  border-radius: var(--tutor-radius) !important;
  overflow: hidden !important;
  box-shadow: var(--tutor-shadow-sm) !important;
  transition: var(--tutor-transition) !important;
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
  background-color: #fff !important;
  border: 1px solid var(--tutor-gray-light) !important;
  margin-bottom: 20px !important;
}

.tutor-course-card:hover,
.tutor-course-listing-item:hover {
  transform: translateY(-5px) !important;
  box-shadow: var(--tutor-shadow-hover) !important;
}

.tutor-course-card .tutor-course-thumbnail,
.tutor-course-listing-item .tutor-course-thumbnail {
  overflow: hidden !important;
  border-radius: var(--tutor-radius) var(--tutor-radius) 0 0 !important;
  position: relative !important;
}

.tutor-course-card .tutor-course-thumbnail img,
.tutor-course-listing-item .tutor-course-thumbnail img {
  transition: var(--tutor-transition-slow) !important;
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
  width: 100% !important;
  height: auto !important;
  object-fit: cover !important;
}

.tutor-course-card:hover .tutor-course-thumbnail img,
.tutor-course-listing-item:hover .tutor-course-thumbnail img {
  transform: scale(1.1) !important;
}

/* Kurs içerik görünürlüğünü düzelt */
.tutor-frontend-dashboard-course-progress,
.tutor-frontend-dashboard-course-progress *,
.tutor-course-listing-item,
.tutor-course-listing-item * {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Kurs başlığı stilleri */
.tutor-course-card-title,
.tutor-course-listing-item-title {
  font-size: 16px !important;
  font-weight: 600 !important;
  margin: 15px 0 10px !important;
  padding: 0 15px !important;
  line-height: 1.4 !important;
  display: block !important;
}

.tutor-course-card-title a,
.tutor-course-listing-item-title a {
  color: var(--tutor-dark) !important;
  text-decoration: none !important;
  transition: var(--tutor-transition) !important;
}

.tutor-course-card-title a:hover,
.tutor-course-listing-item-title a:hover {
  color: var(--tutor-primary) !important;
}

/* Kurs ilerleme çubuğu */
.tutor-progress-bar {
  height: 8px !important;
  border-radius: var(--tutor-radius-full) !important;
  background-color: var(--tutor-gray-light) !important;
  overflow: hidden !important;
  margin: 10px 15px !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.tutor-progress-value {
  background: linear-gradient(90deg, var(--tutor-color-primary), var(--tutor-color-primary)) !important;
  border-radius: var(--tutor-radius-full) !important;
  position: relative !important;
  overflow: hidden !important;
  height: 100% !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.tutor-progress-value:after {
  content: "" !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.2),
    rgba(255, 255, 255, 0.1)) !important;
  width: 50% !important;
  transform: translateX(-100%) !important;
  animation: shimmer 2s infinite !important;
}

/* Tamamlanma metni */
.tutor-course-completion-text {
  font-size: 14px !important;
  color: var(--tutor-gray) !important;
  margin: 5px 15px !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

@keyframes shimmer {
  100% {
    transform: translateX(100%) !important;
  }
}

/* Tablolar */
.tutor-table,
.tutor-dashboard-content-inner table {
  border-radius: var(--tutor-radius) !important;
  overflow: hidden !important;
  box-shadow: var(--tutor-shadow-sm) !important;
  width: 100% !important;
  border-collapse: separate !important;
  border-spacing: 0 !important;
  margin-bottom: 30px !important;
  display: table !important;
  opacity: 1 !important;
  visibility: visible !important;
  border: 1px solid var(--tutor-gray-light) !important;
}

.tutor-table tr,
.tutor-dashboard-content-inner table tr {
  transition: var(--tutor-transition-fast) !important;
  display: table-row !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.tutor-table tr:hover,
.tutor-dashboard-content-inner table tr:hover {
  background-color: var(--tutor-primary-lighter) !important;
}

.tutor-table th,
.tutor-dashboard-content-inner table th {
  background-color: var(--tutor-gray-lighter) !important;
  border-bottom: 2px solid var(--tutor-gray-light) !important;
  font-weight: 600 !important;
  color: var(--tutor-dark) !important;
  padding: 15px 20px !important;
  text-align: left !important;
  display: table-cell !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.tutor-table td,
.tutor-dashboard-content-inner table td {
  padding: 15px 20px !important;
  border-bottom: 1px solid var(--tutor-gray-light) !important;
  color: var(--tutor-text) !important;
  vertical-align: middle !important;
  display: table-cell !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.tutor-table tr:last-child td,
.tutor-dashboard-content-inner table tr:last-child td {
  border-bottom: none !important;
}

/* Yıldız derecelendirme */
.tutor-dashboard-content-inner table .tutor-star-rating-group {
  display: flex !important;
  align-items: center !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.tutor-dashboard-content-inner table .tutor-icon-star-line,
.tutor-dashboard-content-inner table .tutor-icon-star-full,
.tutor-dashboard-content-inner table .tutor-icon-star-half {
  display: inline-block !important;
  opacity: 1 !important;
  visibility: visible !important;
  font-size: 16px !important;
  margin-right: 2px !important;
}

.tutor-dashboard-content-inner table .tutor-icon-star-full,
.tutor-dashboard-content-inner table .tutor-icon-star-half {
  color: #f9a134 !important;
}

.tutor-dashboard-content-inner table .tutor-icon-star-line {
  color: #e9ecef !important;
}

/* Form elemanları */
.tutor-form-control {
  border-radius: var(--tutor-radius) !important;
  border: 1px solid var(--tutor-gray-light) !important;
  transition: var(--tutor-transition) !important;
  box-shadow: var(--tutor-shadow-sm) !important;
}

.tutor-form-control:focus {
  border-color: var(--tutor-primary) !important;
  box-shadow: 0 0 0 3px var(--tutor-primary-lighter) !important;
}

/* Sekmeler */
.tutor-nav-tabs {
  border-bottom: 1px solid var(--tutor-gray-light) !important;
}

.tutor-nav-tabs .tutor-nav-link {
  border-radius: var(--tutor-radius) var(--tutor-radius) 0 0 !important;
  transition: var(--tutor-transition) !important;
}

.tutor-nav-tabs .tutor-nav-link:hover {
  background-color: var(--tutor-primary-lighter) !important;
  border-color: transparent !important;
}

.tutor-nav-tabs .tutor-nav-link.is-active {
  border-bottom: 2px solid var(--tutor-primary) !important;
  color: var(--tutor-primary) !important;
  font-weight: 600 !important;
}

/* ===== KAYDIRMA ÇUBUĞU STİLLERİ ===== */

/* Webkit (Chrome, Safari, Edge) için kaydırma çubuğu */
.tutor-dashboard ::-webkit-scrollbar {
  width: 6px !important;
  height: 6px !important;
  background-color: transparent !important;
}

.tutor-dashboard ::-webkit-scrollbar-track {
  background-color: transparent !important;
  border-radius: var(--tutor-radius-full) !important;
}

.tutor-dashboard ::-webkit-scrollbar-thumb {
  background-color: rgba(var(--tutor-primary-rgb), 0.3) !important;
  border-radius: var(--tutor-radius-full) !important;
  transition: var(--tutor-transition) !important;
}

.tutor-dashboard ::-webkit-scrollbar-thumb:hover {
  background-color: rgba(var(--tutor-primary-rgb), 0.5) !important;
}

/* Firefox için kaydırma çubuğu */
.tutor-dashboard {
  scrollbar-width: thin !important;
  scrollbar-color: rgba(var(--tutor-primary-rgb), 0.3) transparent !important;
}

/* ===== ANİMASYONLAR ===== */

/* Sayfa yüklenme animasyonu */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Kart animasyonu */
@keyframes cardFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Pulsating animasyonu */
@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(var(--tutor-primary-rgb), 0.7);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(var(--tutor-primary-rgb), 0);
  }

  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(var(--tutor-primary-rgb), 0);
  }
}

/* Shimmer animasyonu */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.tutor-dashboard-content-inner {
  animation: none !important;
}

/* Kart animasyonları */
.tutor-animate-card {
  animation: none !important;
  opacity: 1 !important;
}

/* İçerik görünürlüğünü düzelt */
.tutor-dashboard-content-inner * {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Kart animasyonları için gecikme sınıfları */
.tutor-dashboard-overview-cards .tutor-col:nth-child(1) .tutor-card,
.tutor-dashboard-overview-cards .tutor-col:nth-child(2) .tutor-card,
.tutor-dashboard-overview-cards .tutor-col:nth-child(3) .tutor-card,
.tutor-dashboard-overview-cards .tutor-col:nth-child(4) .tutor-card {
  animation-delay: 0s !important;
}

/* Pulsating dot animasyonu (bildirimler için) */
@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(var(--tutor-primary-rgb), 0.7);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(var(--tutor-primary-rgb), 0);
  }

  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(var(--tutor-primary-rgb), 0);
  }
}

.tutor-notification-dot {
  display: inline-block !important;
  width: 8px !important;
  height: 8px !important;
  background-color: var(--tutor-primary) !important;
  border-radius: var(--tutor-radius-full) !important;
  animation: pulse 2s infinite !important;
}

/* ===== RESPONSIVE DÜZENLEMELER ===== */

@media (max-width: 991px) {
  /* Mobil menü toggle butonu */
  .tutor-mobile-menu-toggle {
    background-color: var(--tutor-primary) !important;
    color: white !important;
    border-radius: var(--tutor-radius-full) !important;
    width: 40px !important;
    height: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-shadow: var(--tutor-shadow) !important;
    transition: var(--tutor-transition) !important;
  }

  /* Mobil görünümde Dersi Tamamla butonu için orijinal stil */
  .tutor-topbar-mark-btn.tutor-btn-primary,
  .tutor-mobile-complete-btn .tutor-topbar-mark-btn.tutor-btn-primary {
    background-color: transparent !important;
    background-image: none !important;
    color: var(--tutor-color-primary) !important;
    border: 1px solid var(--tutor-color-primary) !important;
    box-shadow: none !important;
  }

  /* Mobil görünümde Tamamlandı butonu için orijinal stil */
  .tutor-topbar-mark-btn.tutor-btn-success,
  .tutor-mobile-complete-btn .tutor-topbar-mark-btn.tutor-btn-success {
    background-color: transparent !important;
    background-image: none !important;
    color: var(--tutor-success) !important;
    border: 1px solid var(--tutor-success) !important;
    box-shadow: none !important;
  }

  .tutor-mobile-menu-toggle:hover {
    background-color: var(--tutor-primary-hover) !important;
    transform: scale(1.1) !important;
  }

  /* Mobil sidebar */
  .tutor-dashboard-left-menu {
    box-shadow: var(--tutor-shadow-lg) !important;
  }

  /* Mobil header */
  .tutor-frontend-dashboard-header {
    background-color: rgba(255, 255, 255, 0.8) !important;
  }

  /* Mobil header içindeki butonlar - Butonların stilini koruyoruz */
  .tutor-frontend-dashboard-header .tutor-btn {
    background-color: transparent !important;
    color: var(--tutor-primary) !important;
  }

  .tutor-frontend-dashboard-header .tutor-btn:hover {
    background-color: var(--tutor-primary-lighter) !important;
    color: var(--tutor-color-primary) !important;
  }

  /* Mobil avatar */
  .tutor-frontend-dashboard-header .tutor-avatar {
    border: 2px solid white !important;
  }
}
