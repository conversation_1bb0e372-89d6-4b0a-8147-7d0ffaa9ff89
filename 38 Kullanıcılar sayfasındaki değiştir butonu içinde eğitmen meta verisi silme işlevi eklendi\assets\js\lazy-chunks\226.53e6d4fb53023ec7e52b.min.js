"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[226],{3226:(t,e,r)=>{r.r(e);r.d(e,{default:()=>Gc});var n=r(9752);var i=r(2339);var o=r(5587);var a=r(917);var u=r(8003);var s=r(7363);var c=r(1533);var l=r(9250);var d=r(74);var f=r(5033);var p=r(6595);var v=r(96);var h=r(4285);var y=r(2329);var m=r(202);var g=r(7536);var _=r(2798);var b=r(3389);var w=r(9592);var Z=r(4900);var x=r(8877);var O=r(9612);var E=r(7619);var k=r(932);var q=r(7100);var S=r(7941);var j=r(5453);var P=r(9768);var A=r(1162);var T=r(8777);var C=r(6764);var I=r(9528);var D=r(178);var L=r(1343);var W=r(81);var z=r(5589);var N=r(7034);var U=r(8305);var J=r(6413);var Q=r(1537);var F=r(5460);var M=r(2377);var B=r(5219);var G=r(9169);var R=r(7363);function V(t){"@babel/helpers - typeof";return V="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},V(t)}function H(){H=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return H.apply(this,arguments)}function K(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */K=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function t(e,r,n){return e[r]=n}}function c(t,e,r,i){var o=e&&e.prototype instanceof f?e:f,a=Object.create(o.prototype),u=new E(i||[]);return n(a,"_invoke",{value:w(t,r,u)}),a}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};s(h,o,(function(){return this}));var y=Object.getPrototypeOf,m=y&&y(y(k([])));m&&m!==e&&r.call(m,o)&&(h=m);var g=v.prototype=f.prototype=Object.create(h);function _(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function b(t,e){function i(n,o,a,u){var s=l(t[n],t,o);if("throw"!==s.type){var c=s.arg,d=c.value;return d&&"object"==V(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){i("next",t,a,u)}),(function(t){i("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return i("throw",t,a,u)}))}u(s.arg)}var o;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){i(r,n,t,e)}))}return o=o?o.then(a,a):a()}})}function w(t,e,r){var n="suspendedStart";return function(i,o){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===i)throw o;return q()}for(r.method=i,r.arg=o;;){var a=r.delegate;if(a){var u=Z(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var s=l(t,e,r);if("normal"===s.type){if(n=r.done?"completed":"suspendedYield",s.arg===d)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(n="completed",r.method="throw",r.arg=s.arg)}}}function Z(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,Z(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var i=l(n,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,d;var o=i.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function x(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(x,this),this.reset(!0)}function k(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return i.next=i}}return{next:q}}function q(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=s(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,s(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},_(b.prototype),s(b.prototype,a,(function(){return this})),t.AsyncIterator=b,t.async=function(e,r,n,i,o){void 0===o&&(o=Promise);var a=new b(c(e,r,n,i),o);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},_(g),s(g,u,"Generator"),s(g,o,(function(){return this})),s(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,E.prototype={constructor:E,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function i(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],u=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(s&&c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function t(e,n){for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var i=n.completion;if("throw"===i.type){var o=i.arg;O(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function Y(t,e,r,n,i,o,a){try{var u=t[o](a);var s=u.value}catch(t){r(t);return}if(u.done){e(s)}else{Promise.resolve(s).then(n,i)}}function $(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var o=t.apply(e,r);function a(t){Y(o,n,i,a,u,"next",t)}function u(t){Y(o,n,i,a,u,"throw",t)}a(undefined)}))}}function X(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function tt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?X(Object(r),!0).forEach((function(e){et(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):X(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function et(t,e,r){e=rt(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function rt(t){var e=nt(t,"string");return V(e)==="symbol"?e:String(e)}function nt(t,e){if(V(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(V(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var it=(0,N.zs)();var ot=[{label:(0,u.__)("Weeks","tutor"),value:"weeks"},{label:(0,u.__)("Days","tutor"),value:"days"},{label:(0,u.__)("Hours","tutor"),value:"hours"}];var at=function t(e){var r;var n=e.assignmentId,i=n===void 0?"":n,o=e.topicId,c=e.closeModal,l=e.icon,v=e.title,h=e.subtitle,y=e.contentDripType;var _=(0,W.l)(),b=_.fields;var w=!!U.y.tutor_pro_url;var x=((r=U.y.settings)===null||r===void 0?void 0:r.chatgpt_enable)==="on";var O=(0,z.T3)(i,o);var E=(0,z.CR)(it);var k=(0,m.NL)();var q=O.data;var N=k.getQueryData(["Topic",it]);var Q=(0,M.O)({defaultValues:{title:"",summary:"",attachments:[],time_duration:{value:"0",time:"weeks"},total_mark:10,pass_mark:5,upload_files_limit:1,upload_file_size_limit:2,content_drip_settings:{unlock_date:"",after_xdays_of_enroll:"",prerequisites:[]}},shouldFocusError:true,mode:"onChange"});var F=Q.formState.dirtyFields&&Object.keys(Q.formState.dirtyFields).length>0;(0,s.useEffect)((function(){if(q){var t,e,r;Q.reset(tt({title:q.post_title||"",summary:(0,B.qH)(q.post_content)||"",attachments:q.attachments||[],time_duration:{value:q.assignment_option.time_duration.value||"0",time:q.assignment_option.time_duration.time||"weeks"},total_mark:q.assignment_option.total_mark||10,pass_mark:q.assignment_option.pass_mark||5,upload_files_limit:q.assignment_option.upload_files_limit||1,upload_file_size_limit:q.assignment_option.upload_file_size_limit||2,content_drip_settings:{unlock_date:(q===null||q===void 0?void 0:(t=q.content_drip_settings)===null||t===void 0?void 0:t.unlock_date)||"",after_xdays_of_enroll:(q===null||q===void 0?void 0:(e=q.content_drip_settings)===null||e===void 0?void 0:e.after_xdays_of_enroll)||"",prerequisites:(q===null||q===void 0?void 0:(r=q.content_drip_settings)===null||r===void 0?void 0:r.prerequisites)||[]}},Object.fromEntries((0,B.hk)({fields:b.Curriculum.Lesson}).map((function(t){return[t,q[t]]})))),{keepDirty:false})}var n=setTimeout((function(){Q.setFocus("title")}),0);return function(){clearTimeout(n)}}),[q]);var V=function(){var t=$(K().mark((function t(e){var r,n;return K().wrap((function t(a){while(1)switch(a.prev=a.next){case 0:r=(0,z.iL)(e,i,o,y,(0,B.hk)({fields:b.Curriculum.Assignment}));a.next=3;return E.mutateAsync(r);case 3:n=a.sent;if(n.status_code===200||n.status_code===201){c({action:"CONFIRM"})}case 5:case"end":return a.stop()}}),t)})));return function e(r){return t.apply(this,arguments)}}();return(0,a.tZ)(D.Z,{onClose:function t(){return c({action:"CLOSE"})},icon:F?(0,a.tZ)(p.Z,{name:"warning",width:24,height:24}):l,title:F?J.iM.isAboveDesktop?(0,u.__)("Unsaved Changes","tutor"):"":v,subtitle:J.iM.isAboveSmallMobile?h:"",maxWidth:1070,actions:F&&(0,a.tZ)(R.Fragment,null,(0,a.tZ)(d.Z,{variant:"text",size:"small",onClick:function t(){if(i){Q.reset()}else{c({action:"CLOSE"})}}},i?(0,u.__)("Discard Changes","tutor"):(0,u.__)("Cancel","tutor")),(0,a.tZ)(d.Z,{loading:E.isPending,variant:"primary",size:"small",onClick:Q.handleSubmit(V)},i?(0,u.__)("Update","tutor"):(0,u.__)("Save","tutor")))},(0,a.tZ)("div",{css:st.wrapper},(0,a.tZ)(Z.Z,{when:!O.isLoading,fallback:(0,a.tZ)(f.fz,null)},(0,a.tZ)("div",null,(0,a.tZ)("div",{css:st.assignmentInfo},(0,a.tZ)(g.Qr,{name:"title",control:Q.control,rules:tt({required:(0,u.__)("Assignment title is required","tutor")},(0,G.T9)(255)),render:function t(e){return(0,a.tZ)(P.Z,H({},e,{label:(0,u.__)("Title","tutor"),placeholder:(0,u.__)("Enter Assignment Title","tutor"),generateWithAi:!w||x,isClearable:true,selectOnFocus:true}))}}),(0,a.tZ)(g.Qr,{name:"summary",control:Q.control,render:function t(e){return(0,a.tZ)(I.Z,H({},e,{label:(0,u.__)("Content","tutor"),placeholder:(0,u.__)("Enter Assignment Content","tutor"),generateWithAi:!w||x}))}}),(0,a.tZ)(L.Z,{section:"Curriculum.Assignment.after_description",form:Q}))),(0,a.tZ)("div",{css:st.rightPanel},(0,a.tZ)(g.Qr,{name:"attachments",control:Q.control,render:function t(e){return(0,a.tZ)(j.Z,H({},e,{label:(0,u.__)("Attachments","tutor"),buttonText:(0,u.__)("Upload Attachment","tutor"),selectMultiple:true}))}}),(0,a.tZ)(Z.Z,{when:(0,B.ro)(J.AO.CONTENT_DRIP)},(0,a.tZ)(Z.Z,{when:y==="specific_days"},(0,a.tZ)(g.Qr,{name:"content_drip_settings.after_xdays_of_enroll",control:Q.control,render:function t(e){return(0,a.tZ)(P.Z,H({},e,{type:"number",label:(0,a.tZ)("div",{css:st.contentDripLabel},(0,a.tZ)(p.Z,{name:"contentDrip",height:24,width:24}),(0,u.__)("Available after days","tutor")),helpText:(0,u.__)("This assignment will be available after the given number of days.","tutor"),placeholder:"0",selectOnFocus:true}))}})),(0,a.tZ)(Z.Z,{when:y==="unlock_by_date"},(0,a.tZ)(g.Qr,{name:"content_drip_settings.unlock_date",control:Q.control,render:function t(e){return(0,a.tZ)(S.Z,H({},e,{label:(0,a.tZ)("div",{css:st.contentDripLabel},(0,a.tZ)(p.Z,{name:"contentDrip",height:24,width:24}),(0,u.__)("Unlock Date","tutor")),placeholder:(0,u.__)("Select Unlock Date","tutor"),helpText:(0,u.__)("This assignment will be available from the given date. Leave empty to make it available immediately.","tutor")}))}})),(0,a.tZ)(Z.Z,{when:y==="after_finishing_prerequisites"},(0,a.tZ)(g.Qr,{name:"content_drip_settings.prerequisites",control:Q.control,render:function t(e){return(0,a.tZ)(C.Z,H({},e,{label:(0,a.tZ)("div",{css:st.contentDripLabel},(0,a.tZ)(p.Z,{name:"contentDrip",height:24,width:24}),(0,u.__)("Prerequisites","tutor")),placeholder:(0,u.__)("Select Prerequisite","tutor"),options:N.reduce((function(t,e){if(e.id===o){t.push(tt(tt({},e),{},{contents:e.contents.filter((function(t){return t.ID!==i}))}))}else{t.push(e)}return t}),[])||[],isSearchable:true,helpText:(0,u.__)("Select items that should be complete before this item","tutor")}))}}))),(0,a.tZ)("div",{css:st.timeLimit},(0,a.tZ)(g.Qr,{name:"time_duration.value",control:Q.control,render:function t(e){return(0,a.tZ)(P.Z,H({},e,{type:"number",label:(0,u.__)("Time Limit","tutor"),placeholder:"0",dataAttribute:"data-time-limit",selectOnFocus:true}))}}),(0,a.tZ)(g.Qr,{name:"time_duration.time",control:Q.control,render:function t(e){return(0,a.tZ)(T.Z,H({},e,{options:ot,removeOptionsMinWidth:true,dataAttribute:"data-time-limit-unit"}))}})),(0,a.tZ)(g.Qr,{name:"total_mark",control:Q.control,render:function t(e){return(0,a.tZ)(P.Z,H({},e,{type:"number",label:(0,u.__)("Total Points","tutor"),placeholder:"0",selectOnFocus:true}))}}),(0,a.tZ)(g.Qr,{name:"pass_mark",control:Q.control,rules:{validate:function t(e){if(Number(e)>Number(Q.getValues("total_mark"))){return(0,u.__)("Pass mark cannot be greater than total mark","tutor")}return true}},render:function t(e){return(0,a.tZ)(P.Z,H({},e,{type:"number",label:(0,u.__)("Minimum Pass Points","tutor"),placeholder:"0",selectOnFocus:true}))}}),(0,a.tZ)(g.Qr,{name:"upload_files_limit",control:Q.control,render:function t(e){return(0,a.tZ)(P.Z,H({},e,{placeholder:"0",type:"number",label:(0,u.__)("File Upload Limit","tutor"),helpText:(0,u.__)("Define the number of files that a student can upload in this assignment. Input 0 to disable the option to upload.","tutor"),selectOnFocus:true}))}}),(0,a.tZ)(g.Qr,{name:"upload_file_size_limit",control:Q.control,render:function t(e){return(0,a.tZ)(A.Z,H({},e,{type:"number",label:(0,u.__)("Maximum File Size Limit","tutor"),placeholder:"0",content:(0,u.__)("MB","tutor"),contentPosition:"right"}))}}),(0,a.tZ)(L.Z,{section:"Curriculum.Assignment.bottom_of_sidebar",form:Q})))))};const ut=at;var st={wrapper:(0,a.iv)("margin:0 auto;display:grid;grid-template-columns:1fr 338px;width:100%;height:100%;padding-inline:",Q.W0[32],";",Q.Uo.smallTablet,"{grid-template-columns:1fr;padding-inline:",Q.W0[24],";}",Q.Uo.mobile,"{padding-inline:",Q.W0[16],";}"+(true?"":0),true?"":0),assignmentInfo:(0,a.iv)("padding-block:",Q.W0[24],";padding-right:",Q.W0[32],";display:flex;flex-direction:column;gap:",Q.W0[24],";position:sticky;top:0;z-index:",Q.W5.positive,";",Q.Uo.smallTablet,"{position:unset;padding-right:0;}"+(true?"":0),true?"":0),rightPanel:(0,a.iv)("border-left:1px solid ",Q.Jv.stroke.divider,";display:flex;flex-direction:column;gap:",Q.W0[16],";padding-block:",Q.W0[24],";padding-left:",Q.W0[32],";",Q.Uo.smallTablet,"{border-left:none;padding-left:0;}"+(true?"":0),true?"":0),timeLimit:(0,a.iv)("display:grid;align-items:end;grid-template-columns:1fr 100px;& input{border:1px solid ",Q.Jv.stroke["default"],";&[data-time-limit]{border-radius:",Q.E0[6]," 0 0 ",Q.E0[6],";border-right:none;&:focus{border-right:1px solid ",Q.Jv.stroke["default"],";z-index:",Q.W5.positive,";}}&[data-time-limit-unit]{border-radius:0 ",Q.E0[6]," ",Q.E0[6]," 0;}}"+(true?"":0),true?"":0),uploadAttachment:(0,a.iv)("display:flex;flex-direction:column;gap:",Q.W0[8],";"+(true?"":0),true?"":0),uploadLabel:(0,a.iv)(F.c.body()," color:",Q.Jv.text.title,";"+(true?"":0),true?"":0),contentDripLabel:(0,a.iv)("display:flex;align-items:center;svg{margin-right:",Q.W0[4],";color:",Q.Jv.icon.success,";}"+(true?"":0),true?"":0)};var ct=r(5043);var lt=r(4857);var dt=r(830);var ft=r(4436);var pt=r(125);var vt=r(9546);var ht=r(1060);var yt=r(5056);var mt=r(249);var gt=r(8551);var _t=r(7307);var bt=r(3603);function wt(t){"@babel/helpers - typeof";return wt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},wt(t)}function Zt(t,e){var r=typeof Symbol!=="undefined"&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=xt(t))||e&&t&&typeof t.length==="number"){if(r)t=r;var n=0;var i=function t(){};return{s:i,n:function e(){if(n>=t.length)return{done:true};return{done:false,value:t[n++]}},e:function t(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o=true,a=false,u;return{s:function e(){r=r.call(t)},n:function t(){var e=r.next();o=e.done;return e},e:function t(e){a=true;u=e},f:function t(){try{if(!o&&r["return"]!=null)r["return"]()}finally{if(a)throw u}}}}function xt(t,e){if(!t)return;if(typeof t==="string")return Ot(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ot(t,e)}function Ot(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Et(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function kt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Et(Object(r),!0).forEach((function(e){qt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Et(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function qt(t,e,r){e=St(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function St(t){var e=jt(t,"string");return wt(e)==="symbol"?e:String(e)}function jt(t,e){if(wt(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(wt(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var Pt={NEW:"new",UPDATE:"update",NO_CHANGE:"no_change"};var At=function t(e,r){var n=function t(e){if(e.image_url){return e.answer_view_format==="text_image"?Pt.NO_CHANGE:Pt.UPDATE}return e.answer_view_format==="text"?Pt.NO_CHANGE:Pt.UPDATE};var i=function t(e){if(e.question_settings){e.question_settings.answer_required=!!Number(e.question_settings.answer_required);e.question_settings.show_question_mark=!!Number(e.question_settings.show_question_mark);e.question_settings.randomize_question=!!Number(e.question_settings.randomize_question)}e.question_answers=e.question_answers.map((function(t){return kt(kt({},t),{},{_data_status:n(t),is_saved:true,answer_view_format:t.image_url?"text_image":"text"})}));e.question_description=(0,B.qH)(e.question_description)||"";e.answer_explanation=e.answer_explanation==='<p><br data-mce-bogus="1"></p>'?"":(0,B.qH)(e.answer_explanation)||"";switch(e.question_type){case"single_choice":{return kt(kt({},e),{},{_data_status:Pt.UPDATE,question_type:"multiple_choice",question_answers:e.question_answers.map((function(t){return kt(kt({},t),{},{_data_status:Pt.UPDATE})})),question_settings:kt(kt({},e.question_settings),{},{question_type:"multiple_choice",has_multiple_correct_answer:false})})}case"multiple_choice":{return kt(kt({},e),{},{_data_status:e.question_settings.has_multiple_correct_answer?Pt.NO_CHANGE:Pt.UPDATE,question_settings:kt(kt({},e.question_settings),{},{has_multiple_correct_answer:e.question_settings.has_multiple_correct_answer?!!Number(e.question_settings.has_multiple_correct_answer):true})})}case"matching":{return kt(kt({},e),{},{_data_status:e.question_settings.is_image_matching?Pt.NO_CHANGE:Pt.UPDATE,question_settings:kt(kt({},e.question_settings),{},{is_image_matching:e.question_settings.is_image_matching?!!Number(e.question_settings.is_image_matching):false})})}case"image_matching":{return kt(kt({},e),{},{_data_status:Pt.UPDATE,question_type:"matching",question_answers:e.question_answers.map((function(t){return kt(kt({},t),{},{_data_status:Pt.UPDATE})})),question_settings:kt(kt({},e.question_settings),{},{question_type:"matching",is_image_matching:true})})}default:return kt(kt({},e),{},{_data_status:Pt.NO_CHANGE})}};return kt({ID:e.ID,_data_status:Pt.NO_CHANGE,quiz_title:e.post_title||"",quiz_description:e.post_content||"",quiz_option:{time_limit:{time_value:e.quiz_option.time_limit.time_value||0,time_type:e.quiz_option.time_limit.time_type||"minutes"},hide_quiz_time_display:e.quiz_option.hide_quiz_time_display==="1",feedback_mode:e.quiz_option.feedback_mode||"retry",attempts_allowed:e.quiz_option.attempts_allowed||10,pass_is_required:e.quiz_option.pass_is_required==="1",passing_grade:e.quiz_option.passing_grade||80,max_questions_for_answer:e.quiz_option.max_questions_for_answer||10,quiz_auto_start:e.quiz_option.quiz_auto_start==="1",question_layout_view:e.quiz_option.question_layout_view||"",questions_order:e.quiz_option.questions_order||"rand",hide_question_number_overview:e.quiz_option.hide_question_number_overview==="1",short_answer_characters_limit:e.quiz_option.short_answer_characters_limit||200,open_ended_answer_characters_limit:e.quiz_option.open_ended_answer_characters_limit||500,content_drip_settings:e.quiz_option.content_drip_settings||{unlock_date:"",after_xdays_of_enroll:0,prerequisites:[]}},questions:(e.questions||[]).map((function(t){return i(t)})),deleted_question_ids:[],deleted_answer_ids:[]},Object.fromEntries(r.map((function(t){return[t,e[t]]}))))};var Tt=function t(e,r,n,i,o,a){return kt(kt(kt(kt({course_id:i,topic_id:r,payload:{ID:e.ID,_data_status:e._data_status,post_title:e.quiz_title,post_content:e.quiz_description,quiz_option:kt(kt(kt({attempts_allowed:e.quiz_option.attempts_allowed,feedback_mode:e.quiz_option.feedback_mode,hide_question_number_overview:e.quiz_option.hide_question_number_overview?"1":"0",hide_quiz_time_display:e.quiz_option.hide_quiz_time_display?"1":"0",max_questions_for_answer:(0,B.ro)(J.AO.H5P_INTEGRATION)&&e.questions.every((function(t){return t.question_type==="h5p"}))?e.questions.length:e.quiz_option.max_questions_for_answer,open_ended_answer_characters_limit:e.quiz_option.open_ended_answer_characters_limit,pass_is_required:e.quiz_option.pass_is_required?"1":"0",passing_grade:e.quiz_option.passing_grade,question_layout_view:e.quiz_option.question_layout_view,questions_order:e.quiz_option.questions_order,quiz_auto_start:e.quiz_option.quiz_auto_start?"1":"0",short_answer_characters_limit:e.quiz_option.short_answer_characters_limit,time_limit:{time_type:e.quiz_option.time_limit.time_type,time_value:e.quiz_option.time_limit.time_value}},(0,B.ro)(J.AO.CONTENT_DRIP)&&n==="unlock_sequentially"&&e.quiz_option.feedback_mode==="retry"&&{pass_is_required:e.quiz_option.pass_is_required?"1":"0"}),(0,B.ro)(J.AO.CONTENT_DRIP)&&{content_drip_settings:kt(kt(kt({},n==="unlock_by_date"&&{unlock_date:e.quiz_option.content_drip_settings.unlock_date}),n==="specific_days"&&{after_xdays_of_enroll:e.quiz_option.content_drip_settings.after_xdays_of_enroll}),n==="after_finishing_prerequisites"&&{prerequisites:e.quiz_option.content_drip_settings.prerequisites})}),(0,B.ro)(J.AO.H5P_INTEGRATION)&&e.questions.every((function(t){return t.question_type==="h5p"}))&&{quiz_type:"tutor_h5p_quiz"}),questions:e.questions.map((function(t){return kt(kt({_data_status:t._data_status,question_id:t.question_id,question_title:t.question_title,question_description:t.question_description,question_mark:t.question_settings.question_mark},!!U.y.tutor_pro_url&&{answer_explanation:t.answer_explanation}),{},{question_type:t.question_type,question_order:t.question_order,question_settings:kt(kt({answer_required:t.question_settings.answer_required?"1":"0",question_mark:t.question_settings.question_mark,question_type:t.question_type,randomize_question:t.question_settings.randomize_question?"1":"0",show_question_mark:t.question_settings.show_question_mark?"1":"0"},t.question_type==="multiple_choice"&&{has_multiple_correct_answer:t.question_settings.has_multiple_correct_answer?"1":"0"}),t.question_type==="matching"&&{is_image_matching:t.question_settings.is_image_matching?"1":"0"}),question_answers:t.question_answers.map((function(e){return{_data_status:e._data_status,answer_id:e.answer_id,belongs_question_id:t.question_id,belongs_question_type:t.question_type,answer_title:e.answer_title,is_correct:e.is_correct,image_id:t.question_type==="matching"&&!t.question_settings.is_image_matching?"":e.image_id,image_url:t.question_type==="matching"&&!t.question_settings.is_image_matching?"":e.image_url,answer_two_gap_match:e.answer_two_gap_match,answer_view_format:e.answer_view_format,answer_order:e.answer_order}}))},Object.fromEntries(o.map((function(e){return[e,t[e]]}))))}))},deleted_question_ids:e.deleted_question_ids,deleted_answer_ids:e.deleted_answer_ids},(0,B.ro)(J.AO.CONTENT_DRIP)&&n==="unlock_by_date"&&{"content_drip_settings[unlock_date]":e.quiz_option.content_drip_settings.unlock_date}),(0,B.ro)(J.AO.CONTENT_DRIP)&&n==="specific_days"&&{"content_drip_settings[after_xdays_of_enroll]":e.quiz_option.content_drip_settings.after_xdays_of_enroll}),(0,B.ro)(J.AO.CONTENT_DRIP)&&n==="after_finishing_prerequisites"&&{"content_drip_settings[prerequisites]":e.quiz_option.content_drip_settings.prerequisites}),Object.fromEntries(a.map((function(t){return[t,e[t]]}))))};var Ct=function t(e){return{question_id:e.question_id,question_title:e.question_title,question_description:e.question_description,question_type:e.question_type,question_mark:e.question_mark,answer_explanation:e.answer_explanation,"question_settings[question_type]":e.question_type,"question_settings[answer_required]":e.question_settings.answer_required?1:0,"question_settings[question_mark]":e.question_mark}};var It=function t(e){return _t.R.post(bt.Z.QUIZ_IMPORT_DATA,e)};var Dt=function t(){var e=(0,m.NL)();var r=(0,b.p)(),n=r.showToast;return(0,mt.D)({mutationFn:It,onSuccess:function t(r){if(r.success){e.invalidateQueries({queryKey:["Topic"]});n({message:(0,u.__)("Quiz imported successfully","tutor"),type:"success"})}else{n({message:r.data.message,type:"danger"})}},onError:function t(e){n({message:(0,B.Mo)(e),type:"danger"})}})};var Lt=function t(e){return _t.R.post(bt.Z.QUIZ_EXPORT_DATA,{quiz_id:e})};var Wt=function t(){var e=(0,b.p)(),r=e.showToast;return(0,mt.D)({mutationFn:Lt,onSuccess:function t(e){if(!e.success){r({message:(0,u.__)("Something went wrong.","tutor"),type:"danger"});return}var n="";var i=Zt(e.data.output_quiz_data),o;try{for(i.s();!(o=i.n()).done;){var a=o.value;var s=a.join(",");n+="".concat(s,"\r\n")}}catch(t){i.e(t)}finally{i.f()}var c=new Blob([n],{type:"text/csv"});var l=window.webkitURL.createObjectURL(c);var d=document.createElement("a");d.setAttribute("href",l);d.setAttribute("download","tutor-quiz-".concat(e.data.title,".csv"));document.body.appendChild(d);d.click()},onError:function t(e){r({message:(0,B.Mo)(e),type:"danger"})}})};var zt=function t(e){return _t.R.post(bt.Z.SAVE_QUIZ,e)};var Nt=function t(){var e=(0,m.NL)();var r=(0,b.p)(),n=r.showToast;return(0,mt.D)({mutationFn:zt,onSuccess:function t(r){if(r.data){e.setQueryData(["Quiz",r.data.ID],r.data);e.invalidateQueries({queryKey:["Topic"]});n({message:(0,u.__)(r.message,"tutor"),type:"success"})}},onError:function t(e){n({message:(0,B.Mo)(e),type:"danger"})}})};var Ut=function t(e){return _t.R.get(bt.Z.GET_QUIZ_DETAILS,{params:{quiz_id:e}})};var Jt=function t(e){return(0,gt.a)({queryKey:["Quiz",e],queryFn:function t(){return Ut(e).then((function(t){return t.data}))},enabled:!!e})};var Qt=function t(e,r){if(r===e){return null}if(e===Pt.NEW){return Pt.NEW}if((e===Pt.UPDATE||e===Pt.NO_CHANGE)&&r===Pt.UPDATE){return Pt.UPDATE}return Pt.NO_CHANGE};var Ft=function t(e){return _t.R.post(bt.Z.GET_H5P_QUIZ_CONTENT,{search_filter:e})};var Mt=function t(e,r){return(0,gt.a)({queryKey:["H5PQuizContents",e],queryFn:function t(){return Ft(e).then((function(t){return t.data}))},enabled:r==="tutor_h5p_quiz"})};var Bt=function t(e){return wpAjaxInstance.post(endpoints.GET_H5P_QUIZ_CONTENT_BY_ID,{content_id:e}).then((function(t){return t.data}))};var Gt=function t(e,r){return useQuery({queryKey:["H5PQuizContent",e],queryFn:function t(){return Bt(e)},enabled:!!e&&r==="tutor_h5p_quiz"})};var Rt=function t(e){return _t.R.post(bt.Z.DELETE_QUIZ,{quiz_id:e})};var Vt=function t(){var e=(0,m.NL)();var r=(0,b.p)(),n=r.showToast;return(0,mt.D)({mutationFn:Rt,onSuccess:function t(r){if(r.status_code===200){n({message:(0,u.__)(r.message,"tutor"),type:"success"});e.invalidateQueries({queryKey:["Topic"]})}},onError:function t(e){n({message:(0,B.Mo)(e),type:"danger"})}})};var Ht=r(6074);var Kt=r(5340);function Yt(){Yt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Yt.apply(this,arguments)}function $t(t){return ee(t)||te(t)||ie(t)||Xt()}function Xt(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function te(t){if(typeof Symbol!=="undefined"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function ee(t){if(Array.isArray(t))return oe(t)}function re(t,e){return ue(t)||ae(t,e)||ie(t,e)||ne()}function ne(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function ie(t,e){if(!t)return;if(typeof t==="string")return oe(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return oe(t,e)}function oe(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function ae(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],s=!0,c=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,i=t}finally{try{if(!s&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw i}}return u}}function ue(t){if(Array.isArray(t))return t}var se=function t(e){var r=e.title,n=e.closeModal,i=e.onAddContent,o=e.contentType,c=e.addedContentIds,l=c===void 0?[]:c;var f=(0,M.O)({defaultValues:{search:""}});var v=(0,Kt.N)(f.watch("search"),300);var h=Mt(v,o);var y=(0,z.ri)(v,o);var m=(0,s.useState)([]),_=re(m,2),b=_[0],w=_[1];var x=o==="tutor_h5p_quiz"?h.data:y.data;var O=x===null||x===void 0?void 0:x.output.filter((function(t){return!l.includes(String(t.id))}));var E=[{Header:(0,a.tZ)("div",{"data-index":true,css:le.tableLabel},O!==null&&O!==void 0&&O.length?(0,a.tZ)(Ht.Z,{onChange:function t(e){if(e){w((O===null||O===void 0?void 0:O.filter((function(t){return!l.includes(String(t.id))})))||[])}else{w([])}},checked:b.length===((O===null||O===void 0?void 0:O.filter((function(t){return!l.includes(String(t.id))})))||[]).length,isIndeterminate:b.length>0&&b.length<((O===null||O===void 0?void 0:O.filter((function(t){return!l.includes(String(t.id))})))||[]).length}):"#"),Cell:function t(e){return(0,a.tZ)("div",{css:F.c.caption()},(0,a.tZ)(Ht.Z,{onChange:function t(r){if(r){w([].concat($t(b),[e]))}else{w(b.filter((function(t){return t.id!==e.id})))}},checked:b.map((function(t){return t.id})).includes(e.id)&&!l.includes(String(e.id))}))}},{Header:(0,a.tZ)("div",{css:le.tableLabel},(0,u.__)("Title","tutor")),Cell:function t(e){return(0,a.tZ)("div",{css:le.title},e.title)}},{Header:(0,a.tZ)("div",{css:le.tableLabel},(0,u.__)("Content Type","tutor")),Cell:function t(e){return(0,a.tZ)("div",{css:F.c.caption()},e.content_type)}},{Header:(0,a.tZ)("div",{css:le.tableLabel},(0,u.__)("Created At","tutor")),Cell:function t(e){return(0,a.tZ)("div",{css:F.c.caption()},(0,vt["default"])(new Date(e.updated_at),J.E_.yearMonthDayHourMinuteSecond))}}];(0,s.useEffect)((function(){document.body.style.overflow="hidden";return function(){document.body.style.overflow="auto"}}),[]);return(0,a.tZ)(yt.Z,{title:b.length>0?(0,u.sprintf)((0,u.__)("%s  selected","tutor"),b.length):r,onClose:function t(){return n({action:"CLOSE"})},maxWidth:920},(0,a.tZ)("div",{css:le.searchWrapper},(0,a.tZ)(g.Qr,{control:f.control,name:"search",render:function t(e){return(0,a.tZ)(A.Z,Yt({},e,{placeholder:(0,u.__)("Search by title","tutor"),showVerticalBar:false,content:(0,a.tZ)(p.Z,{name:"search",width:24,height:24})}))}})),(0,a.tZ)("div",{css:le.tableWrapper},(0,a.tZ)(ht.Z,{columns:E,data:O||[],loading:h.isLoading||y.isLoading})),(0,a.tZ)(Z.Z,{when:O===null||O===void 0?void 0:O.length},(0,a.tZ)("div",{css:le.footer},(0,a.tZ)(d.Z,{size:"small",variant:"text",onClick:function t(){return n({action:"CLOSE"})}},(0,u.__)("Cancel","tutor")),(0,a.tZ)(d.Z,{type:"submit",size:"small",variant:"primary",onClick:function t(){i(b);n({action:"CONFIRM"})},disabled:!b.length},(0,u.__)("Add","tutor")))))};const ce=se;var le={searchWrapper:(0,a.iv)("display:flex;padding:",Q.W0[20],";"+(true?"":0),true?"":0),tableWrapper:(0,a.iv)("max-height:calc(100vh - 350px);overflow:auto;tr{td:first-of-type{padding-left:",Q.W0[20],";}td:last-of-type{padding-right:",Q.W0[20],";}}"+(true?"":0),true?"":0),tableLabel:(0,a.iv)(F.c.body("medium"),";text-align:left;color:",Q.Jv.text.primary,";&[data-index]{padding-left:",Q.W0[4],";}"+(true?"":0),true?"":0),title:(0,a.iv)(pt.i.text.ellipsis(2)," width:100%;text-align:left;",F.c.caption(),";min-width:340px;max-width:400px;"+(true?"":0),true?"":0),footer:(0,a.iv)("box-shadow:",Q.AF.dividerTop,";height:56px;display:flex;align-items:center;justify-content:end;gap:",Q.W0[16],";padding-inline:",Q.W0[16],";"+(true?"":0),true?"":0)};var de=r(7363);function fe(t){"@babel/helpers - typeof";return fe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fe(t)}function pe(){pe=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return pe.apply(this,arguments)}function ve(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ve=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function t(e,r,n){return e[r]=n}}function c(t,e,r,i){var o=e&&e.prototype instanceof f?e:f,a=Object.create(o.prototype),u=new E(i||[]);return n(a,"_invoke",{value:w(t,r,u)}),a}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};s(h,o,(function(){return this}));var y=Object.getPrototypeOf,m=y&&y(y(k([])));m&&m!==e&&r.call(m,o)&&(h=m);var g=v.prototype=f.prototype=Object.create(h);function _(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function b(t,e){function i(n,o,a,u){var s=l(t[n],t,o);if("throw"!==s.type){var c=s.arg,d=c.value;return d&&"object"==fe(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){i("next",t,a,u)}),(function(t){i("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return i("throw",t,a,u)}))}u(s.arg)}var o;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){i(r,n,t,e)}))}return o=o?o.then(a,a):a()}})}function w(t,e,r){var n="suspendedStart";return function(i,o){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===i)throw o;return q()}for(r.method=i,r.arg=o;;){var a=r.delegate;if(a){var u=Z(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var s=l(t,e,r);if("normal"===s.type){if(n=r.done?"completed":"suspendedYield",s.arg===d)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(n="completed",r.method="throw",r.arg=s.arg)}}}function Z(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,Z(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var i=l(n,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,d;var o=i.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function x(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(x,this),this.reset(!0)}function k(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return i.next=i}}return{next:q}}function q(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=s(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,s(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},_(b.prototype),s(b.prototype,a,(function(){return this})),t.AsyncIterator=b,t.async=function(e,r,n,i,o){void 0===o&&(o=Promise);var a=new b(c(e,r,n,i),o);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},_(g),s(g,u,"Generator"),s(g,o,(function(){return this})),s(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,E.prototype={constructor:E,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function i(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],u=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(s&&c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function t(e,n){for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var i=n.completion;if("throw"===i.type){var o=i.arg;O(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function he(t,e,r,n,i,o,a){try{var u=t[o](a);var s=u.value}catch(t){r(t);return}if(u.done){e(s)}else{Promise.resolve(s).then(n,i)}}function ye(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var o=t.apply(e,r);function a(t){he(o,n,i,a,u,"next",t)}function u(t){he(o,n,i,a,u,"throw",t)}a(undefined)}))}}function me(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ge(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?me(Object(r),!0).forEach((function(e){_e(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):me(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function _e(t,e,r){e=be(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function be(t){var e=we(t,"string");return fe(e)==="symbol"?e:String(e)}function we(t,e){if(fe(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(fe(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Ze(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var xe=(0,N.zs)();var Oe=function t(e){var r,n,i,o;var c=e.lessonId,l=c===void 0?"":c,v=e.topicId,h=e.closeModal,y=e.icon,b=e.title,x=e.subtitle,O=e.contentDripType;var E=!!U.y.tutor_pro_url;var k=((r=U.y.settings)===null||r===void 0?void 0:r.chatgpt_enable)==="on";var q=U.y.enable_lesson_classic_editor==="1";var T=((n=U.y.settings)===null||n===void 0?void 0:n.hide_admin_bar_for_users)==="off";var N=(i=U.y.current_user.roles)===null||i===void 0?void 0:i.includes(J.er.ADMINISTRATOR);var Q=(o=U.y.current_user.roles)===null||o===void 0?void 0:o.includes(J.er.TUTOR_INSTRUCTOR);var F=q&&(N||Q&&T);var R=(0,z.yO)(l,v);var V=(0,z.G4)(xe);var H=(0,m.NL)();var K=(0,w.d)(),Y=K.showModal;var $=R.data,X=R.isLoading;var tt=H.getQueryData(["Topic",xe]);var et=(0,W.l)(),rt=et.fields;var nt=(0,M.O)({defaultValues:{title:"",description:"",thumbnail:null,tutor_attachments:[],lesson_preview:false,video:null,duration:{hour:0,minute:0,second:0},content_drip_settings:{unlock_date:"",after_xdays_of_enroll:"",prerequisites:[]}},shouldFocusError:true,mode:"onChange"});var it=nt.formState.dirtyFields&&Object.keys(nt.formState.dirtyFields).length>0;(0,s.useEffect)((function(){if($&&!X){var t,e,r,n,i,o;nt.reset(ge({title:$.post_title||"",description:(0,B.qH)($.post_content)||"",thumbnail:{id:$.thumbnail_id?Number($.thumbnail_id):0,title:"",url:$.thumbnail||""},tutor_attachments:$.attachments||[],lesson_preview:$.is_preview||false,video:$.video||null,duration:{hour:((t=$.video.runtime)===null||t===void 0?void 0:t.hours)||0,minute:((e=$.video.runtime)===null||e===void 0?void 0:e.minutes)||0,second:((r=$.video.runtime)===null||r===void 0?void 0:r.seconds)||0},content_drip_settings:{unlock_date:($===null||$===void 0?void 0:(n=$.content_drip_settings)===null||n===void 0?void 0:n.unlock_date)||"",after_xdays_of_enroll:($===null||$===void 0?void 0:(i=$.content_drip_settings)===null||i===void 0?void 0:i.after_xdays_of_enroll)||"",prerequisites:($===null||$===void 0?void 0:(o=$.content_drip_settings)===null||o===void 0?void 0:o.prerequisites)||[]}},Object.fromEntries((0,B.hk)({fields:rt.Curriculum.Lesson}).map((function(t){return[t,$[t]]})))))}var a=document.querySelector(".button.insert-media.add_media");var u=document.querySelector(".add-h5p-content-button");if(a&&u){a.after(u)}var s=setTimeout((function(){nt.setFocus("title")}),0);return function(){clearTimeout(s)}}),[$,X]);var ot=function(){var t=ye(ve().mark((function t(e){var r,n;return ve().wrap((function t(i){while(1)switch(i.prev=i.next){case 0:r=(0,z.SF)(e,l,v,O,(0,B.hk)({fields:rt.Curriculum.Lesson}));i.next=3;return V.mutateAsync(r);case 3:n=i.sent;if(n.data){h({action:"CONFIRM"})}case 5:case"end":return i.stop()}}),t)})));return function e(r){return t.apply(this,arguments)}}();var at=function t(){var e=nt.getValues("description");var r=e.match(/\[h5p id="(\d+)"\]/g)||[];var n=r.map((function(t){var e;var r=((e=t.match(/\[h5p id="(\d+)"\]/))===null||e===void 0?void 0:e[1])||"";return String(r)}));var i=function t(r){var n=function t(e){return'[h5p id="'.concat(e.id,'"]')};var i=r.map(n);nt.setValue("description","".concat(e,"\n").concat(i.join("\n")),{shouldDirty:true})};Y({component:ce,props:{title:(0,u.__)("Select H5P Content","tutor"),onAddContent:i,contentType:"lesson",addedContentIds:n}})};return(0,a.tZ)(D.Z,{onClose:function t(){return h({action:"CLOSE"})},icon:it?(0,a.tZ)(p.Z,{name:"warning",width:24,height:24}):y,title:it?J.iM.isAboveDesktop?(0,u.__)("Unsaved Changes","tutor"):"":b,subtitle:J.iM.isAboveSmallMobile?x:"",maxWidth:1070,actions:it&&(0,a.tZ)(de.Fragment,null,(0,a.tZ)(d.Z,{variant:"text",size:"small",onClick:function t(){if(l){nt.reset()}else{h({action:"CLOSE"})}}},l?(0,u.__)("Discard Changes","tutor"):(0,u.__)("Cancel","tutor")),(0,a.tZ)(d.Z,{loading:V.isPending,variant:"primary",size:"small",onClick:nt.handleSubmit(ot)},l?(0,u.__)("Update","tutor"):(0,u.__)("Save","tutor")))},(0,a.tZ)("div",{css:ke.wrapper},(0,a.tZ)(Z.Z,{when:!R.isLoading,fallback:(0,a.tZ)(f.fz,null)},(0,a.tZ)("div",null,(0,a.tZ)("div",{css:ke.lessonInfo},(0,a.tZ)(g.Qr,{name:"title",control:nt.control,rules:ge({required:(0,u.__)("Lesson title is required.","tutor")},(0,G.T9)(255)),render:function t(e){return(0,a.tZ)(P.Z,pe({},e,{label:(0,u.__)("Name","tutor"),placeholder:(0,u.__)("Enter Lesson Name","tutor"),generateWithAi:!E||k,selectOnFocus:true,isClearable:true}))}}),(0,a.tZ)("div",{css:ke.description},(0,a.tZ)(g.Qr,{name:"description",control:nt.control,render:function t(e){return(0,a.tZ)(I.Z,pe({},e,{label:(0,a.tZ)("div",{css:ke.descriptionLabel},(0,u.__)("Content","tutor"),(0,a.tZ)(Z.Z,{when:F},(0,a.tZ)(ct.Z,{content:(0,u.__)("Save the lesson first to use the WP Editor.","tutor"),delay:200,disabled:!!l},(0,a.tZ)(d.Z,{variant:"text",size:"small",onClick:function t(){window.open("".concat(U.y.site_url,"/wp-admin/post.php?post=").concat(l,"&action=edit"),"_blank","noopener")},icon:(0,a.tZ)(p.Z,{name:"edit",width:24,height:24}),buttonCss:ke.wpEditorButton,disabled:!l},(0,u.__)("WP Editor","tutor"))))),placeholder:(0,u.__)("Enter Lesson Description","tutor"),generateWithAi:!E||k}))}}),(0,a.tZ)(Z.Z,{when:E&&(0,B.ro)(J.AO.H5P_INTEGRATION),fallback:(0,a.tZ)(Z.Z,{when:!E},(0,a.tZ)("button",{css:ke.addH5PContentButton({isPro:false}),type:"reset",className:"add-h5p-content-button",disabled:true},(0,a.tZ)(_.Z,null,(0,a.tZ)("div",{"data-button-text":true},(0,u.__)("Add H5P Content","tutor")))))},(0,a.tZ)("button",{className:"add-h5p-content-button",css:ke.addH5PContentButton,type:"button",onClick:at},(0,u.__)("Add H5P Content","tutor")))),(0,a.tZ)(L.Z,{section:"Curriculum.Lesson.after_description",form:nt}))),(0,a.tZ)("div",{css:ke.rightPanel},(0,a.tZ)(g.Qr,{name:"thumbnail",control:nt.control,render:function t(e){return(0,a.tZ)(lt.Z,pe({},e,{label:(0,u.__)("Featured Image","tutor"),buttonText:(0,u.__)("Upload Image","tutor"),infoText:(0,u.sprintf)((0,u.__)("JPEG, PNG, GIF, and WebP formats, up to %s","tutor"),U.y.max_upload_size)}))}}),(0,a.tZ)(g.Qr,{name:"video",control:nt.control,render:function t(e){return(0,a.tZ)(ft.Z,pe({},e,{label:(0,u.__)("Video","tutor"),buttonText:(0,u.__)("Upload Video","tutor"),infoText:(0,u.sprintf)((0,u.__)("MP4, and WebM formats, up to %s","tutor"),U.y.max_upload_size),onGetDuration:function t(e){nt.setValue("duration.hour",e.hours);nt.setValue("duration.minute",e.minutes);nt.setValue("duration.second",e.seconds)}}))}}),(0,a.tZ)("div",{css:ke.durationWrapper},(0,a.tZ)("span",{css:ke.additionLabel},(0,u.__)("Video Playback Time","tutor")),(0,a.tZ)("div",{css:ke.duration},(0,a.tZ)(g.Qr,{name:"duration.hour",control:nt.control,render:function t(e){return(0,a.tZ)(A.Z,pe({},e,{type:"number",content:(0,a.tZ)("span",{css:ke.durationContent},(0,u.__)("hour","tutor")),contentPosition:"right",placeholder:"0",showVerticalBar:false}))}}),(0,a.tZ)(g.Qr,{name:"duration.minute",control:nt.control,render:function t(e){return(0,a.tZ)(A.Z,pe({},e,{type:"number",content:(0,a.tZ)("span",{css:ke.durationContent},(0,u.__)("min","tutor")),contentPosition:"right",placeholder:"0",showVerticalBar:false}))}}),(0,a.tZ)(g.Qr,{name:"duration.second",control:nt.control,render:function t(e){return(0,a.tZ)(A.Z,pe({},e,{type:"number",content:(0,a.tZ)("span",{css:ke.durationContent},(0,u.__)("sec","tutor")),contentPosition:"right",placeholder:"0",showVerticalBar:false}))}}))),(0,a.tZ)(Z.Z,{when:(0,B.ro)(J.AO.CONTENT_DRIP)},(0,a.tZ)(Z.Z,{when:O==="specific_days"},(0,a.tZ)(g.Qr,{name:"content_drip_settings.after_xdays_of_enroll",control:nt.control,render:function t(e){return(0,a.tZ)(P.Z,pe({},e,{type:"number",label:(0,a.tZ)("div",{css:ke.contentDripLabel},(0,a.tZ)(p.Z,{name:"contentDrip",height:24,width:24}),(0,u.__)("Available after days","tutor")),helpText:(0,u.__)("This lesson will be available after the given number of days.","tutor"),placeholder:"0",selectOnFocus:true}))}})),(0,a.tZ)(Z.Z,{when:O==="unlock_by_date"},(0,a.tZ)(g.Qr,{name:"content_drip_settings.unlock_date",control:nt.control,render:function t(e){return(0,a.tZ)(S.Z,pe({},e,{label:(0,a.tZ)("div",{css:ke.contentDripLabel},(0,a.tZ)(p.Z,{name:"contentDrip",height:24,width:24}),(0,u.__)("Unlock Date","tutor")),placeholder:(0,u.__)("Select Unlock Date","tutor"),helpText:(0,u.__)("This lesson will be available from the given date. Leave empty to make it available immediately.","tutor")}))}})),(0,a.tZ)(Z.Z,{when:O==="after_finishing_prerequisites"},(0,a.tZ)(g.Qr,{name:"content_drip_settings.prerequisites",control:nt.control,render:function t(e){return(0,a.tZ)(C.Z,pe({},e,{label:(0,a.tZ)("div",{css:ke.contentDripLabel},(0,a.tZ)(p.Z,{name:"contentDrip",height:24,width:24}),(0,u.__)("Prerequisites","tutor")),placeholder:(0,u.__)("Select Prerequisite","tutor"),options:tt.reduce((function(t,e){if(e.id===v){t.push(ge(ge({},e),{},{contents:e.contents.filter((function(t){return t.ID!==l}))}))}else{t.push(e)}return t}),[])||[],isSearchable:true,helpText:(0,u.__)("Select items that should be complete before this item","tutor")}))}}))),(0,a.tZ)(g.Qr,{name:"tutor_attachments",control:nt.control,render:function t(e){return(0,a.tZ)(j.Z,pe({},e,{label:(0,u.__)("Exercise Files","tutor"),buttonText:(0,u.__)("Upload Attachment","tutor"),selectMultiple:true}))}}),(0,a.tZ)(Z.Z,{when:!E||(0,B.ro)(J.AO.TUTOR_COURSE_PREVIEW)},(0,a.tZ)("div",{css:ke.lessonPreview},(0,a.tZ)(g.Qr,{name:"lesson_preview",control:nt.control,render:function t(e){return(0,a.tZ)(dt.Z,pe({},e,{disabled:!E,label:(0,a.tZ)("div",{css:ke.previewLabel},(0,u.__)("Lesson Preview","tutor"),!E&&(0,a.tZ)(_.Z,{size:"small",content:(0,u.__)("Pro","tutor")})),helpText:(0,u.__)("If checked, any user/guest can view this lesson without enrolling in the course.","tutor")}))}}),(0,a.tZ)(Z.Z,{when:nt.watch("lesson_preview")},(0,a.tZ)("div",{css:ke.previewInfo},(0,u.__)("This lesson is now available for preview. Users and guests can view it without enrolling in the course.","tutor"))))),(0,a.tZ)(L.Z,{section:"Curriculum.Lesson.bottom_of_sidebar",form:nt})))))};const Ee=Oe;var ke={wrapper:(0,a.iv)("margin:0 auto;display:grid;grid-template-columns:1fr 338px;height:100%;width:100%;padding-inline:",Q.W0[32],";",Q.Uo.smallTablet,"{grid-template-columns:1fr;padding-inline:",Q.W0[24],";}",Q.Uo.mobile,"{padding-inline:",Q.W0[16],";}"+(true?"":0),true?"":0),lessonInfo:(0,a.iv)("padding-block:",Q.W0[20],";padding-right:",Q.W0[32],";display:flex;flex-direction:column;gap:",Q.W0[24],";position:sticky;top:0;z-index:",Q.W5.positive,";",Q.Uo.smallTablet,"{position:unset;padding-right:0;}"+(true?"":0),true?"":0),description:true?{name:"bjn8wh",styles:"position:relative"}:0,descriptionLabel:true?{name:"188e2n4",styles:"display:flex;align-items:center;justify-content:space-between;height:32px"}:0,addH5PContentButton:function t(e){var r=e.isPro,n=r===void 0?true:r;return(0,a.iv)("text-decoration:none;font-size:13px;line-height:2.15384615;min-height:30px;margin:0;padding:",!n?"0px":"0 10px",";cursor:pointer;border:1px solid #3e64de;border-radius:3px;white-space:nowrap;box-sizing:border-box;color:#3e64de;border-color:#3e64de;background:transparent;[data-button-text]{",pt.i.flexCenter(),";padding:0 10px;}:hover:not(:disabled){background:",Q.Jv.background.white,";color:#3e64de;}:disabled{cursor:not-allowed;position:relative;top:auto;left:auto;opacity:0.5;}"+(true?"":0),true?"":0)},rightPanel:(0,a.iv)("border-left:1px solid ",Q.Jv.stroke.divider,";display:flex;flex-direction:column;gap:",Q.W0[16],";padding-block:",Q.W0[20],";padding-left:",Q.W0[32],";",Q.Uo.smallTablet,"{border-left:none;padding-left:0;}"+(true?"":0),true?"":0),durationWrapper:(0,a.iv)("display:flex;flex-direction:column;gap:",Q.W0[8],";"+(true?"":0),true?"":0),duration:(0,a.iv)("display:flex;align-items:flex-end;gap:",Q.W0[8],";"+(true?"":0),true?"":0),durationContent:(0,a.iv)(F.c.small(),";color:",Q.Jv.text.hints,";"+(true?"":0),true?"":0),additionLabel:(0,a.iv)(F.c.body()," color:",Q.Jv.text.title,";"+(true?"":0),true?"":0),lessonPreview:(0,a.iv)("background-color:",Q.Jv.background.white,";padding:",Q.W0[12],";border:1px solid ",Q.Jv.stroke["default"],";border-radius:",Q.E0[8],";"+(true?"":0),true?"":0),previewLabel:(0,a.iv)("display:flex;align-items:center;gap:",Q.W0[4],";"+(true?"":0),true?"":0),contentDripLabel:(0,a.iv)("display:flex;align-items:center;svg{margin-right:",Q.W0[4],";color:",Q.Jv.icon.success,";}"+(true?"":0),true?"":0),previewInfo:(0,a.iv)(F.c.small(),";text-align:center;color:",Q.Jv.text.title,";padding:",Q.W0[8]," ",Q.W0[24],";background:",Q.Jv.background.status.success,";border-radius:",Q.E0[4],";margin-top:",Q.W0[12],";"+(true?"":0),true?"":0),wpEditorButton:(0,a.iv)("margin-left:",Q.W0[4],";color:",Q.Jv.text.brand,";svg{color:",Q.Jv.icon.brand,";}&:hover:not(:disabled){text-decoration:underline;}"+(true?"":0),true?"":0)};var qe=r(8898);var Se=r(1933);var je=r(6895);function Pe(t,e){return De(t)||Ie(t,e)||Te(t,e)||Ae()}function Ae(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Te(t,e){if(!t)return;if(typeof t==="string")return Ce(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ce(t,e)}function Ce(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Ie(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],s=!0,c=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,i=t}finally{try{if(!s&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw i}}return u}}function De(t){if(Array.isArray(t))return t}var Le=(0,s.createContext)(null);var We=function t(){var e=(0,s.useContext)(Le);if(!e){throw new Error("useQuizModalContext must be used within a QuizModalContextProvider")}return e};var ze=function t(e){var r=e.children,n=e.quizId,i=e.contentType,o=e.validationError;var u=(0,s.useState)(""),c=Pe(u,2),l=c[0],d=c[1];var f=(0,g.Gc)();var p=f.watch("questions")||[];var v=(0,s.useRef)(p);var h=(0,s.useState)(o||null),y=Pe(h,2),m=y[0],_=y[1];var b=p.findIndex((function(t){return t.question_id===l}));(0,s.useEffect)((function(){if(p.length===0){d("")}else if(v.current.length!==0&&v.current.length<p.length){var t=p.find((function(t){return!v.current.some((function(e){return String(e.question_id)===String(t.question_id)}))}));d((t===null||t===void 0?void 0:t.question_id)||"")}else if(b===-1){d(p[0].question_id)}v.current=p}),[p.length]);(0,s.useEffect)((function(){if(b===-1&&l){d("")}}),[b,l]);return(0,a.tZ)(Le.Provider,{value:{activeQuestionIndex:b,activeQuestionId:l,setActiveQuestionId:d,quizId:n,validationError:m,setValidationError:_,contentType:i}},typeof r==="function"?r({activeQuestionIndex:b,activeQuestionId:l,setActiveQuestionId:d,setValidationError:_}):r)};function Ne(t){"@babel/helpers - typeof";return Ne="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ne(t)}function Ue(){Ue=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Ue.apply(this,arguments)}function Je(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Qe(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Je(Object(r),!0).forEach((function(e){Fe(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Je(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Fe(t,e,r){e=Me(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Me(t){var e=Be(t,"string");return Ne(e)==="symbol"?e:String(e)}function Be(t,e){if(Ne(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Ne(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Ge(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Re={true_false:{label:(0,u.__)("True/False","tutor"),icon:"quizTrueFalse"},multiple_choice:{label:(0,u.__)("Multiple Choice","tutor"),icon:"quizMultiChoice"},open_ended:{label:(0,u.__)("Open Ended/ Essay","tutor"),icon:"quizEssay"},fill_in_the_blank:{label:(0,u.__)("Fill in the Blanks","tutor"),icon:"quizFillInTheBlanks"},short_answer:{label:(0,u.__)("Short Answer","tutor"),icon:"quizShortAnswer"},matching:{label:(0,u.__)("Matching","tutor"),icon:"quizImageMatching"},image_answering:{label:(0,u.__)("Image Answering","tutor"),icon:"quizImageAnswer"},ordering:{label:(0,u.__)("Ordering","tutor"),icon:"quizOrdering"},h5p:{label:(0,u.__)("H5P","tutor"),icon:"quizTrueFalse"}};var Ve=["multiple_choice","matching","image_answering","ordering"];var He=true?{name:"z33n32",styles:"max-width:80px"}:0;var Ke=function t(){var e=We(),r=e.activeQuestionIndex,n=e.activeQuestionId,i=e.validationError,o=e.setValidationError;var s=(0,g.Gc)();var c=s.watch("questions.".concat(r,".question_type"));var l=s.watch("questions.".concat(r,"._data_status"));if(!n){return(0,a.tZ)("p",{css:$e.emptyQuestions},(0,u.__)("Create/Select a question to view details","tutor"))}return(0,a.tZ)("div",{key:"".concat(n,"-").concat(r)},(0,a.tZ)("div",{css:$e.questionTypeWrapper},(0,a.tZ)("div",{css:F.c.caption("medium")},(0,u.__)("Question Type","tutor")),(0,a.tZ)("div",{css:$e.questionType},(0,a.tZ)(p.Z,{name:c?Re[c].icon:"quizTrueFalse",width:32,height:32}),(0,a.tZ)("span",null,c?Re[c].label:""))),(0,a.tZ)("div",{css:$e.conditions},(0,a.tZ)("p",null,(0,u.__)("Conditions:","tutor")),(0,a.tZ)("div",{css:$e.conditionControls},(0,a.tZ)(Z.Z,{when:c==="multiple_choice"},(0,a.tZ)(g.Qr,{control:s.control,name:"questions.".concat(r,".question_settings.has_multiple_correct_answer"),render:function t(e){return(0,a.tZ)(dt.Z,Ue({},e,{label:(0,u.__)("Multiple Correct Answer","tutor"),onChange:function t(e){if(Qt(l,Pt.UPDATE)){s.setValue("questions.".concat(r,"._data_status"),Qt(l,Pt.UPDATE))}if(!e){s.setValue("questions.".concat(r,".question_answers"),s.getValues("questions.".concat(r,".question_answers")).map((function(t){return Qe(Qe({},t),{},{is_correct:"0"})})))}}}))}})),(0,a.tZ)(Z.Z,{when:c==="matching"},(0,a.tZ)(g.Qr,{control:s.control,name:"questions.".concat(r,".question_settings.is_image_matching"),render:function t(e){return(0,a.tZ)(dt.Z,Ue({},e,{label:(0,u.__)("Image Matching","tutor"),onChange:function t(e){if(Qt(l,Pt.UPDATE)){s.setValue("questions.".concat(r,"._data_status"),Qt(l,Pt.UPDATE))}if((i===null||i===void 0?void 0:i.type)==="question"&&!e){o(null)}}}))}})),(0,a.tZ)(g.Qr,{control:s.control,name:"questions.".concat(r,".question_settings.answer_required"),render:function t(e){return(0,a.tZ)(dt.Z,Ue({},e,{label:(0,u.__)("Answer Required","tutor"),onChange:function t(){if(Qt(l,Pt.UPDATE)){s.setValue("questions.".concat(r,"._data_status"),Qt(l,Pt.UPDATE))}}}))}}),(0,a.tZ)(Z.Z,{when:Ve.includes(c)},(0,a.tZ)(g.Qr,{control:s.control,name:"questions.".concat(r,".question_settings.randomize_question"),render:function t(e){return(0,a.tZ)(dt.Z,Ue({},e,{label:(0,u.__)("Randomize Choice","tutor"),onChange:function t(){if(Qt(l,Pt.UPDATE)){s.setValue("questions.".concat(r,"._data_status"),Qt(l,Pt.UPDATE))}}}))}})),(0,a.tZ)(g.Qr,{control:s.control,name:"questions.".concat(r,".question_settings.question_mark"),rules:{min:0},render:function t(e){return(0,a.tZ)(P.Z,Ue({},e,{label:(0,u.__)("Point For This Question","tutor"),type:"number",isInlineLabel:true,placeholder:"0",selectOnFocus:true,style:He,onChange:function t(){if(Qt(l,Pt.UPDATE)){s.setValue("questions.".concat(r,"._data_status"),Qt(l,Pt.UPDATE))}}}))}}),(0,a.tZ)(g.Qr,{control:s.control,name:"questions.".concat(r,".question_settings.show_question_mark"),render:function t(e){return(0,a.tZ)(dt.Z,Ue({},e,{label:(0,u.__)("Display Points","tutor"),onChange:function t(){if(Qt(l,Pt.UPDATE)){s.setValue("questions.".concat(r,"._data_status"),Qt(l,Pt.UPDATE))}}}))}}),(0,a.tZ)(L.Z,{section:"Curriculum.Quiz.bottom_of_question_sidebar",namePrefix:"questions.".concat(r,"."),form:s}))))};const Ye=Ke;var $e={emptyQuestions:(0,a.iv)("padding:",Q.W0[12]," ",Q.W0[32]," ",Q.W0[24]," ",Q.W0[24],";",F.c.caption("medium"),";"+(true?"":0),true?"":0),questionTypeWrapper:(0,a.iv)(pt.i.display.flex("column"),";padding:",Q.W0[8]," ",Q.W0[32]," ",Q.W0[24]," ",Q.W0[24],";gap:",Q.W0[10],";border-bottom:1px solid ",Q.Jv.stroke.divider,";"+(true?"":0),true?"":0),questionType:(0,a.iv)("display:flex;align-items:center;gap:",Q.W0[10],";"+(true?"":0),true?"":0),conditions:(0,a.iv)("padding:",Q.W0[8]," ",Q.W0[32]," ",Q.W0[24]," ",Q.W0[24],";p{",F.c.body("medium"),";color:",Q.Jv.text.primary,";}"+(true?"":0),true?"":0),conditionControls:(0,a.iv)(pt.i.display.flex("column"),";gap:",Q.W0[16],";margin-top:",Q.W0[16],";"+(true?"":0),true?"":0)};var Xe=r(4063);function tr(t,e){return or(t)||ir(t,e)||rr(t,e)||er()}function er(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function rr(t,e){if(!t)return;if(typeof t==="string")return nr(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nr(t,e)}function nr(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function ir(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],s=!0,c=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,i=t}finally{try{if(!s&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw i}}return u}}function or(t){if(Array.isArray(t))return t}function ar(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var ur=function t(e){var r;var n=e.label,i=e.field,o=e.fieldState,c=e.disabled,l=e.loading,f=e.placeholder,p=e.helpText,v=e.onChange;var h=(r=i.value)!==null&&r!==void 0?r:"";var y=(0,s.useState)(false),m=tr(y,2),g=m[0],_=m[1];var b=(0,s.useState)(h),w=tr(b,2),x=w[0],O=w[1];return(0,a.tZ)("div",{css:vr.wrapper({hasValue:!!h&&!g})},(0,a.tZ)(Z.Z,{when:g||h},(0,a.tZ)("label",{css:vr.answerLabel},n)),(0,a.tZ)("div",{css:vr.editorWrapper({isEdit:g})},(0,a.tZ)("div",{css:vr.container({isEdit:g||!!h})},(0,a.tZ)(Z.Z,{when:!h&&!g,fallback:(0,a.tZ)(I.Z,{field:i,fieldState:o,disabled:c,helpText:p,loading:l,readOnly:!g,onChange:v,placeholder:f,autoFocus:true,isMinimal:true})},(0,a.tZ)("div",{css:vr.placeholder},f)),(0,a.tZ)(Z.Z,{when:g},(0,a.tZ)("div",{"data-action-buttons":true,css:vr.actionButtonWrapper({isEdit:g})},(0,a.tZ)(d.Z,{variant:"text",size:"small",onClick:function t(){i.onChange(x);_(false)}},(0,u.__)("Cancel","tutor")),(0,a.tZ)(d.Z,{variant:"secondary",size:"small",onClick:function t(){var e;O((e=i.value)!==null&&e!==void 0?e:"");_(false)},disabled:i.value===x},(0,u.__)("Ok","tutor")))),(0,a.tZ)(Z.Z,{when:!g},(0,a.tZ)("div",{onClick:function t(){if(!g&&!c){_(true)}},onKeyDown:function t(e){if((e.key==="Enter"||e.key===" ")&&!g){_(true)}},"data-overlay":true,tabIndex:0,role:"button"})))))};const sr=ur;var cr=true?{name:"3ix1vd",styles:"opacity:1"}:0;var lr=true?{name:"1dz94pb",styles:"resize:vertical"}:0;var dr=true?{name:"1peqg37",styles:"background-color:transparent"}:0;var fr=true?{name:"1hcx8jb",styles:"padding:0"}:0;var pr=true?{name:"11j83c5",styles:"padding-inline:0;max-height:unset;overflow:unset"}:0;var vr={wrapper:function t(e){var r=e.hasValue;return(0,a.iv)(pt.i.display.flex("column")," gap:",Q.W0[10],";border-radius:",Q.E0.card,";",r&&(0,a.iv)("background-color:",Q.Jv.color.success[30],";padding:",Q.W0[12]," ",Q.W0[24],";&:hover{background-color:",Q.Jv.color.success[40],";}"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},editorWrapper:function t(e){var r=e.isEdit;return(0,a.iv)("position:relative;max-height:400px;overflow-y:scroll;",r&&pr,";"+(true?"":0),true?"":0)},container:function t(e){var r=e.isEdit;return(0,a.iv)("position:relative;display:flex;flex-direction:column;gap:",Q.W0[16],";min-height:48px;height:100%;width:100%;inset:0;border-radius:",Q.E0[6],";transition:background 0.15s ease-in-out;[data-overlay]{position:absolute;inset:0;opacity:1;:focus-visible{outline:2px solid ",Q.Jv.stroke.brand,";outline-offset:-2px;border-radius:",Q.E0.card,";}}& label{",F.c.caption()," margin-bottom:",Q.W0[6],";color:",Q.Jv.text.title,";}",r&&fr," &:hover{background-color:",Q.Jv.background.white,";color:",Q.Jv.text.subdued,";cursor:text;",r&&dr,";[data-action-buttons]{opacity:1;}}"+(true?"":0),true?"":0)},inputContainer:function t(e){return(0,a.iv)("position:relative;display:flex;& textarea{",F.c.caption()," color:",Q.Jv.text.title,";height:auto;padding:",Q.W0[8]," ",Q.W0[12],";resize:none;",e&&lr,";}"+(true?"":0),true?"":0)},clearButton:(0,a.iv)("position:absolute;right:",Q.W0[2],";top:",Q.W0[2],";width:36px;height:36px;border-radius:",Q.E0[2],";background:transparent;button{padding:",Q.W0[10],";}"+(true?"":0),true?"":0),placeholder:(0,a.iv)("padding:",Q.W0[12]," ",Q.W0[24],";",F.c.caption()," color:",Q.Jv.text.hints,";display:flex;align-items:center;height:100%;"+(true?"":0),true?"":0),actionButtonWrapper:function t(e){var r=e.isEdit;return(0,a.iv)("display:flex;justify-content:flex-end;gap:",Q.W0[8],";opacity:0;transition:opacity 0.15s ease-in-out;",r&&cr,";"+(true?"":0),true?"":0)},answer:(0,a.iv)("padding:",Q.W0[12]," ",Q.W0[24],";border-radius:",Q.E0.card,";display:flex;flex-direction:column;gap:",Q.W0[16],";background-color:",Q.Jv.background.success.fill30,";color:",Q.Jv.text.title,";transition:background 0.15s ease-in-out;&:hover{background-color:",Q.Jv.background.success.fill40,";}"+(true?"":0),true?"":0),answerLabel:(0,a.iv)(F.c.caption()," color:",Q.Jv.text.title,";"+(true?"":0),true?"":0),answerParagraph:(0,a.iv)("pre{",pt.i.overflowXAuto,";}"+(true?"":0),true?"":0)};function hr(t,e){return br(t)||_r(t,e)||mr(t,e)||yr()}function yr(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function mr(t,e){if(!t)return;if(typeof t==="string")return gr(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return gr(t,e)}function gr(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function _r(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],s=!0,c=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,i=t}finally{try{if(!s&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw i}}return u}}function br(t){if(Array.isArray(t))return t}function wr(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Zr=!!U.y.tutor_pro_url;var xr=function t(e){var r;var n=e.label,i=e.field,o=e.fieldState,c=e.disabled,l=c===void 0?false:c,f=e.loading,p=e.placeholder,v=e.helpText,h=e.onChange;var y=(r=i.value)!==null&&r!==void 0?r:"";var m=(0,s.useState)(false),g=hr(m,2),_=g[0],b=g[1];var w=(0,s.useState)(y),x=hr(w,2),O=x[0],E=x[1];return(0,a.tZ)("div",{css:Sr.editorWrapper({isEdit:_})},(0,a.tZ)("div",{css:Sr.container({isEdit:_,isDisabled:l})},(0,a.tZ)(Z.Z,{when:!_&&(!Zr||!y),fallback:(0,a.tZ)(Z.Z,{when:Zr,fallback:(0,a.tZ)(qe.Z,{field:i,fieldState:o,label:n,disabled:l,helpText:v,loading:f,readOnly:!_,onChange:h,placeholder:p,autoResize:true,maxHeight:400})},(0,a.tZ)(I.Z,{field:i,fieldState:o,label:n,disabled:l,helpText:v,loading:f,readOnly:!_,onChange:h,placeholder:p,min_height:100,max_height:400,toolbar1:"bold italic underline | bullist numlist | blockquote | alignleft aligncenter alignright | link unlink | ".concat(Zr?" codesample":""," | wp_adv"),toolbar2:"formatselect strikethrough hr wp_more forecolor pastetext removeformat charmap outdent indent undo redo wp_help fullscreen tutor_button undoRedoDropdown"}))},(0,a.tZ)("div",{css:Sr.placeholder,dangerouslySetInnerHTML:{__html:y||p||""}})),(0,a.tZ)(Z.Z,{when:_},(0,a.tZ)("div",{"data-action-buttons":true,css:Sr.actionButtonWrapper({isEdit:_})},(0,a.tZ)(d.Z,{variant:"text",size:"small",onClick:function t(){i.onChange(O);b(false)}},(0,u.__)("Cancel","tutor")),(0,a.tZ)(d.Z,{variant:"secondary",size:"small",onClick:function t(){b(false);E(i.value||"")},disabled:y===O},(0,u.__)("Ok","tutor")))),(0,a.tZ)(Z.Z,{when:!_},(0,a.tZ)("div",{onClick:function t(){if(!_&&!l){b(true)}},onKeyDown:function t(e){if((e.key==="Enter"||e.key===" ")&&!_){b(true)}},"data-overlay":true,tabIndex:0,role:"button"}))))};const Or=xr;var Er=true?{name:"3ix1vd",styles:"opacity:1"}:0;var kr=true?{name:"gv9h2n",styles:"padding-inline:0"}:0;var qr=true?{name:"11j83c5",styles:"padding-inline:0;max-height:unset;overflow:unset"}:0;var Sr={editorWrapper:function t(e){var r=e.isEdit;return(0,a.iv)("position:relative;max-height:400px;overflow-y:auto;border-radius:",Q.E0[6],";",r&&qr,";"+(true?"":0),true?"":0)},container:function t(e){var r=e.isEdit,n=e.isDisabled;return(0,a.iv)("position:relative;display:flex;flex-direction:column;gap:",Q.W0[16],";min-height:64px;height:100%;width:100%;inset:0;padding-inline:",Q.W0[8]," ",Q.W0[16],";border-radius:",Q.E0[6],";transition:background-color 0.15s ease-in-out;[data-overlay]{position:absolute;inset:0;opacity:1;&:focus-visible{outline:2px solid ",Q.Jv.stroke.brand,";outline-offset:-2px;border-radius:",Q.E0.card,";}}& label{",F.c.caption(),";margin-bottom:",Q.W0[6],";color:",Q.Jv.text.title,";}",!n&&(0,a.iv)("&:hover{background-color:",!r&&Q.Jv.background.white,";color:",Q.Jv.text.subdued,";[data-action-buttons]{opacity:1;}}"+(true?"":0),true?"":0)," ",r&&kr,";"+(true?"":0),true?"":0)},placeholder:(0,a.iv)(F.c.caption()," color:",Q.Jv.text.hints,";height:100%;inset:0;padding-block:",Q.W0[8],";overflow-x:hidden;"+(true?"":0),true?"":0),actionButtonWrapper:function t(e){var r=e.isEdit;return(0,a.iv)("display:flex;justify-content:flex-end;gap:",Q.W0[8],";opacity:0;transition:opacity 0.15s ease-in-out;",r&&Er,";"+(true?"":0),true?"":0)},overlay:true?{name:"b98nv4",styles:"position:absolute;inset:0"}:0};var jr=r(8402);var Pr=r(7151);var Ar=r(7363);function Tr(t){"@babel/helpers - typeof";return Tr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Tr(t)}function Cr(){Cr=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Cr.apply(this,arguments)}function Ir(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Dr(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ir(Object(r),!0).forEach((function(e){Lr(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ir(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Lr(t,e,r){e=Wr(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Wr(t){var e=zr(t,"string");return Tr(e)==="symbol"?e:String(e)}function zr(t,e){if(Tr(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Tr(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Nr(t,e){return Mr(t)||Fr(t,e)||Jr(t,e)||Ur()}function Ur(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Jr(t,e){if(!t)return;if(typeof t==="string")return Qr(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Qr(t,e)}function Qr(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Fr(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],s=!0,c=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,i=t}finally{try{if(!s&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw i}}return u}}function Mr(t){if(Array.isArray(t))return t}function Br(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Gr=function t(e){var r;var n=e.maxLimit,i=e.field,o=e.fieldState,u=e.disabled,c=e.readOnly,l=e.loading,f=e.placeholder,v=e.helpText,h=e.onChange,y=e.onKeyDown,m=e.isHidden,g=e.isSecondary,_=g===void 0?false:g,b=e.removeBorder,w=e.dataAttribute,x=e.isInlineLabel,O=x===void 0?false:x,E=e.style,k=e.selectOnFocus,q=k===void 0?false:k;var S=(r=i.value)!==null&&r!==void 0?r:"";var j=(0,s.useRef)(null);var P=(0,s.useState)(false),A=Nr(P,2),T=A[0],C=A[1];var I=(0,s.useState)(S),D=Nr(I,2),L=D[0],W=D[1];var z=undefined;if(n){z={maxLimit:n,inputCharacter:S.toString().length}}var N=Dr({},(0,Pr.$K)(w)&&Lr({},w,true));(0,s.useEffect)((function(){if((0,Pr.$K)(j.current)){j.current.focus();W(S)}}),[T,j.current]);return(0,a.tZ)("div",{role:"button",tabIndex:0,css:Hr.container({isEdit:T,isDisabled:u||false})},(0,a.tZ)(Z.Z,{when:!T},(0,a.tZ)("div",{css:Hr.placeholder,onClick:function t(){return!u&&C(true)},onKeyDown:function t(e){if(e.key==="Enter"||e.key===" "){C(true)}}},i.value||f)),(0,a.tZ)(Z.Z,{when:T},(0,a.tZ)(jr.Z,{field:i,fieldState:o,disabled:u,readOnly:c,loading:l,helpText:v,isHidden:m,characterCount:z,isSecondary:_,removeBorder:b,isInlineLabel:O,inputStyle:E},(function(t){return(0,a.tZ)(Ar.Fragment,null,(0,a.tZ)("div",{css:Hr.inputContainer(false)},(0,a.tZ)("input",Cr({},i,t,N,{className:"tutor-input-field",type:"text",ref:j,value:S,onChange:function t(e){var r=e.target.value;i.onChange(r);if(h){h(r)}},onKeyDown:function t(e){if(e.key==="Enter"){C(false)}if(e.key==="Escape"){i.onChange(L);C(false)}y===null||y===void 0?void 0:y(e.key)},onFocus:function t(e){if(q){e.target.select()}},autoComplete:"off"}))))}))),(0,a.tZ)("div",{"data-action-buttons":true,css:Hr.actionButtonWrapper({isEdit:T})},(0,a.tZ)(Z.Z,{when:T,fallback:(0,a.tZ)(d.Z,{buttonCss:Hr.actionButton,variant:"text",size:"small",onClick:function t(){return C(true)}},(0,a.tZ)(p.Z,{name:"edit",height:24,width:24}))},(0,a.tZ)(Ar.Fragment,null,(0,a.tZ)(Z.Z,{when:i.value!==L},(0,a.tZ)(d.Z,{buttonCss:Hr.actionButton,variant:"text",size:"small",onClick:function t(){C(false)},disabled:i.value===L},(0,a.tZ)(p.Z,{name:"checkMark",height:24,width:24}))),(0,a.tZ)(d.Z,{buttonCss:Hr.actionButton,variant:"text",size:"small",onClick:function t(){i.onChange(L);C(false)}},(0,a.tZ)(p.Z,{name:"lineCross",height:24,width:24}))))))};const Rr=Gr;var Vr=true?{name:"3ix1vd",styles:"opacity:1"}:0;var Hr={container:function t(e){var r=e.isEdit,n=e.isDisabled;return(0,a.iv)("position:relative;display:grid;grid-template-columns:1fr auto;align-items:center;gap:",Q.W0[8],";min-height:50px;height:100%;width:100%;cursor:text;padding-inline:",Q.W0[8]," ",Q.W0[16],";padding-block:",Q.W0[8],";border-radius:",Q.E0[6],";transition:box-shadow 0.15s ease-in-out;border:1px solid transparent;",!n&&(0,a.iv)("&:hover{background-color:",Q.Jv.background.white,";color:",Q.Jv.text.subdued,";[data-action-buttons]{opacity:",!n?1:0,";}}&:focus-within{",r&&pt.i.inputFocus," [data-action-buttons]{opacity:1;}}&:focus-visible{outline:2px solid ",Q.Jv.stroke.brand,";}"+(true?"":0),true?"":0)," ",r&&(0,a.iv)("background-color:",Q.Jv.background.white,";color:",Q.Jv.text.subdued,";padding-block:",Q.W0[4],";border:1px solid ",Q.Jv.stroke["default"],";cursor:default;"+(true?"":0),true?"":0)," ",Q.Uo.smallTablet,"{[data-action-buttons]{opacity:1;}}"+(true?"":0),true?"":0)},inputContainer:function t(e){return(0,a.iv)("position:relative;display:flex;transition:background 0.15s ease-in-out;& input{",F.c.heading6()," color:",Q.Jv.text.primary,";border:none;background:none;padding:0;",e&&"padding-right: ".concat(Q.W0[36],";"),";width:100%;&.tutor-input-field{border:none;box-shadow:none;padding-inline:0;&:focus{border:none;box-shadow:none;outline:none;}}}"+(true?"":0),true?"":0)},clearButton:(0,a.iv)("position:absolute;right:",Q.W0[2],";top:",Q.W0[2],";width:36px;height:36px;border-radius:",Q.E0[2],";background:transparent;button{padding:",Q.W0[10],";}"+(true?"":0),true?"":0),placeholder:(0,a.iv)(F.c.heading6()," color:",Q.Jv.text.hints,";border-radius:",Q.E0[6],";"+(true?"":0),true?"":0),actionButtonWrapper:function t(e){var r=e.isEdit;return(0,a.iv)("display:flex;align-items:center;gap:",Q.W0[8],";opacity:0;transition:opacity 0.15s ease-in-out;",r&&Vr,";"+(true?"":0),true?"":0)},actionButton:(0,a.iv)("padding:0;color:",Q.Jv.icon.subdued,";"+(true?"":0),true?"":0)};var Kr=r(7583);function Yr(t){"@babel/helpers - typeof";return Yr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Yr(t)}function $r(){$r=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return $r.apply(this,arguments)}function Xr(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Xr=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function t(e,r,n){return e[r]=n}}function c(t,e,r,i){var o=e&&e.prototype instanceof f?e:f,a=Object.create(o.prototype),u=new E(i||[]);return n(a,"_invoke",{value:w(t,r,u)}),a}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};s(h,o,(function(){return this}));var y=Object.getPrototypeOf,m=y&&y(y(k([])));m&&m!==e&&r.call(m,o)&&(h=m);var g=v.prototype=f.prototype=Object.create(h);function _(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function b(t,e){function i(n,o,a,u){var s=l(t[n],t,o);if("throw"!==s.type){var c=s.arg,d=c.value;return d&&"object"==Yr(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){i("next",t,a,u)}),(function(t){i("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return i("throw",t,a,u)}))}u(s.arg)}var o;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){i(r,n,t,e)}))}return o=o?o.then(a,a):a()}})}function w(t,e,r){var n="suspendedStart";return function(i,o){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===i)throw o;return q()}for(r.method=i,r.arg=o;;){var a=r.delegate;if(a){var u=Z(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var s=l(t,e,r);if("normal"===s.type){if(n=r.done?"completed":"suspendedYield",s.arg===d)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(n="completed",r.method="throw",r.arg=s.arg)}}}function Z(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,Z(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var i=l(n,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,d;var o=i.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function x(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(x,this),this.reset(!0)}function k(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return i.next=i}}return{next:q}}function q(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=s(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,s(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},_(b.prototype),s(b.prototype,a,(function(){return this})),t.AsyncIterator=b,t.async=function(e,r,n,i,o){void 0===o&&(o=Promise);var a=new b(c(e,r,n,i),o);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},_(g),s(g,u,"Generator"),s(g,o,(function(){return this})),s(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,E.prototype={constructor:E,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function i(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],u=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(s&&c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function t(e,n){for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var i=n.completion;if("throw"===i.type){var o=i.arg;O(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function tn(t,e,r,n,i,o,a){try{var u=t[o](a);var s=u.value}catch(t){r(t);return}if(u.done){e(s)}else{Promise.resolve(s).then(n,i)}}function en(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var o=t.apply(e,r);function a(t){tn(o,n,i,a,u,"next",t)}function u(t){tn(o,n,i,a,u,"throw",t)}a(undefined)}))}}function rn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function nn(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rn(Object(r),!0).forEach((function(e){on(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rn(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function on(t,e,r){e=an(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function an(t){var e=un(t,"string");return Yr(e)==="symbol"?e:String(e)}function un(t,e){if(Yr(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Yr(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function sn(t,e){return pn(t)||fn(t,e)||ln(t,e)||cn()}function cn(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function ln(t,e){if(!t)return;if(typeof t==="string")return dn(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return dn(t,e)}function dn(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function fn(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],s=!0,c=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,i=t}finally{try{if(!s&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw i}}return u}}function pn(t){if(Array.isArray(t))return t}function vn(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var hn=function t(e){var r,n,i,o,c;var l=e.field;var f=We(),v=f.activeQuestionId,h=f.validationError,y=f.setValidationError;var m=(r=l.value)!==null&&r!==void 0?r:{_data_status:Pt.NEW,is_saved:false,answer_id:(0,B.x0)(),answer_title:"",belongs_question_id:v,belongs_question_type:"fill_in_the_blank",answer_two_gap_match:"",answer_view_format:"text",answer_order:0,is_correct:"0"};var g=(0,s.useRef)(null);var _=(n=m.answer_two_gap_match)===null||n===void 0?void 0:n.split("|");var b=(0,s.useState)(!m.answer_title||!m.answer_two_gap_match),w=sn(b,2),x=w[0],O=w[1];var E=(0,s.useState)(m),k=sn(E,2),q=k[0],S=k[1];var j=((i=m.answer_title)===null||i===void 0?void 0:(o=i.match(/{dash}/g))===null||o===void 0?void 0:o.length)||0;var P=((c=m.answer_two_gap_match)===null||c===void 0?void 0:c.split("|").length)||0;var A=!!(m.answer_title&&m.answer_two_gap_match&&j!==P);(0,s.useEffect)((function(){if((0,Pr.$K)(g.current)&&x){g.current.focus()}}),[x]);return(0,a.tZ)("div",{css:mn.option},(0,a.tZ)("div",{css:mn.optionLabel({isEditing:x})},(0,a.tZ)("div",{css:mn.optionHeader},(0,a.tZ)("div",{css:mn.optionTitle},(0,u.__)("Fill in the blanks","tutor")),(0,a.tZ)(Z.Z,{when:m.is_saved&&!x},(0,a.tZ)("div",{css:mn.optionActions},(0,a.tZ)(ct.Z,{content:(0,u.__)("Edit","tutor")},(0,a.tZ)("button",{type:"button",css:pt.i.actionButton,"data-edit-button":true,onClick:function t(e){e.stopPropagation();O(true)},"data-visually-hidden":true},(0,a.tZ)(p.Z,{name:"edit",width:24,height:24})))))),(0,a.tZ)("div",{css:mn.optionBody},(0,a.tZ)(Z.Z,{when:x,fallback:(0,a.tZ)("div",{css:mn.placeholderWrapper},(0,a.tZ)("div",{css:mn.optionPlaceholder({isTitle:!!m.answer_title})},m.answer_title?m.answer_title.replace(/{dash}/g,"_____"):(0,u.__)("Question title...","tutor")),(0,a.tZ)("div",{css:mn.optionPlaceholder({isCorrectAnswer:_===null||_===void 0?void 0:_.length})},_&&_.length>0?(0,a.tZ)(Kr.Z,{each:_},(function(t,e){return(0,a.tZ)(s.Fragment,{key:e},t,(0,a.tZ)(Z.Z,{when:e<(_?_.length-1:0)},(0,a.tZ)("span",null,"|")))})):(0,u.__)("Correct Answer(s)...","tutor")))},(0,a.tZ)("div",{css:mn.optionInputWrapper},(0,a.tZ)("div",{css:mn.inputWithHints},(0,a.tZ)("input",$r({},l,{ref:g,type:"text",placeholder:(0,u.__)("Question title...","tutor"),value:m.answer_title,onClick:function t(e){e.stopPropagation()},onChange:function t(e){l.onChange(nn(nn(nn({},m),Qt(m._data_status,Pt.UPDATE)&&{_data_status:Qt(m._data_status,Pt.UPDATE)}),{},{answer_title:e.target.value}))},onKeyDown:function(){var t=en(Xr().mark((function t(e){return Xr().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:e.stopPropagation();if((e.metaKey||e.ctrlKey)&&e.key==="Enter"&&m.answer_title){l.onChange(nn(nn({},m),Qt(m._data_status,Pt.UPDATE)&&{_data_status:Qt(m._data_status,Pt.UPDATE)}));O(false)}case 2:case"end":return r.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()})),(0,a.tZ)("div",{css:mn.inputHints},(0,a.tZ)(p.Z,{name:"info",height:20,width:20}),(0,a.tZ)("p",null,(0,u.__)("Please make sure to use the variable {dash} in your question title to show the blanks in your question. You can use multiple {dash} variables in one question.","tutor")))),(0,a.tZ)("div",{css:mn.inputWithHints},(0,a.tZ)("input",$r({},l,{type:"text",placeholder:(0,u.__)("Correct Answer(s)..."),value:_===null||_===void 0?void 0:_.join("|"),onClick:function t(e){e.stopPropagation()},onChange:function t(e){l.onChange(nn(nn(nn({},m),Qt(m._data_status,Pt.UPDATE)&&{_data_status:Qt(m._data_status,Pt.UPDATE)}),{},{answer_two_gap_match:e.target.value}))},onKeyDown:function(){var t=en(Xr().mark((function t(e){return Xr().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:if((e.metaKey||e.ctrlKey)&&e.key==="Enter"&&m.answer_two_gap_match){l.onChange(nn(nn({},m),Qt(m._data_status,Pt.UPDATE)&&{_data_status:Qt(m._data_status,Pt.UPDATE),is_saved:true}));if((h===null||h===void 0?void 0:h.type)==="save_option"){y(null)}O(false)}case 1:case"end":return r.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()})),(0,a.tZ)(Z.Z,{when:A},(0,a.tZ)("div",{css:mn.errorMessage},(0,a.tZ)(p.Z,{name:"info",height:20,width:20}),(0,a.tZ)("p",null,(0,u.__)("Match the number of answers to the number of blanks {dash} in your question.","tutor")))),(0,a.tZ)("div",{css:mn.inputHints},(0,a.tZ)(p.Z,{name:"info",height:20,width:20}),(0,a.tZ)("p",null,(0,u.__)("Separate multiple answers by a vertical bar |. 1 answer per {dash} variable is defined in the question. Example: Apple | Banana | Orange","tutor")))),(0,a.tZ)("div",{css:mn.optionInputButtons},(0,a.tZ)(Z.Z,{when:m.is_saved},(0,a.tZ)(d.Z,{variant:"text",size:"small",onClick:function t(e){e.stopPropagation();O(false);l.onChange(q)}},(0,u.__)("Cancel","tutor"))),(0,a.tZ)(d.Z,{variant:"secondary",size:"small",onClick:function(){var t=en(Xr().mark((function t(e){return Xr().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:e.stopPropagation();if(!A){r.next=3;break}return r.abrupt("return");case 3:l.onChange(nn(nn({},m),Qt(m._data_status,Pt.UPDATE)&&{_data_status:Qt(m._data_status,Pt.UPDATE),is_saved:true}));S(nn(nn({},m),Qt(m._data_status,Pt.UPDATE)&&{_data_status:Qt(m._data_status,Pt.UPDATE),is_saved:true}));if((h===null||h===void 0?void 0:h.type)==="save_option"){y(null)}O(false);case 7:case"end":return r.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),disabled:!m.answer_title||!m.answer_two_gap_match||A},(0,u.__)("Ok","tutor"))))))))};const yn=hn;var mn={option:(0,a.iv)(pt.i.display.flex(),";",F.c.caption("medium"),";align-items:center;color:",Q.Jv.text.subdued,";gap:",Q.W0[10],";align-items:center;"+(true?"":0),true?"":0),optionLabel:function t(e){var r=e.isEditing;return(0,a.iv)("display:flex;flex-direction:column;gap:",Q.W0[12],";width:100%;border-radius:",Q.E0.card,";padding:",Q.W0[12]," ",Q.W0[16],";transition:box-shadow 0.15s ease-in-out;background-color:",Q.Jv.background.white,";[data-visually-hidden]{opacity:0;}&:hover{box-shadow:0 0 0 1px ",Q.Jv.stroke.hover,";[data-visually-hidden]{opacity:1;}}",r&&(0,a.iv)("background-color:",Q.Jv.background.white,";box-shadow:0 0 0 1px ",Q.Jv.stroke.brand,";&:hover{box-shadow:0 0 0 1px ",Q.Jv.stroke.brand,";}"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},optionHeader:true?{name:"1066lcq",styles:"display:flex;justify-content:space-between;align-items:center"}:0,optionTitle:(0,a.iv)(F.c.caption("medium"),";color:",Q.Jv.text.subdued,";"+(true?"":0),true?"":0),optionActions:(0,a.iv)("display:flex;gap:",Q.W0[8],";align-items:center;"+(true?"":0),true?"":0),actionButton:(0,a.iv)(pt.i.resetButton,";color:",Q.Jv.icon["default"],";display:flex;cursor:pointer;"+(true?"":0),true?"":0),optionBody:true?{name:"zjik7",styles:"display:flex"}:0,optionInputWrapper:(0,a.iv)(pt.i.optionInputWrapper,";gap:",Q.W0[16],";"+(true?"":0),true?"":0),placeholderWrapper:true?{name:"1fttcpj",styles:"display:flex;flex-direction:column"}:0,optionPlaceholder:function t(e){var r=e.isTitle,n=e.isCorrectAnswer;return(0,a.iv)(F.c.body(),";color:",Q.Jv.text.subdued,";padding-block:",Q.W0[4],";display:flex;align-items:center;gap:",Q.W0[4],";",r&&(0,a.iv)("color:",Q.Jv.text.hints,";"+(true?"":0),true?"":0)," ",n&&(0,a.iv)("color:",Q.Jv.text.success,";span{color:",Q.Jv.stroke.border,";}"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},inputWithHints:(0,a.iv)("display:flex;flex-direction:column;gap:",Q.W0[8],";"+(true?"":0),true?"":0),inputHints:(0,a.iv)("display:flex;gap:",Q.W0[4],";",F.c.small(),";color:",Q.Jv.text.hints,";align-items:flex-start;svg{flex-shrink:0;}"+(true?"":0),true?"":0),errorMessage:(0,a.iv)("display:flex;gap:",Q.W0[4],";",F.c.small(),";color:",Q.Jv.text.error,";align-items:flex-start;color:",Q.Jv.text.error,";svg{flex-shrink:0;color:",Q.Jv.icon.error,";}"+(true?"":0),true?"":0),optionInputButtons:(0,a.iv)("display:flex;justify-content:flex-end;gap:",Q.W0[8],";"+(true?"":0),true?"":0)};var gn=function t(){var e=(0,g.Gc)();var r=We(),n=r.activeQuestionIndex;var i=(0,g.Dq)({control:e.control,name:"questions.".concat(n,".question_answers")}),o=i.fields;return(0,a.tZ)("div",{css:bn.optionWrapper},(0,a.tZ)(g.Qr,{key:o.length?JSON.stringify(o[0]):"",control:e.control,name:"questions.".concat(n,".question_answers.0"),render:function t(e){return(0,a.tZ)(yn,e)}}))};const _n=gn;var bn={optionWrapper:(0,a.iv)(pt.i.display.flex("column"),";padding-left:",Q.W0[40],";"+(true?"":0),true?"":0)};var wn=r(8507);var Zn=r(8237);var xn=r(4215);function On(t){"@babel/helpers - typeof";return On="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},On(t)}function En(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */En=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function t(e,r,n){return e[r]=n}}function c(t,e,r,i){var o=e&&e.prototype instanceof f?e:f,a=Object.create(o.prototype),u=new E(i||[]);return n(a,"_invoke",{value:w(t,r,u)}),a}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};s(h,o,(function(){return this}));var y=Object.getPrototypeOf,m=y&&y(y(k([])));m&&m!==e&&r.call(m,o)&&(h=m);var g=v.prototype=f.prototype=Object.create(h);function _(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function b(t,e){function i(n,o,a,u){var s=l(t[n],t,o);if("throw"!==s.type){var c=s.arg,d=c.value;return d&&"object"==On(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){i("next",t,a,u)}),(function(t){i("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return i("throw",t,a,u)}))}u(s.arg)}var o;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){i(r,n,t,e)}))}return o=o?o.then(a,a):a()}})}function w(t,e,r){var n="suspendedStart";return function(i,o){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===i)throw o;return q()}for(r.method=i,r.arg=o;;){var a=r.delegate;if(a){var u=Z(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var s=l(t,e,r);if("normal"===s.type){if(n=r.done?"completed":"suspendedYield",s.arg===d)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(n="completed",r.method="throw",r.arg=s.arg)}}}function Z(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,Z(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var i=l(n,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,d;var o=i.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function x(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(x,this),this.reset(!0)}function k(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return i.next=i}}return{next:q}}function q(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=s(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,s(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},_(b.prototype),s(b.prototype,a,(function(){return this})),t.AsyncIterator=b,t.async=function(e,r,n,i,o){void 0===o&&(o=Promise);var a=new b(c(e,r,n,i),o);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},_(g),s(g,u,"Generator"),s(g,o,(function(){return this})),s(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,E.prototype={constructor:E,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function i(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],u=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(s&&c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function t(e,n){for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var i=n.completion;if("throw"===i.type){var o=i.arg;O(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function kn(t,e,r,n,i,o,a){try{var u=t[o](a);var s=u.value}catch(t){r(t);return}if(u.done){e(s)}else{Promise.resolve(s).then(n,i)}}function qn(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var o=t.apply(e,r);function a(t){kn(o,n,i,a,u,"next",t)}function u(t){kn(o,n,i,a,u,"throw",t)}a(undefined)}))}}function Sn(){Sn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Sn.apply(this,arguments)}function jn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Pn(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?jn(Object(r),!0).forEach((function(e){An(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):jn(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function An(t,e,r){e=Tn(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Tn(t){var e=Cn(t,"string");return On(e)==="symbol"?e:String(e)}function Cn(t,e){if(On(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(On(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function In(t,e){return Nn(t)||zn(t,e)||Ln(t,e)||Dn()}function Dn(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Ln(t,e){if(!t)return;if(typeof t==="string")return Wn(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Wn(t,e)}function Wn(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function zn(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],s=!0,c=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,i=t}finally{try{if(!s&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw i}}return u}}function Nn(t){if(Array.isArray(t))return t}function Un(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Jn=!!U.y.tutor_pro_url;var Qn=function t(e){var r;var n=e.index,i=e.onDuplicateOption,c=e.onRemoveOption,l=e.field,f=e.isOverlay,v=f===void 0?false:f;var y=We(),m=y.activeQuestionId,g=y.validationError,b=y.setValidationError;var w=(r=l.value)!==null&&r!==void 0?r:{answer_id:(0,B.x0)(),answer_title:"",is_correct:"0",belongs_question_id:m,belongs_question_type:"image_answering"};var x=(0,s.useRef)(null);var O=(0,s.useState)(!w.answer_title&&!w.image_id&&!w.image_url),E=In(O,2),k=E[0],q=E[1];var S=(0,s.useState)(w),j=In(S,2),P=j[0],A=j[1];var T=(0,o.nB)({id:l.value.answer_id||0,animateLayoutChanges:xn.h}),C=T.attributes,I=T.listeners,D=T.setNodeRef,L=T.transform,W=T.transition,z=T.isDragging;var N=(0,Zn.Z)({options:{type:"image"},onChange:function t(e){if(e&&!Array.isArray(e)){var r=e.id,n=e.url;l.onChange(Pn(Pn(Pn({},w),Qt(w._data_status,Pt.UPDATE)&&{_data_status:Qt(w._data_status,Pt.UPDATE)}),{},{image_id:r,image_url:n}))}},initialFiles:l.value.image_id?{id:Number(w.image_id),url:w.image_url||"",title:w.image_url||""}:null}),U=N.openMediaLibrary,J=N.resetFiles;var F={transform:h.ux.Transform.toString(L),transition:W,opacity:z?.3:undefined};var M=function t(){l.onChange(Pn(Pn(Pn({},w),Qt(w._data_status,Pt.UPDATE)&&{_data_status:Qt(w._data_status,Pt.UPDATE)}),{},{image_id:"",image_url:""}));J()};(0,s.useEffect)((function(){if((0,Pr.$K)(x.current)&&k){x.current.focus()}}),[k]);return(0,a.tZ)("div",Sn({},C,{css:Mn.option,ref:D,style:F,tabIndex:-1}),(0,a.tZ)("div",{css:Mn.optionLabel({isEditing:k,isOverlay:v,isDragging:z}),onClick:function t(){q(true)},onKeyDown:function t(e){e.stopPropagation();if(e.key==="Enter"||e.key===" "){q(true)}}},(0,a.tZ)("div",{css:Mn.optionHeader},(0,a.tZ)("div",{css:pt.i.optionCounter({isEditing:k})},String.fromCharCode(65+n)),(0,a.tZ)(Z.Z,{when:!k&&w.is_saved},(0,a.tZ)("button",Sn({},I,{type:"button",css:pt.i.optionDragButton({isOverlay:v}),"data-visually-hidden":true}),(0,a.tZ)(p.Z,{name:"dragVertical",height:24,width:24})),(0,a.tZ)("div",{css:Mn.optionActions,"data-visually-hidden":true},(0,a.tZ)(ct.Z,{content:(0,u.__)("Edit","tutor"),delay:200},(0,a.tZ)("button",{type:"button",css:pt.i.actionButton,onClick:function t(e){e.stopPropagation();q(true)}},(0,a.tZ)(p.Z,{name:"edit",width:24,height:24}))),(0,a.tZ)(ct.Z,{content:(0,u.__)("Duplicate","tutor"),delay:200},(0,a.tZ)(Z.Z,{when:!Jn,fallback:(0,a.tZ)("button",{type:"button",css:pt.i.actionButton,onClick:function t(e){e.stopPropagation();i(w)}},(0,a.tZ)(p.Z,{name:"copyPaste",width:24,height:24}))},(0,a.tZ)(_.Z,{size:"tiny"},(0,a.tZ)("button",{disabled:true,type:"button",css:pt.i.actionButton,onClick:B.ZT},(0,a.tZ)(p.Z,{name:"copyPaste",width:24,height:24}))))),(0,a.tZ)(ct.Z,{content:(0,u.__)("Delete","tutor"),delay:200},(0,a.tZ)("button",{type:"button",css:pt.i.actionButton,onClick:function t(e){e.stopPropagation();c()}},(0,a.tZ)(p.Z,{name:"delete",width:24,height:24})))))),(0,a.tZ)("div",{css:Mn.optionBody},(0,a.tZ)(Z.Z,{when:k,fallback:(0,a.tZ)("div",{css:Mn.placeholderWrapper},(0,a.tZ)(Z.Z,{when:w.image_url,fallback:(0,a.tZ)("div",{css:Mn.imagePlaceholder},(0,a.tZ)(p.Z,{name:"imagePreview",height:48,width:48}))},(function(){return(0,a.tZ)("div",{css:Mn.imagePlaceholder},(0,a.tZ)("img",{src:w.image_url,alt:w.image_url}))})),(0,a.tZ)("div",{css:Mn.optionPlaceholder},w.answer_title||(0,u.__)("Write answer option...","tutor")))},(0,a.tZ)("div",{css:pt.i.optionInputWrapper},(0,a.tZ)(wn.Z,{value:{id:Number(w.image_id),url:w.image_url||"",title:w.image_url||""},buttonText:(0,u.__)("Upload Image","tutor"),infoText:(0,u.__)("Standard Size: 700x430 pixels","tutor"),uploadHandler:U,clearHandler:M,emptyImageCss:Mn.emptyImageInput,previewImageCss:Mn.previewImageInput}),(0,a.tZ)("div",{css:Mn.inputWithHints},(0,a.tZ)("input",Sn({},l,{ref:x,type:"text",placeholder:(0,u.__)("Input answer here","tutor"),value:w.answer_title,onClick:function t(e){e.stopPropagation()},onChange:function t(e){l.onChange(Pn(Pn(Pn({},w),Qt(w._data_status,Pt.UPDATE)&&{_data_status:Qt(w._data_status,Pt.UPDATE)}),{},{answer_title:e.target.value}))},onKeyDown:function(){var t=qn(En().mark((function t(e){return En().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:e.stopPropagation();if((e.metaKey||e.ctrlKey)&&e.key==="Enter"&&w.answer_title){l.onChange(Pn(Pn({},w),Qt(w._data_status,Pt.UPDATE)&&{_data_status:Qt(w._data_status,Pt.UPDATE),is_saved:true}));if((g===null||g===void 0?void 0:g.type)==="save_option"){b(null)}q(false)}case 2:case"end":return r.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()})),(0,a.tZ)("div",{css:Mn.inputHints},(0,a.tZ)(p.Z,{name:"info",height:20,width:20}),(0,a.tZ)("p",null,(0,u.__)("Students need to type their answers exactly as you write them here. Use ","tutor"),(0,a.tZ)("span",{css:(0,a.iv)({fontWeight:Q.Ue.semiBold},true?"":0,true?"":0)},(0,u.__)("small caps","tutor")),(0,u.__)(" when writing the answer.","tutor")))),(0,a.tZ)("div",{css:Mn.optionInputButtons},(0,a.tZ)(d.Z,{variant:"text",size:"small",onClick:function t(e){e.stopPropagation();q(false);l.onChange(P);if(!w.is_saved){c();if((g===null||g===void 0?void 0:g.type)==="save_option"){b(null)}}}},(0,u.__)("Cancel","tutor")),(0,a.tZ)(d.Z,{variant:"secondary",size:"small",onClick:function(){var t=qn(En().mark((function t(e){return En().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:e.stopPropagation();l.onChange(Pn(Pn(Pn({},w),Qt(w._data_status,Pt.UPDATE)&&{_data_status:Qt(w._data_status,Pt.UPDATE)}),{},{is_saved:true}));A(Pn(Pn(Pn({},w),Qt(w._data_status,Pt.UPDATE)&&{_data_status:Qt(w._data_status,Pt.UPDATE)}),{},{is_saved:true}));if((g===null||g===void 0?void 0:g.type)==="save_option"){b(null)}q(false);case 5:case"end":return r.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),disabled:!w.answer_title||!w.image_id},(0,u.__)("Ok","tutor"))))))))};const Fn=Qn;var Mn={option:(0,a.iv)(pt.i.display.flex(),";",F.c.caption("medium"),";align-items:center;color:",Q.Jv.text.subdued,";gap:",Q.W0[10],";align-items:center;"+(true?"":0),true?"":0),optionLabel:function t(e){var r=e.isEditing,n=e.isOverlay,i=e.isDragging;return(0,a.iv)(pt.i.display.flex("column")," gap:",Q.W0[20],";width:100%;border-radius:",Q.E0.card,";padding:",Q.W0[12]," ",Q.W0[16],";background-color:",Q.Jv.background.white,";[data-visually-hidden]{opacity:0;}&:hover{outline:1px solid ",Q.Jv.stroke.hover,";[data-visually-hidden]{opacity:1;}}",r&&(0,a.iv)("background-color:",Q.Jv.background.white,";outline:1px solid ",Q.Jv.stroke.brand,";&:hover{outline:1px solid ",Q.Jv.stroke.brand,";}"+(true?"":0),true?"":0)," ",i&&(0,a.iv)("background-color:",Q.Jv.stroke.hover,";"+(true?"":0),true?"":0)," ",n&&(0,a.iv)("box-shadow:",Q.AF.drag,";"+(true?"":0),true?"":0)," ",Q.Uo.smallTablet,"{[data-visually-hidden]{opacity:1;}}"+(true?"":0),true?"":0)},optionHeader:true?{name:"ilv1kv",styles:"display:grid;grid-template-columns:1fr auto 1fr;align-items:center;:focus-within{[data-visually-hidden]{opacity:1;}}"}:0,optionActions:(0,a.iv)(pt.i.display.flex()," gap:",Q.W0[8],";place-self:center end;"+(true?"":0),true?"":0),optionBody:(0,a.iv)(pt.i.display.flex(),";"+(true?"":0),true?"":0),placeholderWrapper:(0,a.iv)(pt.i.display.flex("column")," gap:",Q.W0[12],";width:100%;"+(true?"":0),true?"":0),imagePlaceholder:(0,a.iv)(pt.i.flexCenter(),";height:210px;width:100%;background-color:",Q.Jv.background["default"],";border-radius:",Q.E0.card,";overflow:hidden;svg{color:",Q.Jv.icon.hints,";}img{width:100%;height:100%;object-fit:cover;object-position:center;}"+(true?"":0),true?"":0),emptyImageInput:(0,a.iv)("background-color:",Q.Jv.background["default"],";height:210px;"+(true?"":0),true?"":0),previewImageInput:true?{name:"be4gwm",styles:"height:210px"}:0,optionPlaceholder:(0,a.iv)(F.c.body(),";color:",Q.Jv.text.subdued,";padding-block:",Q.W0[4],";"+(true?"":0),true?"":0),inputWithHints:(0,a.iv)(pt.i.display.flex("column")," gap:",Q.W0[8],";"+(true?"":0),true?"":0),inputHints:(0,a.iv)(pt.i.display.flex()," gap:",Q.W0[4],";",F.c.small(),";color:",Q.Jv.text.hints,";align-items:flex-start;svg{flex-shrink:0;}"+(true?"":0),true?"":0),optionInputButtons:(0,a.iv)(pt.i.display.flex()," justify-content:flex-end;gap:",Q.W0[8],";"+(true?"":0),true?"":0)};function Bn(t){"@babel/helpers - typeof";return Bn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Bn(t)}function Gn(){Gn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Gn.apply(this,arguments)}function Rn(t){return Kn(t)||Hn(t)||ii(t)||Vn()}function Vn(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Hn(t){if(typeof Symbol!=="undefined"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function Kn(t){if(Array.isArray(t))return oi(t)}function Yn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function $n(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Yn(Object(r),!0).forEach((function(e){Xn(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Yn(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Xn(t,e,r){e=ti(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function ti(t){var e=ei(t,"string");return Bn(e)==="symbol"?e:String(e)}function ei(t,e){if(Bn(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Bn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function ri(t,e){return ui(t)||ai(t,e)||ii(t,e)||ni()}function ni(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function ii(t,e){if(!t)return;if(typeof t==="string")return oi(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return oi(t,e)}function oi(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function ai(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],s=!0,c=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,i=t}finally{try{if(!s&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw i}}return u}}function ui(t){if(Array.isArray(t))return t}var si=function t(){var e=(0,s.useState)(null),r=ri(e,2),l=r[0],f=r[1];var v=(0,g.Gc)();var h=We(),y=h.activeQuestionIndex,m=h.activeQuestionId,_=h.validationError,b=h.setValidationError;var w=(0,g.Dq)({control:v.control,name:"questions.".concat(y,".question_answers")}),x=w.fields,O=w.append,E=w.insert,k=w.remove,q=w.move;var S=(0,n.Dy)((0,n.VT)(n.we,{activationConstraint:{distance:10}}),(0,n.VT)(n.Lg,{coordinateGetter:o.is}));var j=(0,s.useMemo)((function(){if(!l){return null}return x.find((function(t){return t.answer_id===l}))}),[l,x]);var P=function t(){O({_data_status:Pt.NEW,is_saved:false,answer_id:(0,B.x0)(),answer_title:"",is_correct:"0",belongs_question_id:m,belongs_question_type:"image_answering",answer_order:x.length,answer_two_gap_match:"",answer_view_format:""},{shouldFocus:true,focusName:"questions.".concat(y,".question_answers.").concat(x.length,".answer_title")});if((_===null||_===void 0?void 0:_.type)==="add_option"){b(null)}};var A=function t(e,r){var n=$n($n({},r),{},{_data_status:Pt.NEW,is_saved:true,answer_id:(0,B.x0)(),answer_title:"".concat(r.answer_title," (copy)"),is_correct:"0"});var i=e+1;E(i,n)};var T=function t(e,r){k(e);if(r._data_status!==Pt.NEW){v.setValue("deleted_answer_ids",[].concat(Rn(v.getValues("deleted_answer_ids")),[r.answer_id]))}};return(0,a.tZ)("div",{css:li.optionWrapper},(0,a.tZ)(n.LB,{sensors:S,collisionDetection:n.pE,modifiers:[i.hg],onDragStart:function t(e){f(e.active.id)},onDragEnd:function t(e){var r=e.active,n=e.over;if(!n){return}if(r.id!==n.id){var i=x.findIndex((function(t){return t.answer_id===r.id}));var o=x.findIndex((function(t){return t.answer_id===n.id}));q(i,o)}f(null)}},(0,a.tZ)(o.Fo,{items:x.map((function(t){return $n($n({},t),{},{id:t.answer_id})})),strategy:o.qw},(0,a.tZ)(Kr.Z,{each:x},(function(t,e){return(0,a.tZ)(g.Qr,{key:"".concat(t.answer_id,"-").concat(e),control:v.control,name:"questions.".concat(y,".question_answers.").concat(e),render:function r(n){return(0,a.tZ)(Fn,Gn({},n,{onDuplicateOption:function t(r){return A(e,r)},onRemoveOption:function r(){return T(e,t)},index:e}))}})}))),(0,c.createPortal)((0,a.tZ)(n.y9,null,(0,a.tZ)(Z.Z,{when:j},(function(t){var e=x.findIndex((function(e){return e.answer_id===t.answer_id}));return(0,a.tZ)(g.Qr,{key:l,control:v.control,name:"questions.".concat(y,".question_answers.").concat(e),render:function t(r){return(0,a.tZ)(Fn,Gn({},r,{onDuplicateOption:B.ZT,onRemoveOption:B.ZT,index:e,isOverlay:true}))}})}))),document.body)),(0,a.tZ)("div",null,(0,a.tZ)(d.Z,{variant:"text",onClick:P,buttonContentCss:li.addOptionButton,icon:(0,a.tZ)(p.Z,{name:"plus",height:24,width:24})},(0,u.__)("Add Option","tutor"))))};const ci=si;var li={optionWrapper:(0,a.iv)(pt.i.display.flex("column"),";gap:",Q.W0[12],";padding-left:",Q.W0[40],";",Q.Uo.smallMobile,"{padding-left:",Q.W0[8],";}"+(true?"":0),true?"":0),addOptionButton:(0,a.iv)("color:",Q.Jv.text.brand,";svg{color:",Q.Jv.icon.brand,";}"+(true?"":0),true?"":0)};function di(t){"@babel/helpers - typeof";return di="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},di(t)}function fi(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */fi=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function t(e,r,n){return e[r]=n}}function c(t,e,r,i){var o=e&&e.prototype instanceof f?e:f,a=Object.create(o.prototype),u=new E(i||[]);return n(a,"_invoke",{value:w(t,r,u)}),a}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};s(h,o,(function(){return this}));var y=Object.getPrototypeOf,m=y&&y(y(k([])));m&&m!==e&&r.call(m,o)&&(h=m);var g=v.prototype=f.prototype=Object.create(h);function _(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function b(t,e){function i(n,o,a,u){var s=l(t[n],t,o);if("throw"!==s.type){var c=s.arg,d=c.value;return d&&"object"==di(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){i("next",t,a,u)}),(function(t){i("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return i("throw",t,a,u)}))}u(s.arg)}var o;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){i(r,n,t,e)}))}return o=o?o.then(a,a):a()}})}function w(t,e,r){var n="suspendedStart";return function(i,o){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===i)throw o;return q()}for(r.method=i,r.arg=o;;){var a=r.delegate;if(a){var u=Z(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var s=l(t,e,r);if("normal"===s.type){if(n=r.done?"completed":"suspendedYield",s.arg===d)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(n="completed",r.method="throw",r.arg=s.arg)}}}function Z(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,Z(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var i=l(n,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,d;var o=i.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function x(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(x,this),this.reset(!0)}function k(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return i.next=i}}return{next:q}}function q(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=s(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,s(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},_(b.prototype),s(b.prototype,a,(function(){return this})),t.AsyncIterator=b,t.async=function(e,r,n,i,o){void 0===o&&(o=Promise);var a=new b(c(e,r,n,i),o);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},_(g),s(g,u,"Generator"),s(g,o,(function(){return this})),s(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,E.prototype={constructor:E,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function i(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],u=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(s&&c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function t(e,n){for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var i=n.completion;if("throw"===i.type){var o=i.arg;O(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function pi(t,e,r,n,i,o,a){try{var u=t[o](a);var s=u.value}catch(t){r(t);return}if(u.done){e(s)}else{Promise.resolve(s).then(n,i)}}function vi(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var o=t.apply(e,r);function a(t){pi(o,n,i,a,u,"next",t)}function u(t){pi(o,n,i,a,u,"throw",t)}a(undefined)}))}}function hi(){hi=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return hi.apply(this,arguments)}function yi(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function mi(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?yi(Object(r),!0).forEach((function(e){gi(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):yi(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function gi(t,e,r){e=_i(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function _i(t){var e=bi(t,"string");return di(e)==="symbol"?e:String(e)}function bi(t,e){if(di(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(di(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function wi(t,e){return ki(t)||Ei(t,e)||xi(t,e)||Zi()}function Zi(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function xi(t,e){if(!t)return;if(typeof t==="string")return Oi(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Oi(t,e)}function Oi(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Ei(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],s=!0,c=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,i=t}finally{try{if(!s&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw i}}return u}}function ki(t){if(Array.isArray(t))return t}function qi(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Si=!!U.y.tutor_pro_url;var ji=function t(e){var r,n;var i=e.index,c=e.onDuplicateOption,l=e.onRemoveOption,f=e.field,v=e.isOverlay,y=v===void 0?false:v;var m=We(),b=m.activeQuestionId,w=m.activeQuestionIndex,x=m.validationError,O=m.setValidationError;var E=(0,g.Gc)();var k=(r=f.value)!==null&&r!==void 0?r:{answer_id:"",answer_title:"",answer_two_gap_match:"",is_correct:"0",belongs_question_id:b,belongs_question_type:"matching"};var q=(0,s.useRef)(null);var S=(0,g.qo)({control:E.control,name:"questions.".concat(w,".question_settings.is_image_matching"),defaultValue:false});var j=(0,s.useState)(!k.answer_title||S?!k.image_url:!k.answer_two_gap_match),P=wi(j,2),A=P[0],T=P[1];var C=(0,s.useState)(k),I=wi(C,2),D=I[0],L=I[1];var W=(0,o.nB)({id:f.value.answer_id||0,animateLayoutChanges:xn.h}),z=W.attributes,N=W.listeners,U=W.setNodeRef,J=W.transform,Q=W.transition,F=W.isDragging;var M=(0,Zn.Z)({options:{type:"image"},onChange:function t(e){if(e&&!Array.isArray(e)){var r=e.id,n=e.url;f.onChange(mi(mi(mi({},k),Qt(k._data_status,Pt.UPDATE)&&{_data_status:Qt(k._data_status,Pt.UPDATE)}),{},{image_id:r,image_url:n}))}},initialFiles:(n=f.value)!==null&&n!==void 0&&n.image_id?{id:Number(f.value.image_id),url:f.value.image_url||"",title:""}:null}),G=M.openMediaLibrary,R=M.resetFiles;var V={transform:h.ux.Transform.toString(J),transition:Q,opacity:F?.3:undefined};var H=function t(){f.onChange(mi(mi(mi({},k),Qt(k._data_status,Pt.UPDATE)&&{_data_status:Qt(k._data_status,Pt.UPDATE)}),{},{image_id:"",image_url:""}));R()};(0,s.useEffect)((function(){if((0,Pr.$K)(q.current)&&A){q.current.focus()}}),[A]);return(0,a.tZ)("div",hi({},z,{css:Ai.option,ref:U,tabIndex:-1,style:V}),(0,a.tZ)("div",{css:Ai.optionLabel({isEditing:A,isDragging:F,isOverlay:y}),onClick:function t(){T(true)},onKeyDown:function t(e){e.stopPropagation();if(e.key==="Enter"||e.key===" "){T(true)}}},(0,a.tZ)("div",{css:Ai.optionHeader},(0,a.tZ)("div",{css:pt.i.optionCounter({isEditing:A})},String.fromCharCode(65+i)),(0,a.tZ)(Z.Z,{when:!A&&k.is_saved},(0,a.tZ)("button",hi({},N,{type:"button",css:pt.i.optionDragButton({isOverlay:y}),"data-visually-hidden":true}),(0,a.tZ)(p.Z,{name:"dragVertical",height:24,width:24})),(0,a.tZ)("div",{css:Ai.optionActions,"data-visually-hidden":true},(0,a.tZ)(ct.Z,{content:(0,u.__)("Edit","tutor"),delay:200},(0,a.tZ)("button",{type:"button",css:pt.i.actionButton,"data-edit-button":true,onClick:function t(e){e.stopPropagation();T(true)}},(0,a.tZ)(p.Z,{name:"edit",width:24,height:24}))),(0,a.tZ)(ct.Z,{content:(0,u.__)("Duplicate","tutor"),delay:200},(0,a.tZ)(Z.Z,{when:!Si,fallback:(0,a.tZ)("button",{type:"button",css:pt.i.actionButton,onClick:function t(e){e.stopPropagation();c(k)}},(0,a.tZ)(p.Z,{name:"copyPaste",width:24,height:24}))},(0,a.tZ)(_.Z,{size:"tiny"},(0,a.tZ)("button",{disabled:true,type:"button",css:pt.i.actionButton,onClick:B.ZT},(0,a.tZ)(p.Z,{name:"copyPaste",width:24,height:24}))))),(0,a.tZ)(ct.Z,{content:(0,u.__)("Delete","tutor"),delay:200},(0,a.tZ)("button",{type:"button",css:pt.i.actionButton,"data-visually-hidden":true,onClick:function t(e){e.stopPropagation();l()}},(0,a.tZ)(p.Z,{name:"delete",width:24,height:24})))))),(0,a.tZ)("div",{css:Ai.optionBody},(0,a.tZ)(Z.Z,{when:A,fallback:(0,a.tZ)("div",{css:Ai.placeholderWrapper({isImageMatching:S})},(0,a.tZ)(Z.Z,{when:S,fallback:(0,a.tZ)("div",{css:Ai.optionPlaceholder},k.answer_title||(0,u.__)("Answer title...","tutor"))},(0,a.tZ)(Z.Z,{when:k.image_url,fallback:(0,a.tZ)("div",{css:Ai.imagePlaceholder},(0,a.tZ)(p.Z,{name:"imagePreview",height:48,width:48}))},(function(){return(0,a.tZ)("div",{css:Ai.imagePlaceholder},(0,a.tZ)("img",{src:k.image_url,alt:k.image_url}))}))),(0,a.tZ)("div",{css:Ai.optionPlaceholder},!S?k.answer_two_gap_match:k.answer_title))},(0,a.tZ)("div",{css:pt.i.optionInputWrapper},(0,a.tZ)(Z.Z,{when:S},(0,a.tZ)(wn.Z,{value:{id:Number(k.image_id),url:k.image_url||"",title:k.image_url||""},infoText:(0,u.__)("Standard Size: 700x430 pixels","tutor"),uploadHandler:G,clearHandler:H,emptyImageCss:Ai.emptyImageInput,previewImageCss:Ai.previewImageInput})),(0,a.tZ)("input",hi({},f,{type:"text",ref:q,placeholder:!S?(0,u.__)("Question","tutor"):(0,u.__)("Image matched text..","tutor"),value:k.answer_title,onClick:function t(e){e.stopPropagation()},onChange:function t(e){f.onChange(mi(mi(mi({},k),Qt(k._data_status,Pt.UPDATE)&&{_data_status:Qt(k._data_status,Pt.UPDATE)}),{},{answer_title:e.target.value}))},onKeyDown:function(){var t=vi(fi().mark((function t(e){return fi().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:e.stopPropagation();if((e.metaKey||e.ctrlKey)&&e.key==="Enter"&&k.answer_title&&k.answer_two_gap_match){f.onChange(mi(mi({},k),Qt(k._data_status,Pt.UPDATE)&&{_data_status:Qt(k._data_status,Pt.UPDATE)}));T(false)}case 2:case"end":return r.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()})),(0,a.tZ)(Z.Z,{when:!S},(0,a.tZ)("input",hi({},f,{type:"text",placeholder:(0,u.__)("Matched option..","tutor"),value:k.answer_two_gap_match,onClick:function t(e){e.stopPropagation()},onChange:function t(e){f.onChange(mi(mi(mi({},k),Qt(k._data_status,Pt.UPDATE)&&{_data_status:Qt(k._data_status,Pt.UPDATE)}),{},{answer_two_gap_match:e.target.value}))},onKeyDown:function t(e){e.stopPropagation();if((e.metaKey||e.ctrlKey)&&e.key==="Enter"&&k.answer_title&&k.answer_two_gap_match){f.onChange(mi(mi({},k),Qt(k._data_status,Pt.UPDATE)&&{_data_status:Qt(k._data_status,Pt.UPDATE),is_saved:true}));if((x===null||x===void 0?void 0:x.type)==="save_option"){O(null)}T(false)}}}))),(0,a.tZ)("div",{css:Ai.optionInputButtons},(0,a.tZ)(d.Z,{variant:"text",size:"small",onClick:function t(e){e.stopPropagation();T(false);f.onChange(D);if(!k.is_saved){if((x===null||x===void 0?void 0:x.type)==="save_option"){O(null)}l()}}},(0,u.__)("Cancel","tutor")),(0,a.tZ)(d.Z,{variant:"secondary",size:"small",onClick:function t(e){e.stopPropagation();f.onChange(mi(mi(mi({},k),Qt(k._data_status,Pt.UPDATE)&&{_data_status:Qt(k._data_status,Pt.UPDATE)}),{},{is_saved:true}));L(mi(mi(mi({},k),Qt(k._data_status,Pt.UPDATE)&&{_data_status:Qt(k._data_status,Pt.UPDATE)}),{},{is_saved:true}));if((x===null||x===void 0?void 0:x.type)==="save_option"){O(null)}T(false)},disabled:!k.answer_title||(S?!k.image_id:!k.answer_two_gap_match)},(0,u.__)("Ok","tutor"))))))))};const Pi=ji;var Ai={option:(0,a.iv)(pt.i.display.flex(),";",F.c.caption("medium"),";align-items:center;color:",Q.Jv.text.subdued,";gap:",Q.W0[10],";align-items:center;"+(true?"":0),true?"":0),optionLabel:function t(e){var r=e.isEditing,n=e.isDragging,i=e.isOverlay;return(0,a.iv)("display:flex;flex-direction:column;gap:",Q.W0[12],";width:100%;border-radius:",Q.E0.card,";padding:",Q.W0[12]," ",Q.W0[16],";background-color:",Q.Jv.background.white,";[data-visually-hidden]{opacity:0;}&:hover{outline:1px solid ",Q.Jv.stroke.hover,";[data-visually-hidden]{opacity:1;}}",r&&(0,a.iv)("background-color:",Q.Jv.background.white,";outline:1px solid ",Q.Jv.stroke.brand,";&:hover{outline:1px solid ",Q.Jv.stroke.brand,";}"+(true?"":0),true?"":0)," ",n&&(0,a.iv)("background-color:",Q.Jv.stroke.hover,";"+(true?"":0),true?"":0)," ",i&&(0,a.iv)("box-shadow:",Q.AF.drag,";"+(true?"":0),true?"":0)," ",Q.Uo.smallTablet,"{[data-visually-hidden]{opacity:1;}}"+(true?"":0),true?"":0)},optionHeader:true?{name:"1b8dd6m",styles:"display:grid;grid-template-columns:1fr auto 1fr;align-items:center;&:focus-within{[data-visually-hidden]{opacity:1;}}"}:0,optionActions:(0,a.iv)(pt.i.display.flex()," gap:",Q.W0[8],";place-self:center end;"+(true?"":0),true?"":0),actionButton:(0,a.iv)(pt.i.resetButton,";",pt.i.display.flex()," color:",Q.Jv.icon["default"],";cursor:pointer;&:disabled{cursor:not-allowed;color:",Q.Jv.icon.disable.background,";}"+(true?"":0),true?"":0),optionBody:(0,a.iv)(pt.i.display.flex(),";"+(true?"":0),true?"":0),placeholderWrapper:function t(e){var r=e.isImageMatching;return(0,a.iv)(pt.i.display.flex("column")," width:100%;",r&&(0,a.iv)("gap:",Q.W0[12],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},imagePlaceholder:(0,a.iv)(pt.i.flexCenter(),";height:210px;width:100%;background-color:",Q.Jv.background["default"],";border-radius:",Q.E0.card,";overflow:hidden;svg{color:",Q.Jv.icon.hints,";}img{width:100%;height:100%;object-fit:cover;object-position:center;}"+(true?"":0),true?"":0),emptyImageInput:(0,a.iv)("background-color:",Q.Jv.background["default"],";height:210px;"+(true?"":0),true?"":0),previewImageInput:true?{name:"be4gwm",styles:"height:210px"}:0,optionPlaceholder:(0,a.iv)(F.c.body(),";color:",Q.Jv.text.subdued,";padding-block:",Q.W0[4],";"+(true?"":0),true?"":0),optionInputWrapper:(0,a.iv)(pt.i.display.flex("column")," width:100%;gap:",Q.W0[12],";"+(true?"":0),true?"":0),optionInputButtons:(0,a.iv)(pt.i.display.flex()," justify-content:flex-end;gap:",Q.W0[8],";"+(true?"":0),true?"":0)};function Ti(t){"@babel/helpers - typeof";return Ti="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ti(t)}function Ci(){Ci=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Ci.apply(this,arguments)}function Ii(t){return Wi(t)||Li(t)||Bi(t)||Di()}function Di(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Li(t){if(typeof Symbol!=="undefined"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function Wi(t){if(Array.isArray(t))return Gi(t)}function zi(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Ni(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?zi(Object(r),!0).forEach((function(e){Ui(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):zi(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Ui(t,e,r){e=Ji(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Ji(t){var e=Qi(t,"string");return Ti(e)==="symbol"?e:String(e)}function Qi(t,e){if(Ti(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Ti(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Fi(t,e){return Vi(t)||Ri(t,e)||Bi(t,e)||Mi()}function Mi(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Bi(t,e){if(!t)return;if(typeof t==="string")return Gi(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Gi(t,e)}function Gi(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Ri(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],s=!0,c=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,i=t}finally{try{if(!s&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw i}}return u}}function Vi(t){if(Array.isArray(t))return t}var Hi=function t(){var e=(0,s.useState)(null),r=Fi(e,2),l=r[0],f=r[1];var v=(0,g.Gc)();var h=We(),y=h.activeQuestionIndex,m=h.activeQuestionId,_=h.validationError,b=h.setValidationError;var w=(0,g.Dq)({control:v.control,name:"questions.".concat(y,".question_answers")}),x=w.fields,O=w.append,E=w.insert,k=w.remove,q=w.move;var S=(0,g.qo)({control:v.control,name:"questions.".concat(y,".question_settings.is_image_matching"),defaultValue:false});var j=(0,n.Dy)((0,n.VT)(n.we,{activationConstraint:{distance:10}}),(0,n.VT)(n.Lg,{coordinateGetter:o.is}));var P=(0,s.useMemo)((function(){if(!l){return null}return x.find((function(t){return t.answer_id===l}))}),[l,x]);var A=function t(){O({_data_status:Pt.NEW,is_saved:false,answer_id:(0,B.x0)(),answer_title:"",is_correct:"0",belongs_question_id:m,belongs_question_type:S?"image_matching":"matching",answer_order:x.length,answer_two_gap_match:"",answer_view_format:""},{shouldFocus:true,focusName:"questions.".concat(y,".question_answers.").concat(x.length,".answer_title")});if((_===null||_===void 0?void 0:_.type)==="add_option"){b(null)}};var T=function t(e,r){var n=Ni(Ni({},r),{},{_data_status:Pt.NEW,is_saved:true,answer_id:(0,B.x0)(),answer_title:"".concat(r.answer_title," (copy)"),is_correct:"0"});var i=e+1;E(i,n)};var C=function t(e,r){k(e);if(r._data_status!==Pt.NEW){v.setValue("deleted_answer_ids",[].concat(Ii(v.getValues("deleted_answer_ids")),[r.answer_id]))}};return(0,a.tZ)("div",{css:Yi.optionWrapper},(0,a.tZ)(n.LB,{sensors:j,collisionDetection:n.pE,modifiers:[i.hg],onDragStart:function t(e){f(e.active.id)},onDragEnd:function t(e){var r=e.active,n=e.over;if(!n){return}if(r.id!==n.id){var i=x.findIndex((function(t){return t.answer_id===r.id}));var o=x.findIndex((function(t){return t.answer_id===n.id}));q(i,o)}f(null)}},(0,a.tZ)(o.Fo,{items:x.map((function(t){return Ni(Ni({},t),{},{id:t.answer_id})})),strategy:o.qw},(0,a.tZ)(Kr.Z,{each:x},(function(t,e){return(0,a.tZ)(g.Qr,{key:"".concat(t.answer_id,"-").concat(e),control:v.control,name:"questions.".concat(y,".question_answers.").concat(e),render:function r(n){return(0,a.tZ)(Pi,Ci({},n,{index:e,onDuplicateOption:function t(r){return T(e,r)},onRemoveOption:function r(){return C(e,t)}}))}})}))),(0,c.createPortal)((0,a.tZ)(n.y9,null,(0,a.tZ)(Z.Z,{when:P},(function(t){var e=x.findIndex((function(e){return e.answer_id===t.answer_id}));return(0,a.tZ)(g.Qr,{key:l,control:v.control,name:"questions.".concat(y,".question_answers.").concat(e),render:function t(r){return(0,a.tZ)(Pi,Ci({},r,{index:e,onDuplicateOption:B.ZT,onRemoveOption:B.ZT,isOverlay:true}))}})}))),document.body)),(0,a.tZ)("div",null,(0,a.tZ)(d.Z,{variant:"text",onClick:A,buttonContentCss:Yi.addOptionButton,icon:(0,a.tZ)(p.Z,{name:"plus",height:24,width:24})},(0,u.__)("Add Option","tutor"))))};const Ki=Hi;var Yi={optionWrapper:(0,a.iv)(pt.i.display.flex("column"),";gap:",Q.W0[12],";padding-left:",Q.W0[40],";",Q.Uo.smallMobile,"{padding-left:",Q.W0[8],";}"+(true?"":0),true?"":0),addOptionButton:(0,a.iv)("color:",Q.Jv.text.brand,";svg{color:",Q.Jv.icon.brand,";}"+(true?"":0),true?"":0)};function $i(t){"@babel/helpers - typeof";return $i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},$i(t)}function Xi(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Xi=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function t(e,r,n){return e[r]=n}}function c(t,e,r,i){var o=e&&e.prototype instanceof f?e:f,a=Object.create(o.prototype),u=new E(i||[]);return n(a,"_invoke",{value:w(t,r,u)}),a}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};s(h,o,(function(){return this}));var y=Object.getPrototypeOf,m=y&&y(y(k([])));m&&m!==e&&r.call(m,o)&&(h=m);var g=v.prototype=f.prototype=Object.create(h);function _(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function b(t,e){function i(n,o,a,u){var s=l(t[n],t,o);if("throw"!==s.type){var c=s.arg,d=c.value;return d&&"object"==$i(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){i("next",t,a,u)}),(function(t){i("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return i("throw",t,a,u)}))}u(s.arg)}var o;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){i(r,n,t,e)}))}return o=o?o.then(a,a):a()}})}function w(t,e,r){var n="suspendedStart";return function(i,o){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===i)throw o;return q()}for(r.method=i,r.arg=o;;){var a=r.delegate;if(a){var u=Z(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var s=l(t,e,r);if("normal"===s.type){if(n=r.done?"completed":"suspendedYield",s.arg===d)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(n="completed",r.method="throw",r.arg=s.arg)}}}function Z(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,Z(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var i=l(n,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,d;var o=i.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function x(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(x,this),this.reset(!0)}function k(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return i.next=i}}return{next:q}}function q(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=s(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,s(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},_(b.prototype),s(b.prototype,a,(function(){return this})),t.AsyncIterator=b,t.async=function(e,r,n,i,o){void 0===o&&(o=Promise);var a=new b(c(e,r,n,i),o);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},_(g),s(g,u,"Generator"),s(g,o,(function(){return this})),s(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,E.prototype={constructor:E,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function i(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],u=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(s&&c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function t(e,n){for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var i=n.completion;if("throw"===i.type){var o=i.arg;O(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function to(t,e,r,n,i,o,a){try{var u=t[o](a);var s=u.value}catch(t){r(t);return}if(u.done){e(s)}else{Promise.resolve(s).then(n,i)}}function eo(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var o=t.apply(e,r);function a(t){to(o,n,i,a,u,"next",t)}function u(t){to(o,n,i,a,u,"throw",t)}a(undefined)}))}}function ro(){ro=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return ro.apply(this,arguments)}function no(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function io(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?no(Object(r),!0).forEach((function(e){oo(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):no(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function oo(t,e,r){e=ao(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function ao(t){var e=uo(t,"string");return $i(e)==="symbol"?e:String(e)}function uo(t,e){if($i(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if($i(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function so(t,e){return vo(t)||po(t,e)||lo(t,e)||co()}function co(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function lo(t,e){if(!t)return;if(typeof t==="string")return fo(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fo(t,e)}function fo(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function po(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],s=!0,c=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,i=t}finally{try{if(!s&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw i}}return u}}function vo(t){if(Array.isArray(t))return t}function ho(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var yo=!!U.y.tutor_pro_url;var mo=function t(e){var r,n,i;var c=e.field,l=e.onDuplicateOption,f=e.onRemoveOption,v=e.onCheckCorrectAnswer,y=e.index,m=e.isOverlay,b=m===void 0?false:m;var w=(0,g.Gc)();var x=We(),O=x.activeQuestionId,E=x.activeQuestionIndex,k=x.validationError,q=x.setValidationError;var S=(r=c.value)!==null&&r!==void 0?r:{answer_id:(0,B.x0)(),answer_title:"",is_correct:"0",belongs_question_id:O,belongs_question_type:"multiple_choice"};var j=(0,s.useRef)(null);var P=(0,g.qo)({control:w.control,name:"questions.".concat(E,".question_settings.has_multiple_correct_answer"),defaultValue:false});var A=w.watch("questions.".concat(E,".question_type"));var T=(0,s.useState)(!S.is_saved||!S.answer_title&&!S.image_url),C=so(T,2),I=C[0],D=C[1];var L=(0,s.useState)((0,Pr.$K)(S.image_id)&&(0,Pr.$K)(S.image_url)),W=so(L,2),z=W[0],N=W[1];var U=(0,s.useState)(S),J=so(U,2),Q=J[0],F=J[1];var M=(0,o.nB)({id:((n=c.value)===null||n===void 0?void 0:n.answer_id)||0,animateLayoutChanges:xn.h}),G=M.attributes,R=M.listeners,V=M.setNodeRef,H=M.transform,K=M.transition,Y=M.isDragging;var $=(0,Zn.Z)({options:{type:"image"},onChange:function t(e){if(e&&!Array.isArray(e)){var r=e.id,n=e.url;c.onChange(io(io(io({},S),Qt(S._data_status,Pt.UPDATE)&&{_data_status:Qt(S._data_status,Pt.UPDATE)}),{},{image_id:r,image_url:n,answer_view_format:"text_image"}));N(true)}},initialFiles:(i=c.value)!==null&&i!==void 0&&i.image_id?{id:Number(c.value.image_id),url:c.value.image_url||"",title:""}:null}),X=$.openMediaLibrary,tt=$.resetFiles;var et={transform:h.ux.Transform.toString(H),transition:K,opacity:Y?.3:undefined};var rt=function t(){tt();c.onChange(io(io(io({},S),Qt(S._data_status,Pt.UPDATE)&&{_data_status:Qt(S._data_status,Pt.UPDATE)}),{},{image_id:"",image_url:"",answer_view_format:"text"}));N(false)};(0,s.useEffect)((function(){if((0,Pr.$K)(j.current)&&I){j.current.focus()}}),[I]);return(0,a.tZ)("div",ro({},G,{css:bo.option({isSelected:!!Number(S.is_correct),isMultipleChoice:P}),tabIndex:-1,ref:V,style:et}),(0,a.tZ)(Z.Z,{when:A==="multiple_choice"},(0,a.tZ)("button",{key:S.is_correct,css:pt.i.optionCheckButton,"data-check-button":true,type:"button",onClick:v},(0,a.tZ)(Z.Z,{when:P,fallback:(0,a.tZ)(p.Z,{name:Number(S.is_correct)?"checkFilled":"check",height:32,width:32})},(0,a.tZ)(p.Z,{name:Number(S.is_correct)?"checkSquareFilled":"checkSquare",height:32,width:32})))),(0,a.tZ)("div",{css:bo.optionLabel({isSelected:!!Number(S.is_correct),isEditing:I,isDragging:Y,isOverlay:b}),onClick:function t(){D(true)},onKeyDown:function t(e){e.stopPropagation();if(e.key==="Enter"||e.key===" "){D(true)}}},(0,a.tZ)("div",{css:bo.optionHeader({isEditing:I})},(0,a.tZ)("div",{css:bo.optionCounterAndButton},(0,a.tZ)("div",{css:pt.i.optionCounter({isSelected:!!Number(S.is_correct),isEditing:I})},String.fromCharCode(65+y)),(0,a.tZ)(Z.Z,{when:I},(0,a.tZ)(Z.Z,{when:!z,fallback:(0,a.tZ)(d.Z,{variant:"text",icon:(0,a.tZ)(p.Z,{name:"removeImage",width:24,height:24}),onClick:function t(e){e.stopPropagation();rt()}},(0,u.__)("Remove Image","tutor"))},(0,a.tZ)(d.Z,{variant:"text",icon:(0,a.tZ)(p.Z,{name:"addImage",width:24,height:24}),onClick:function t(e){e.stopPropagation();X()}},(0,u.__)("Add Image","tutor"))))),(0,a.tZ)(Z.Z,{when:!I&&S.is_saved},(0,a.tZ)("button",ro({},R,{type:"button",css:pt.i.optionDragButton({isOverlay:b}),"data-visually-hidden":true}),(0,a.tZ)(p.Z,{name:"dragVertical",height:24,width:24})),(0,a.tZ)("div",{css:bo.optionActions,"data-visually-hidden":true},(0,a.tZ)(ct.Z,{content:(0,u.__)("Edit","tutor"),delay:200},(0,a.tZ)("button",{type:"button",css:pt.i.actionButton,onClick:function t(e){e.stopPropagation();D(true)}},(0,a.tZ)(p.Z,{name:"edit",width:24,height:24}))),(0,a.tZ)(ct.Z,{content:(0,u.__)("Duplicate","tutor"),delay:200},(0,a.tZ)(Z.Z,{when:!yo,fallback:(0,a.tZ)("button",{type:"button",css:pt.i.actionButton,onClick:function t(e){e.stopPropagation();l(S)}},(0,a.tZ)(p.Z,{name:"copyPaste",width:24,height:24}))},(0,a.tZ)(_.Z,{size:"tiny"},(0,a.tZ)("button",{disabled:true,type:"button",css:pt.i.actionButton,onClick:B.ZT},(0,a.tZ)(p.Z,{name:"copyPaste",width:24,height:24}))))),(0,a.tZ)(ct.Z,{content:(0,u.__)("Delete","tutor"),delay:200},(0,a.tZ)("button",{type:"button",css:pt.i.actionButton,onClick:function t(e){e.stopPropagation();f()}},(0,a.tZ)(p.Z,{name:"delete",width:24,height:24})))))),(0,a.tZ)("div",{css:bo.optionBody},(0,a.tZ)(Z.Z,{when:I,fallback:(0,a.tZ)("div",{css:bo.placeholderWrapper},(0,a.tZ)(Z.Z,{when:S.image_url},(0,a.tZ)("div",{css:bo.imagePlaceholder},(0,a.tZ)("img",{src:S.image_url,alt:S.image_url}))),(0,a.tZ)("div",{css:bo.optionPlaceholder},S.answer_title||(0,u.__)("Write answer option...","tutor")))},(0,a.tZ)("div",{css:pt.i.optionInputWrapper},(0,a.tZ)(Z.Z,{when:z},(0,a.tZ)(wn.Z,{value:{id:Number(S.image_id),url:S.image_url||"",title:(0,u.__)("Image","tutor")},buttonText:(0,u.__)("Upload Image","tutor"),infoText:(0,u.__)("Size: 700x430 pixels","tutor"),uploadHandler:X,clearHandler:rt,emptyImageCss:bo.emptyImageInput,previewImageCss:bo.previewImageInput})),(0,a.tZ)("textarea",ro({},c,{ref:j,placeholder:(0,u.__)("Write option...","tutor"),value:S.answer_title,onClick:function t(e){e.stopPropagation()},onChange:function t(e){var r=e.target.value;c.onChange(io(io(io({},S),Qt(S._data_status,Pt.UPDATE)&&{_data_status:Qt(S._data_status,Pt.UPDATE)}),{},{answer_title:r}))},onKeyDown:function(){var t=eo(Xi().mark((function t(e){return Xi().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:e.stopPropagation();if((e.metaKey||e.ctrlKey)&&e.key==="Enter"&&S.answer_title){c.onChange(io(io({},S),Qt(S._data_status,Pt.UPDATE)&&{_data_status:Qt(S._data_status,Pt.UPDATE),is_saved:true}));if((k===null||k===void 0?void 0:k.type)==="save_option"){q(null)}D(false)}case 2:case"end":return r.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()})),(0,a.tZ)("div",{css:bo.optionInputButtons},(0,a.tZ)(d.Z,{variant:"text",size:"small",onClick:function t(e){e.stopPropagation();D(false);c.onChange(Q);if(!S.is_saved){if((k===null||k===void 0?void 0:k.type)==="save_option"){q(null)}f()}}},(0,u.__)("Cancel","tutor")),(0,a.tZ)(d.Z,{variant:"secondary",size:"small",onClick:function(){var t=eo(Xi().mark((function t(e){return Xi().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:e.stopPropagation();c.onChange(io(io(io({},S),Qt(S._data_status,Pt.UPDATE)&&{_data_status:Qt(S._data_status,Pt.UPDATE)}),{},{is_saved:true}));F(io(io(io({},S),Qt(S._data_status,Pt.UPDATE)&&{_data_status:Qt(S._data_status,Pt.UPDATE)}),{},{is_saved:true}));if((k===null||k===void 0?void 0:k.type)==="save_option"){q(null)}D(false);case 5:case"end":return r.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),disabled:!S.answer_title&&!S.image_url},(0,u.__)("Ok","tutor"))))))))};const go=mo;var _o=true?{name:"697o47",styles:"fill:none"}:0;var bo={option:function t(e){var r=e.isSelected,n=e.isMultipleChoice;return(0,a.iv)(pt.i.display.flex(),";",F.c.caption("medium"),";align-items:center;color:",Q.Jv.text.subdued,";gap:",Q.W0[10],";align-items:center;[data-check-button]{color:",Q.Jv.icon["default"],";",!n&&_o," &:focus-visible{opacity:1;}}&:hover{[data-check-button]{opacity:1;}}&:focus-within{[data-check-button]{opacity:1;}}",r&&(0,a.iv)("[data-check-button]{opacity:1;color:",Q.Jv.bg.success,";",!n&&(0,a.iv)("fill:",Q.Jv.bg.success,";"+(true?"":0),true?"":0),";}"+(true?"":0),true?"":0)," ",Q.Uo.smallTablet,"{[data-check-button]{opacity:1;}}"+(true?"":0),true?"":0)},optionLabel:function t(e){var r=e.isSelected,n=e.isEditing,i=e.isDragging,o=e.isOverlay;return(0,a.iv)("display:flex;flex-direction:column;gap:",Q.W0[12],";width:100%;border-radius:",Q.E0.card,";padding:",Q.W0[12]," ",Q.W0[16],";background-color:",Q.Jv.background.white,";[data-visually-hidden]{opacity:0;}&:hover{outline:1px solid ",Q.Jv.stroke.hover,";[data-visually-hidden]{opacity:1;}}",r&&(0,a.iv)("background-color:",Q.Jv.background.success.fill40,";color:",Q.Jv.text.primary,";&:hover{outline:1px solid ",Q.Jv.stroke.success.fill70,";}"+(true?"":0),true?"":0)," ",n&&(0,a.iv)("background-color:",Q.Jv.background.white,";outline:1px solid ",r?Q.Jv.stroke.success.fill70:Q.Jv.stroke.brand,";&:hover{outline:1px solid ",r?Q.Jv.stroke.success.fill70:Q.Jv.stroke.brand,";}"+(true?"":0),true?"":0)," ",i&&(0,a.iv)("background-color:",Q.Jv.stroke.hover,";"+(true?"":0),true?"":0)," ",o&&(0,a.iv)("box-shadow:",Q.AF.drag,";"+(true?"":0),true?"":0)," ",Q.Uo.smallTablet,"{[data-visually-hidden]{opacity:1;}}"+(true?"":0),true?"":0)},optionHeader:function t(e){var r=e.isEditing,n=r===void 0?false:r;return(0,a.iv)("display:grid;grid-template-columns:",!n?"1fr auto 1fr":"1fr",";align-items:center;&:focus-within{[data-visually-hidden]{opacity:1;}}"+(true?"":0),true?"":0)},optionCounterAndButton:(0,a.iv)(pt.i.display.flex()," gap:",Q.W0[8],";place-self:center start;button{padding:0;}"+(true?"":0),true?"":0),optionActions:(0,a.iv)(pt.i.display.flex()," gap:",Q.W0[8],";place-self:center end;"+(true?"":0),true?"":0),optionBody:(0,a.iv)(pt.i.display.flex(),";"+(true?"":0),true?"":0),placeholderWrapper:(0,a.iv)(pt.i.display.flex("column")," gap:",Q.W0[12],";width:100%;"+(true?"":0),true?"":0),imagePlaceholder:(0,a.iv)(pt.i.flexCenter(),";height:210px;width:100%;background-color:",Q.Jv.background["default"],";border-radius:",Q.E0.card,";overflow:hidden;svg{color:",Q.Jv.icon.hints,";}img{width:100%;height:100%;object-fit:cover;object-position:center;}"+(true?"":0),true?"":0),emptyImageInput:(0,a.iv)("background-color:",Q.Jv.background["default"],";height:210px;"+(true?"":0),true?"":0),previewImageInput:true?{name:"be4gwm",styles:"height:210px"}:0,optionPlaceholder:(0,a.iv)(F.c.body(),";color:",Q.Jv.text.subdued,";padding-block:",Q.W0[4],";"+(true?"":0),true?"":0),optionInputButtons:(0,a.iv)(pt.i.display.flex()," justify-content:flex-end;gap:",Q.W0[8],";"+(true?"":0),true?"":0)};function wo(t){"@babel/helpers - typeof";return wo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},wo(t)}function Zo(){Zo=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Zo.apply(this,arguments)}function xo(t){return ko(t)||Eo(t)||Io(t)||Oo()}function Oo(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Eo(t){if(typeof Symbol!=="undefined"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function ko(t){if(Array.isArray(t))return Do(t)}function qo(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function So(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?qo(Object(r),!0).forEach((function(e){jo(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):qo(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function jo(t,e,r){e=Po(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Po(t){var e=Ao(t,"string");return wo(e)==="symbol"?e:String(e)}function Ao(t,e){if(wo(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(wo(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function To(t,e){return Wo(t)||Lo(t,e)||Io(t,e)||Co()}function Co(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Io(t,e){if(!t)return;if(typeof t==="string")return Do(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Do(t,e)}function Do(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Lo(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],s=!0,c=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,i=t}finally{try{if(!s&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw i}}return u}}function Wo(t){if(Array.isArray(t))return t}var zo=function t(){var e=(0,s.useState)(null),r=To(e,2),l=r[0],f=r[1];var v=(0,g.Gc)();var h=We(),y=h.activeQuestionIndex,m=h.activeQuestionId,_=h.validationError,b=h.setValidationError;var w=(0,g.qo)({control:v.control,name:"questions.".concat(y,".question_settings.has_multiple_correct_answer"),defaultValue:false});var x=v.watch("questions.".concat(y,".question_type"));var O=(0,g.Dq)({control:v.control,name:"questions.".concat(y,".question_answers")}),E=O.fields,k=O.append,q=O.insert,S=O.remove,j=O.update,P=O.replace,A=O.move;var T=(0,n.Dy)((0,n.VT)(n.we,{activationConstraint:{distance:10}}),(0,n.VT)(n.Lg,{coordinateGetter:o.is}));var C=(0,g.qo)({control:v.control,name:"questions.".concat(y,".question_answers"),defaultValue:[]});var I=(0,s.useMemo)((function(){if(!l){return null}return E.find((function(t){return t.answer_id===l}))}),[l,E]);var D=function t(e,r){if(w){j(e,So(So(So({},r),Qt(r._data_status,Pt.UPDATE)&&{_data_status:Qt(r._data_status,Pt.UPDATE)}),{},{is_correct:r.is_correct==="1"?"0":"1"}))}else{var n=C.map((function(t){return So(So(So({},t),Qt(t._data_status,Pt.UPDATE)&&{_data_status:Qt(t._data_status,Pt.UPDATE)}),{},{is_correct:t.answer_id===r.answer_id?"1":"0"})}));P(n)}if((_===null||_===void 0?void 0:_.type)==="correct_option"){b(null)}};var L=function t(){k({_data_status:Pt.NEW,is_saved:false,answer_id:(0,B.x0)(),answer_title:"",is_correct:"0",belongs_question_id:m,belongs_question_type:x,answer_order:E.length,answer_two_gap_match:"",answer_view_format:"text"},{shouldFocus:true,focusName:"questions.".concat(y,".question_answers.").concat(E.length,".answer_title")});if((_===null||_===void 0?void 0:_.type)==="add_option"){b(null)}};var W=function t(e,r){var n=So(So({},r),{},{_data_status:Pt.NEW,is_saved:true,answer_id:(0,B.x0)(),answer_title:"".concat(r.answer_title," (copy)"),is_correct:"0"});var i=e+1;q(i,n)};var z=function t(e,r){S(e);if(r._data_status!==Pt.NEW){v.setValue("deleted_answer_ids",[].concat(xo(v.getValues("deleted_answer_ids")),[r.answer_id]))}};return(0,a.tZ)("div",{css:Uo.optionWrapper({isOrdering:x==="ordering"})},(0,a.tZ)(n.LB,{sensors:T,collisionDetection:n.pE,modifiers:[i.hg],onDragStart:function t(e){f(e.active.id)},onDragEnd:function t(e){var r=e.active,n=e.over;if(!n){return}if(r.id!==n.id){var i=E.findIndex((function(t){return t.answer_id===r.id}));var o=E.findIndex((function(t){return t.answer_id===n.id}));A(i,o)}f(null)}},(0,a.tZ)(o.Fo,{items:E.map((function(t){return So(So({},t),{},{id:t.answer_id})})),strategy:o.qw},(0,a.tZ)(Kr.Z,{each:E},(function(t,e){return(0,a.tZ)(g.Qr,{key:"".concat(t.answer_id,"-").concat(t.is_correct),control:v.control,name:"questions.".concat(y,".question_answers.").concat(e),render:function r(n){return(0,a.tZ)(go,Zo({},n,{onDuplicateOption:function t(r){return W(e,r)},onRemoveOption:function r(){return z(e,t)},onCheckCorrectAnswer:function r(){return D(e,t)},index:e}))}})}))),(0,c.createPortal)((0,a.tZ)(n.y9,null,(0,a.tZ)(Z.Z,{when:I},(function(t){var e=C.findIndex((function(e){return e.answer_id===t.answer_id}));return(0,a.tZ)(g.Qr,{key:l,control:v.control,name:"questions.".concat(y,".question_answers.").concat(e),render:function t(r){return(0,a.tZ)(go,Zo({},r,{onDuplicateOption:B.ZT,onRemoveOption:B.ZT,onCheckCorrectAnswer:B.ZT,index:e,isOverlay:true}))}})}))),document.body)),(0,a.tZ)("div",{css:Uo.addOptionButtonWrapper({isOrdering:x==="ordering"})},(0,a.tZ)(d.Z,{variant:"text",onClick:L,buttonContentCss:Uo.addOptionButton,icon:(0,a.tZ)(p.Z,{name:"plus",height:24,width:24})},(0,u.__)("Add Option","tutor"))))};const No=zo;var Uo={optionWrapper:function t(e){var r=e.isOrdering;return(0,a.iv)(pt.i.display.flex("column"),";gap:",Q.W0[12],";",r&&(0,a.iv)("padding-left:",Q.W0[40],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},addOptionButtonWrapper:function t(e){var r=e.isOrdering;return(0,a.iv)("margin-left:",Q.W0[48],";",r&&(0,a.iv)("margin-left:",Q.W0[8],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},addOptionButton:(0,a.iv)("color:",Q.Jv.text.brand,";svg{color:",Q.Jv.icon.brand,";}"+(true?"":0),true?"":0)};var Jo=function t(){return(0,a.tZ)("div",{css:Fo.optionWrapper},(0,a.tZ)(Xe.Z,{icon:"bulb"},(0,u.__)("No options are necessary for this question type","tutor")))};const Qo=Jo;var Fo={optionWrapper:(0,a.iv)("padding-left:",Q.W0[40],";",Q.Uo.smallMobile,"{padding-left:",Q.W0[8],";}"+(true?"":0),true?"":0)};function Mo(){Mo=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Mo.apply(this,arguments)}var Bo=function t(e){var r;var n=e.field,i=e.onCheckCorrectAnswer,u=e.isOverlay,s=u===void 0?false:u;var c=We(),l=c.activeQuestionId;var d=(r=n.value)!==null&&r!==void 0?r:{answer_id:(0,B.x0)(),answer_title:"",is_correct:"0",belongs_question_id:l,belongs_question_type:"true_false"};var f=(0,o.nB)({id:n.value.answer_id||0,animateLayoutChanges:xn.h}),v=f.attributes,y=f.listeners,m=f.setNodeRef,g=f.transform,_=f.transition,b=f.isDragging;var w={transform:h.ux.Transform.toString(g),transition:_,opacity:b?.3:undefined};return(0,a.tZ)("div",Mo({},v,{css:Ro.option({isSelected:!!Number(n.value.is_correct),isOverlay:s}),tabIndex:-1,ref:m,style:w}),(0,a.tZ)("button",{"data-check-button":true,type:"button",css:pt.i.optionCheckButton,onClick:i},(0,a.tZ)(p.Z,{name:Number(n.value.is_correct)?"checkFilled":"check",height:32,width:32})),(0,a.tZ)("div",{css:Ro.optionLabel({isSelected:!!Number(n.value.is_correct),isDragging:b,isOverlay:s})},(0,a.tZ)("span",null,d.answer_title),(0,a.tZ)("button",Mo({},y,{type:"button",css:pt.i.optionDragButton({isOverlay:s}),"data-visually-hidden":true}),(0,a.tZ)(p.Z,{name:"dragVertical",height:24,width:24}))))};const Go=Bo;var Ro={option:function t(e){var r=e.isSelected,n=e.isOverlay;return(0,a.iv)(pt.i.display.flex(),";",F.c.caption("medium"),";align-items:center;color:",Q.Jv.text.subdued,";gap:",Q.W0[10],";height:48px;align-items:center;[data-check-button]{color:",Q.Jv.icon["default"],";opacity:0;fill:none;flex-shrink:0;&:focus-visible{opacity:1;}}&:focus-within{[data-check-button]{opacity:1;}}&:hover{[data-check-button]{opacity:",n?0:1,";}}",r&&(0,a.iv)("[data-check-button]{opacity:1;color:",Q.Jv.bg.success,";}"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},optionLabel:function t(e){var r=e.isSelected,n=e.isDragging,i=e.isOverlay;return(0,a.iv)("display:grid;grid-template-columns:1fr auto 1fr;align-items:center;width:100%;border-radius:",Q.E0.card,";padding:",Q.W0[12]," ",Q.W0[16],";background-color:",Q.Jv.background.white,";text-transform:capitalize;[data-visually-hidden]{opacity:0;}&:hover{outline:1px solid ",Q.Jv.stroke.hover,";[data-visually-hidden]{opacity:1;}}&:focus-within{[data-visually-hidden]{opacity:1;}}",r&&(0,a.iv)("background-color:",Q.Jv.background.success.fill40,";color:",Q.Jv.text.primary,";&:hover{outline:1px solid ",Q.Jv.stroke.success.fill70,";}"+(true?"":0),true?"":0)," ",n&&(0,a.iv)("background-color:",Q.Jv.stroke.hover,";"+(true?"":0),true?"":0)," ",i&&(0,a.iv)("box-shadow:",Q.AF.drag,";"+(true?"":0),true?"":0)," ",Q.Uo.smallTablet,"{[data-visually-hidden]{opacity:1;}}"+(true?"":0),true?"":0)}};function Vo(t){"@babel/helpers - typeof";return Vo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Vo(t)}function Ho(){Ho=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Ho.apply(this,arguments)}function Ko(t,e){var r=typeof Symbol!=="undefined"&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=sa(t))||e&&t&&typeof t.length==="number"){if(r)t=r;var n=0;var i=function t(){};return{s:i,n:function e(){if(n>=t.length)return{done:true};return{done:false,value:t[n++]}},e:function t(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o=true,a=false,u;return{s:function e(){r=r.call(t)},n:function t(){var e=r.next();o=e.done;return e},e:function t(e){a=true;u=e},f:function t(){try{if(!o&&r["return"]!=null)r["return"]()}finally{if(a)throw u}}}}function Yo(t){return ta(t)||Xo(t)||sa(t)||$o()}function $o(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Xo(t){if(typeof Symbol!=="undefined"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function ta(t){if(Array.isArray(t))return ca(t)}function ea(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ra(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ea(Object(r),!0).forEach((function(e){na(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ea(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function na(t,e,r){e=ia(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function ia(t){var e=oa(t,"string");return Vo(e)==="symbol"?e:String(e)}function oa(t,e){if(Vo(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Vo(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function aa(t,e){return da(t)||la(t,e)||sa(t,e)||ua()}function ua(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function sa(t,e){if(!t)return;if(typeof t==="string")return ca(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ca(t,e)}function ca(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function la(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],s=!0,c=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,i=t}finally{try{if(!s&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw i}}return u}}function da(t){if(Array.isArray(t))return t}var fa=function t(){var e=(0,s.useState)(null),r=aa(e,2),u=r[0],l=r[1];var d=(0,g.Gc)();var f=We(),p=f.activeQuestionIndex;var v=(0,g.Dq)({control:d.control,name:"questions.".concat(p,".question_answers")}),h=v.fields,y=v.move,m=v.replace;var _=(0,n.Dy)((0,n.VT)(n.we,{activationConstraint:{distance:10}}),(0,n.VT)(n.Lg,{coordinateGetter:o.is}));var b=(0,g.qo)({control:d.control,name:"questions.".concat(p,".question_answers"),defaultValue:[]});var w=(0,s.useMemo)((function(){if(!u){return null}return h.find((function(t){return t.answer_id===u}))}),[u,h]);var x=function t(e,r){var n=b.map((function(t){return ra(ra(ra({},t),Qt(t._data_status,Pt.UPDATE)&&{_data_status:Qt(t._data_status,Pt.UPDATE)}),{},{is_correct:t.answer_id===r.answer_id?"1":"0"})}));m(n)};(0,s.useEffect)((function(){var t=b.filter((function(t){var e=h.findIndex((function(e){return e.answer_id===t.answer_id}));var r=h[e]||{};return t.is_correct!==r.is_correct}));if(t.length===0){return}var e=h.findIndex((function(e){return e.answer_id===t[0].answer_id}));var r=Yo(b);r[e]=Object.assign({},r[e],{is_correct:"1"});var n=Ko(r.entries()),i;try{for(n.s();!(i=n.n()).done;){var o=aa(i.value,2),a=o[0],u=o[1];if(a!==e){r[a]=ra(ra(ra({},u),Qt(u._data_status,Pt.UPDATE)&&{_data_status:Qt(u._data_status,Pt.UPDATE)}),{},{is_correct:"0"})}}}catch(t){n.e(t)}finally{n.f()}d.setValue("questions.".concat(p,".question_answers"),r)}),[b]);return(0,a.tZ)("div",{css:va.optionWrapper},(0,a.tZ)(n.LB,{sensors:_,collisionDetection:n.pE,modifiers:[i.hg],onDragStart:function t(e){l(e.active.id)},onDragEnd:function t(e){var r=e.active,n=e.over;if(!n){return}if(r.id!==n.id){var i=h.findIndex((function(t){return t.answer_id===r.id}));var o=h.findIndex((function(t){return t.answer_id===n.id}));y(i,o)}l(null)}},(0,a.tZ)(o.Fo,{items:h.map((function(t){return ra(ra({},t),{},{id:t.answer_id})})),strategy:o.qw},(0,a.tZ)(Kr.Z,{each:h},(function(t,e){return(0,a.tZ)(g.Qr,{key:"".concat(t.answer_id,"-").concat(t.is_correct),control:d.control,name:"questions.".concat(p,".question_answers.").concat(e),render:function r(n){return(0,a.tZ)(Go,Ho({},n,{index:e,onCheckCorrectAnswer:function r(){return x(e,t)}}))}})}))),(0,c.createPortal)((0,a.tZ)(n.y9,null,(0,a.tZ)(Z.Z,{when:w},(function(t){var e=h.findIndex((function(e){return e.answer_id===t.answer_id}));return(0,a.tZ)(g.Qr,{key:u,control:d.control,name:"questions.".concat(p,".question_answers.").concat(e),render:function t(r){return(0,a.tZ)(Go,Ho({},r,{index:e,onCheckCorrectAnswer:B.ZT,isOverlay:true}))}})}))),document.body)))};const pa=fa;var va={optionWrapper:(0,a.iv)(pt.i.display.flex("column"),";gap:",Q.W0[12],";"+(true?"":0),true?"":0),option:function t(e){var r=e.isSelected;return(0,a.iv)(pt.i.display.flex(),";",F.c.caption("medium"),";align-items:center;color:",Q.Jv.text.subdued,";gap:",Q.W0[10],";height:48px;align-items:center;[data-check-icon]{opacity:0;transition:opacity 0.15s ease-in-out;fill:none;flex-shrink:0;}&:hover{[data-check-icon]{opacity:1;}}",r&&(0,a.iv)("[data-check-icon]{opacity:1;color:",Q.Jv.bg.success,";}"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},optionLabel:function t(e){var r=e.isSelected;return(0,a.iv)("display:grid;grid-template-columns:1fr auto 1fr;align-items:center;width:100%;border-radius:",Q.E0.card,";padding:",Q.W0[12]," ",Q.W0[16],";background-color:",Q.Jv.background.white,";cursor:pointer;[data-visually-hidden]{opacity:0;}&:hover{box-shadow:0 0 0 1px ",Q.Jv.stroke.hover,";[data-visually-hidden]{opacity:1;}}",r&&(0,a.iv)("background-color:",Q.Jv.background.success.fill40,";color:",Q.Jv.text.primary,";&:hover{box-shadow:0 0 0 1px ",Q.Jv.stroke.success.fill70,";}"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},optionDragButton:(0,a.iv)(pt.i.resetButton," ",pt.i.flexCenter()," transform:rotate(90deg);color:",Q.Jv.icon["default"],";cursor:grab;place-self:center center;"+(true?"":0),true?"":0)};var ha=r(2460);const ya=r.p+"images/9e5f8c5e5307063e90cc7f2a55e50d8c-quiz-empty-state-2x.webp";const ma=r.p+"images/bdb31b4692f8bb5ee2633ce7abb0356d-quiz-empty-state.webp";function ga(){ga=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return ga.apply(this,arguments)}function _a(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var ba=!!U.y.tutor_pro_url;var wa=function t(){var e=We(),r=e.activeQuestionIndex,n=e.activeQuestionId,i=e.validationError,o=e.contentType;var c=(0,ha.D)(n);var l=(0,g.Gc)();var d=(0,s.useRef)(null);var f=l.watch("questions.".concat(r,".question_type"));var p=l.watch("questions")||[];var v=l.watch("questions.".concat(r,"._data_status"));var h={true_false:(0,a.tZ)(pa,{key:n}),multiple_choice:(0,a.tZ)(No,{key:n}),open_ended:(0,a.tZ)(Qo,{key:n}),fill_in_the_blank:(0,a.tZ)(_n,{key:n}),short_answer:(0,a.tZ)(Qo,{key:n}),matching:(0,a.tZ)(Ki,{key:n}),image_answering:(0,a.tZ)(ci,{key:n}),ordering:(0,a.tZ)(No,{key:n})};(0,s.useEffect)((function(){if(i&&d.current){d.current.scrollIntoView({behavior:"smooth",block:"center",inline:"center"})}}),[i]);if(!n&&!l.formState.isLoading&&p.length===0){return(0,a.tZ)("div",{css:xa.emptyState},(0,a.tZ)("img",{css:xa.emptyStateImage,src:ma,srcSet:"".concat(ma," 1x, ").concat(ya," 2x"),alt:""}),(0,a.tZ)("p",{css:xa.emptyStateText},(0,u.__)("Enter a quiz title to begin. Choose from a variety of question types to keep things interesting!","tutor")))}return(0,a.tZ)("div",{key:r,css:xa.questionForm(n===c)},(0,a.tZ)("div",{css:xa.questionWithIndex},(0,a.tZ)("div",{css:xa.questionIndex},r+1,"."),(0,a.tZ)("div",{css:xa.questionTitleAndDesc},(0,a.tZ)(g.Qr,{control:l.control,rules:{required:(0,u.__)("Question title is required","tutor")},name:"questions.".concat(r,".question_title"),render:function t(e){var n;return(0,a.tZ)(Rr,ga({},e,{placeholder:(0,u.__)("Write your question here..","tutor"),disabled:o==="tutor_h5p_quiz",onChange:function t(){if(Qt(v,Pt.UPDATE)){l.setValue("questions.".concat(r,"._data_status"),Qt(v,Pt.UPDATE))}},selectOnFocus:((n=p[r])===null||n===void 0?void 0:n._data_status)===Pt.NEW}))}}),(0,a.tZ)(Z.Z,{when:o!=="tutor_h5p_quiz",fallback:(0,a.tZ)("div",null,(0,a.tZ)("div",{css:xa.h5pShortCode},'[h5p id: "'.concat(l.watch("questions.".concat(r,".question_description")),'"]')))},(0,a.tZ)(g.Qr,{control:l.control,name:"questions.".concat(r,".question_description"),render:function t(e){return(0,a.tZ)(Or,ga({},e,{placeholder:(0,u.__)("Description (optional)","tutor"),disabled:o==="tutor_h5p_quiz",onChange:function t(){if(Qt(v,Pt.UPDATE)){l.setValue("questions.".concat(r,"._data_status"),Qt(v,Pt.UPDATE))}}}))}})),(0,a.tZ)(L.Z,{section:"Curriculum.Quiz.after_question_description",namePrefix:"questions.".concat(r,"."),form:l}))),(0,a.tZ)(Z.Z,{when:i},(0,a.tZ)("div",{key:Math.random(),ref:d,css:xa.alertWrapper},(0,a.tZ)(Xe.Z,{type:"danger",icon:"warning"},i===null||i===void 0?void 0:i.message))),h[f],(0,a.tZ)(Z.Z,{when:ba&&f!=="h5p"},(0,a.tZ)("div",{css:xa.questionAnswer},(0,a.tZ)(g.Qr,{control:l.control,name:"questions.".concat(r,".answer_explanation"),render:function t(e){return(0,a.tZ)(sr,ga({},e,{label:(0,u.__)("Answer Explanation","tutor"),placeholder:(0,u.__)("Write answer explanation...","tutor"),onChange:function t(){if(Qt(v,Pt.UPDATE)){l.setValue("questions.".concat(r,"._data_status"),Qt(v,Pt.UPDATE))}}}))}}))))};const Za=wa;var xa={questionForm:function t(e){return(0,a.iv)(pt.i.display.flex("column"),";padding-right:",Q.W0[48],";gap:",Q.W0[16],";animation:",e?undefined:"fadeIn 0.25s ease-in-out",";@keyframes fadeIn{from{opacity:0;}to{opacity:1;}}",Q.Uo.smallMobile,"{padding-right:",Q.W0[8],";}"+(true?"":0),true?"":0)},questionWithIndex:(0,a.iv)(pt.i.display.flex("row"),";align-items:flex-start;padding-left:",Q.W0[40],";gap:",Q.W0[4],";",Q.Uo.smallMobile,"{padding-left:",Q.W0[8],";}"+(true?"":0),true?"":0),questionIndex:(0,a.iv)("margin-top:",Q.W0[10],";",F.c.heading6(),";color:",Q.Jv.text.hints,";"+(true?"":0),true?"":0),questionTitleAndDesc:(0,a.iv)(pt.i.display.flex("column"),";gap:",Q.W0[8],";width:100%;"+(true?"":0),true?"":0),h5pShortCode:(0,a.iv)("margin-left:",Q.W0[8],";padding:",Q.W0[4]," ",Q.W0[8],";",F.c.caption(),";color:",Q.Jv.text.white,";background-color:#2575be;border-radius:",Q.E0.card,";width:fit-content;font-family:'Fire Code',monospace;"+(true?"":0),true?"":0),questionAnswer:(0,a.iv)("padding-left:",Q.W0[40],";",Q.Uo.smallMobile,"{padding-left:",Q.W0[8],";}"+(true?"":0),true?"":0),emptyState:(0,a.iv)(pt.i.flexCenter("column"),";padding-left:",Q.W0[40],";padding-right:",Q.W0[48],";gap:",Q.W0[16],";"+(true?"":0),true?"":0),emptyStateImage:true?{name:"vdj5lp",styles:"width:220px;height:auto"}:0,emptyStateText:(0,a.iv)(F.c.small(),";color:",Q.Jv.text.subdued,";text-align:center;max-width:330px;"+(true?"":0),true?"":0),alertWrapper:(0,a.iv)("padding-left:",Q.W0[40],";animation:shake 0.3s linear;@keyframes shake{0%{transform:translateX(0);}25%{transform:translateX(-5px);}50%{transform:translateX(5px);}75%{transform:translateX(-5px);}100%{transform:translateX(0);}}"+(true?"":0),true?"":0)};var Oa=r(3366);function Ea(){Ea=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Ea.apply(this,arguments)}function ka(t,e){return Aa(t)||Pa(t,e)||Sa(t,e)||qa()}function qa(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Sa(t,e){if(!t)return;if(typeof t==="string")return ja(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ja(t,e)}function ja(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Pa(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],s=!0,c=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,i=t}finally{try{if(!s&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw i}}return u}}function Aa(t){if(Array.isArray(t))return t}function Ta(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Ca={true_false:"quizTrueFalse",multiple_choice:"quizMultiChoice",open_ended:"quizEssay",fill_in_the_blank:"quizFillInTheBlanks",short_answer:"quizShortAnswer",matching:"quizImageMatching",image_answering:"quizImageAnswer",ordering:"quizOrdering",h5p:"quizH5p"};var Ia=!!U.y.tutor_pro_url;var Da=function t(e){var r=e.question,n=e.index,i=e.onDuplicateQuestion,c=e.onRemoveQuestion,l=e.isOverlay,d=l===void 0?false:l;var f=We(),v=f.activeQuestionIndex,y=f.activeQuestionId,m=f.validationError,b=f.setActiveQuestionId,w=f.setValidationError,Z=f.contentType;var x=(0,g.Gc)();var O=(0,s.useState)(false),k=ka(O,2),q=k[0],S=k[1];var j=(0,s.useRef)(null);var P=(0,o.nB)({id:r.question_id,animateLayoutChanges:xn.h}),A=P.attributes,T=P.listeners,C=P.setNodeRef,I=P.transform,D=P.transition,L=P.isDragging;var W={transform:h.ux.Transform.toString(I),transition:D,opacity:L?.3:undefined,background:L?Q.Jv.stroke.hover:undefined};(0,s.useEffect)((function(){if(y===r.question_id){var t;(t=j.current)===null||t===void 0?void 0:t.scrollIntoView({behavior:"smooth",block:"center",inline:"center"})}}),[y,r.question_id]);return(0,a.tZ)("div",Ea({},A,{key:r.question_id,css:za.questionItem({isActive:String(y)===String(r.question_id),isDragging:d,isThreeDotsOpen:q}),ref:function t(e){C(e);j.current=e},style:W,onClick:function t(){if(y===r.question_id){return}var e=(0,N.aU)(v,x);if(e!==true){w(e);return}w(null);b(r.question_id)},onKeyDown:function t(e){if(e.key==="Enter"||e.key===" "){if(y===r.question_id){return}var n=(0,N.aU)(v,x);if(n!==true){w(n);return}w(null);b(r.question_id)}}}),(0,a.tZ)("div",{css:za.iconAndSerial({isDragging:d}),"data-icon-serial":true},(0,a.tZ)("span",{"data-serial":true},n+1),(0,a.tZ)("button",Ea({"data-drag-icon":true},T,{type:"button",css:pt.i.resetButton}),(0,a.tZ)(p.Z,{"data-drag-icon":true,name:"dragVertical",width:24,height:24})),(0,a.tZ)(p.Z,{name:Ca[r.question_type],width:24,height:24,"data-question-icon":true})),(0,a.tZ)("span",{css:za.questionTitle({isActive:String(y)===String(r.question_id)})},r.question_title),(0,a.tZ)(E.Z,{isOpen:q,onClick:function t(e){e.stopPropagation();var r=(0,N.aU)(v,x);S(true);if(r!==true){w(r)}},animationType:Oa.ru.slideDown,closePopover:function t(){return S(false)},dotsOrientation:"vertical",maxWidth:Ia?"150px":"160px",isInverse:true,arrowPosition:"auto",size:"small",hideArrow:true,"data-three-dots":true},!m&&Z!=="tutor_h5p_quiz"&&(0,a.tZ)(E.Z.Option,{text:(0,a.tZ)("div",{css:za.duplicate},(0,u.__)("Duplicate","tutor"),!Ia&&(0,a.tZ)(_.Z,{size:"small",content:(0,u.__)("Pro","tutor")})),icon:(0,a.tZ)(p.Z,{name:"duplicate",width:24,height:24}),disabled:!Ia,onClick:function t(e){e.stopPropagation();i(r);S(false)}}),(0,a.tZ)(E.Z.Option,{isTrash:true,text:(0,u.__)("Delete","tutor"),icon:(0,a.tZ)(p.Z,{name:"delete",width:24,height:24}),onClick:function t(e){e.stopPropagation();c();S(false)}})))};const La=Da;var Wa=true?{name:"1c7c6p3",styles:"[data-three-dots]{opacity:1;}"}:0;var za={questionItem:function t(e){var r=e.isActive,n=r===void 0?false:r,i=e.isDragging,o=i===void 0?false:i,u=e.isThreeDotsOpen,s=u===void 0?false:u;return(0,a.iv)("padding:",Q.W0[10]," ",Q.W0[8]," ",Q.W0[10]," ",Q.W0[28],";display:flex;align-items:center;justify-content:space-between;gap:",Q.W0[12],";border-bottom:1px solid ",Q.Jv.stroke.divider,";cursor:pointer;transition:border 0.3s ease-in-out,background-color 0.3s ease-in-out;[data-three-dots]{opacity:0;background:transparent;svg{color:",Q.Jv.icon["default"],";}:focus-visible{opacity:1;}}",n&&(0,a.iv)("color:",Q.Jv.text.brand,";background-color:",Q.Jv.background.white,";[data-icon-serial]{border-top-right-radius:3px;border-bottom-right-radius:3px;border-color:transparent;}"+(true?"":0),true?"":0)," ",s&&Wa," :hover{background-color:",Q.Jv.background.hover,";[data-serial]{display:none;}[data-drag-icon]{display:block;}[data-icon-serial]{border-top-right-radius:3px;border-bottom-right-radius:3px;border-color:transparent;}[data-three-dots]{opacity:1;}}:focus-visible{outline:2px solid ",Q.Jv.stroke.brand,";outline-offset:-2px;border-radius:",Q.E0.card,";[data-three-dots]{opacity:1;}}",o&&(0,a.iv)("box-shadow:",Q.AF.drag,";background-color:",Q.Jv.background.white,";border-radius:",Q.E0.card,";:hover{background-color:",Q.Jv.background.white,";}"+(true?"":0),true?"":0)," ",Q.Uo.smallMobile,"{padding:",Q.W0[8]," ",Q.W0[8]," ",Q.W0[8]," ",Q.W0[8],";[data-three-dots]{opacity:1;}}"+(true?"":0),true?"":0)},iconAndSerial:function t(e){var r=e.isDragging,n=r===void 0?false:r;return(0,a.iv)("display:grid;grid-template-columns:1fr 1fr;align-items:center;border-radius:3px 0 0 3px;width:64px;padding:",Q.W0[4]," ",Q.W0[8]," ",Q.W0[4]," ",Q.W0[4],";flex-shrink:0;column-gap:",Q.W0[12],";place-items:center center;[data-drag-icon]{display:none;color:",Q.Jv.icon.hints,";cursor:",n?"grabbing":"grab",";}[data-question-icon]{flex-shrink:0;}svg{flex-shrink:0;}[data-serial]{width:24px;display:block;",F.c.caption("medium")," text-align:center;flex-grow:1;}"+(true?"":0),true?"":0)},questionTitle:function t(e){var r=e.isActive,n=r===void 0?false:r;return(0,a.iv)(F.c.small(n?"medium":"regular"),";color:",n?Q.Jv.text.brand:Q.Jv.text.subdued,";flex-grow:1;"+(true?"":0),true?"":0)},duplicate:(0,a.iv)("display:flex;align-items:center;gap:",Q.W0[4],";"+(true?"":0),true?"":0)};function Na(t){"@babel/helpers - typeof";return Na="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Na(t)}function Ua(t){return Fa(t)||Qa(t)||$a(t)||Ja()}function Ja(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Qa(t){if(typeof Symbol!=="undefined"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function Fa(t){if(Array.isArray(t))return Xa(t)}function Ma(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Ba(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ma(Object(r),!0).forEach((function(e){Ga(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ma(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Ga(t,e,r){e=Ra(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Ra(t){var e=Va(t,"string");return Na(e)==="symbol"?e:String(e)}function Va(t,e){if(Na(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Na(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Ha(t,e){var r=typeof Symbol!=="undefined"&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=$a(t))||e&&t&&typeof t.length==="number"){if(r)t=r;var n=0;var i=function t(){};return{s:i,n:function e(){if(n>=t.length)return{done:true};return{done:false,value:t[n++]}},e:function t(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o=true,a=false,u;return{s:function e(){r=r.call(t)},n:function t(){var e=r.next();o=e.done;return e},e:function t(e){a=true;u=e},f:function t(){try{if(!o&&r["return"]!=null)r["return"]()}finally{if(a)throw u}}}}function Ka(t,e){return eu(t)||tu(t,e)||$a(t,e)||Ya()}function Ya(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function $a(t,e){if(!t)return;if(typeof t==="string")return Xa(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Xa(t,e)}function Xa(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function tu(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],s=!0,c=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,i=t}finally{try{if(!s&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw i}}return u}}function eu(t){if(Array.isArray(t))return t}var ru=[{label:(0,u.__)("True/False","tutor"),value:"true_false",icon:"quizTrueFalse",isPro:false},{label:(0,u.__)("Multiple Choice","tutor"),value:"multiple_choice",icon:"quizMultiChoice",isPro:false},{label:(0,u.__)("Open Ended/ Essay","tutor"),value:"open_ended",icon:"quizEssay",isPro:false},{label:(0,u.__)("Fill in the Blanks","tutor"),value:"fill_in_the_blank",icon:"quizFillInTheBlanks",isPro:false},{label:(0,u.__)("Short Answer","tutor"),value:"short_answer",icon:"quizShortAnswer",isPro:true},{label:(0,u.__)("Matching","tutor"),value:"matching",icon:"quizImageMatching",isPro:true},{label:(0,u.__)("Image Answering","tutor"),value:"image_answering",icon:"quizImageAnswer",isPro:true},{label:(0,u.__)("Ordering","tutor"),value:"ordering",icon:"quizOrdering",isPro:true}];var nu=!!U.y.tutor_pro_url;var iu=function t(e){var r=e.isEditing;var l=(0,s.useState)(null),d=Ka(l,2),f=d[0],v=d[1];var h=(0,s.useState)(false),y=Ka(h,2),m=y[0],b=y[1];var x=(0,s.useRef)(null);var E=(0,s.useRef)(null);var k=(0,g.Gc)();var q=We(),S=q.contentType,j=q.activeQuestionIndex,P=q.validationError,A=q.setActiveQuestionId,T=q.setValidationError;var C=(0,g.Dq)({control:k.control,name:"questions"}),I=C.remove,D=C.append,L=C.insert,W=C.move,z=C.fields;var U=(0,w.d)(),Q=U.showModal;var F=(0,n.Dy)((0,n.VT)(n.we,{activationConstraint:{distance:10}}),(0,n.VT)(n.Lg,{coordinateGetter:o.is}));var M=(0,s.useMemo)((function(){if(!f){return null}return z.find((function(t){return t.question_id===f}))}),[f,z]);var G=k.watch("questions")||[];var R=function t(e,r){var n=(0,N.aU)(j,k);if(n!==true){T(n);b(false);return}var i=(0,B.x0)();D({_data_status:Pt.NEW,question_id:i,question_title:e==="h5p"?r===null||r===void 0?void 0:r.title:(0,u.sprintf)((0,u.__)("Question %d","tutor"),z.length+1),question_description:e==="h5p"?r===null||r===void 0?void 0:r.id:"",question_type:e,question_answers:e==="true_false"?[{answer_id:(0,B.x0)(),_data_status:Pt.NEW,is_saved:true,answer_title:(0,u.__)("True","tutor"),is_correct:"1",answer_order:1,answer_two_gap_match:"",answer_view_format:"text",belongs_question_id:i,belongs_question_type:"true_false"},{answer_id:(0,B.x0)(),is_saved:true,_data_status:Pt.NEW,answer_title:(0,u.__)("False","tutor"),is_correct:"0",answer_order:2,answer_two_gap_match:"",answer_view_format:"text",belongs_question_id:i,belongs_question_type:"true_false"}]:e==="fill_in_the_blank"?[{_data_status:Pt.NEW,is_saved:false,answer_id:(0,B.x0)(),answer_title:"",belongs_question_id:i,belongs_question_type:"fill_in_the_blank",answer_two_gap_match:"",answer_view_format:"",answer_order:0,is_correct:"0"}]:[],answer_explanation:"",question_mark:1,question_order:z.length+1,question_settings:{answer_required:false,question_mark:S==="tutor_h5p_quiz"?0:1,question_type:e,randomize_question:false,show_question_mark:false}});T(null);A(i);b(false)};var V=function t(e){var r=Ha(e),n;try{for(r.s();!(n=r.n()).done;){var i=n.value;R("h5p",i)}}catch(t){r.e(t)}finally{r.f()}};var H=function t(e,r){var n=k.watch("questions.".concat(r));if(!n||P){return}var i=Ba(Ba({},e),{},{question_id:(0,B.x0)(),_data_status:Pt.NEW,question_title:"".concat(n.question_title," (copy)"),question_answers:n.question_answers.map((function(t){return Ba(Ba({},t),{},{answer_id:(0,B.x0)(),_data_status:Pt.NEW})}))});var o=r+1;L(o,i)};var K=function t(e,r){I(e);if(j===e){A("");T(null)}if(r._data_status!==Pt.NEW){k.setValue("deleted_question_ids",[].concat(Ua(k.getValues("deleted_question_ids")),[r.question_id]))}};var Y=function t(e){var r=e.active,n=e.over;if(!n||r.id===n.id){return}var i=z.findIndex((function(t){return t.question_id===r.id}));var o=z.findIndex((function(t){return t.question_id===n.id}));W(i,o)};(0,s.useEffect)((function(){if(x.current){x.current.style.maxHeight="".concat(window.innerHeight-x.current.getBoundingClientRect().top,"px")}}),[x.current,r]);if(!k.getValues("quiz_title")){return null}return(0,a.tZ)("div",null,(0,a.tZ)("div",{css:au.questionsLabel},(0,a.tZ)("span",null,(0,u.__)("Questions","tutor")),(0,a.tZ)("button",{ref:E,type:"button",onClick:function t(){if(S==="tutor_h5p_quiz"){Q({component:ce,props:{title:(0,u.__)("Select H5P Content","tutor"),onAddContent:function t(e){V(e)},contentType:"tutor_h5p_quiz",addedContentIds:G.map((function(t){return t.question_description}))}})}else{b(true)}}},(0,a.tZ)(p.Z,{name:"plusSquareBrand",width:32,height:32}))),(0,a.tZ)("div",{ref:x,css:au.questionList},(0,a.tZ)(Z.Z,{when:G.length>0,fallback:(0,a.tZ)("div",{css:au.emptyQuestionText},(0,u.__)("No questions added yet.","tutor"))},(0,a.tZ)(n.LB,{sensors:F,collisionDetection:n.pE,modifiers:[i.hg],onDragStart:function t(e){v(e.active.id)},onDragEnd:function t(e){return Y(e)}},(0,a.tZ)(o.Fo,{items:G.map((function(t){return Ba(Ba({},t),{},{id:t.question_id})})),strategy:o.qw},(0,a.tZ)(Kr.Z,{each:G},(function(t,e){return(0,a.tZ)(La,{key:t.question_id,question:t,index:e,onDuplicateQuestion:function t(r){H(r,e)},onRemoveQuestion:function r(){return K(e,t)}})}))),(0,c.createPortal)((0,a.tZ)(n.y9,null,(0,a.tZ)(Z.Z,{when:M},(function(t){var e=z.findIndex((function(e){return e.question_id===t.question_id}));return(0,a.tZ)(La,{key:t.question_id,question:t,index:e,onDuplicateQuestion:B.ZT,onRemoveQuestion:B.ZT,isOverlay:true})}))),document.body))),(0,a.tZ)(O.Z,{gap:4,maxWidth:"240px",arrow:J.iM.isAboveTablet?"top":J.iM.isAboveMobile?"right":"absoluteCenter",triggerRef:E,isOpen:m,closePopover:function t(){return b(false)},animationType:Oa.ru.slideUp},(0,a.tZ)("div",{css:au.questionOptionsWrapper},(0,a.tZ)("span",{css:au.questionTypeOptionsTitle},(0,u.__)("Select Question Type","tutor")),ru.map((function(t){return(0,a.tZ)(Z.Z,{key:t.value,when:t.isPro&&!nu,fallback:(0,a.tZ)("button",{key:t.value,type:"button",css:au.questionTypeOption,onClick:function e(){R(t.value)}},(0,a.tZ)(p.Z,{name:t.icon,width:24,height:24}),(0,a.tZ)("span",null,t.label))},(0,a.tZ)("button",{key:t.value,type:"button",css:au.questionTypeOption,disabled:true,onClick:B.ZT},(0,a.tZ)(p.Z,{"data-question-icon":true,name:t.icon,width:24,height:24}),(0,a.tZ)("span",null,t.label),(0,a.tZ)(_.Z,{size:"small",content:(0,u.__)("Pro","tutor")})))}))))))};const ou=iu;var au={questionsLabel:(0,a.iv)("display:flex;gap:",Q.W0[4],";align-items:center;justify-content:space-between;border-bottom:1px solid ",Q.Jv.stroke.divider,";padding:",Q.W0[16]," ",Q.W0[16]," ",Q.W0[16]," ",Q.W0[28],";",F.c.caption("medium"),";color:",Q.Jv.text.subdued,";button{",pt.i.resetButton,";width:32px;height:32px;border-radius:",Q.E0[6],";svg{color:",Q.Jv.action.primary["default"],";width:100%;height:100%;}:focus-visible{outline:2px solid ",Q.Jv.stroke.brand,";}}",Q.Uo.smallMobile,"{padding:",Q.W0[16],";}"+(true?"":0),true?"":0),questionList:(0,a.iv)(pt.i.overflowYAuto,";scrollbar-gutter:auto;padding:",Q.W0[8]," 0 ",Q.W0[8]," 0;"+(true?"":0),true?"":0),questionTypeOptionsTitle:(0,a.iv)(F.c.caption("medium"),";color:",Q.Jv.text.subdued,";padding:",Q.W0[8]," ",Q.W0[16]," ",Q.W0[8]," ",Q.W0[20],";border-bottom:1px solid ",Q.Jv.stroke.divider,";"+(true?"":0),true?"":0),questionOptionsWrapper:(0,a.iv)("display:flex;flex-direction:column;padding-block:",Q.W0[6],";"+(true?"":0),true?"":0),questionTypeOption:(0,a.iv)(pt.i.resetButton,";width:100%;padding:",Q.W0[8]," ",Q.W0[16]," ",Q.W0[8]," ",Q.W0[20],";transition:background-color 0.3s ease-in-out;display:flex;align-items:center;gap:",Q.W0[4],";border:2px solid transparent;:disabled{cursor:not-allowed;color:",Q.Jv.text.primary,";[data-question-icon]{filter:grayscale(100%);}}:hover:enabled{background-color:",Q.Jv.background.hover,";color:",Q.Jv.text.title,";}:focus:enabled,:active:enabled{border-color:",Q.Jv.stroke.brand,";}"+(true?"":0),true?"":0),emptyQuestionText:(0,a.iv)(F.c.small(),";color:",Q.Jv.text.subdued,";padding:",Q.W0[8]," ",Q.W0[16]," ",Q.W0[8]," ",Q.W0[28],";"+(true?"":0),true?"":0)};var uu=r(3224);function su(t){"@babel/helpers - typeof";return su="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},su(t)}function cu(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function lu(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?cu(Object(r),!0).forEach((function(e){du(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):cu(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function du(t,e,r){e=fu(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function fu(t){var e=pu(t,"string");return su(e)==="symbol"?e:String(e)}function pu(t,e){if(su(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(su(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function vu(){vu=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return vu.apply(this,arguments)}var hu=(0,N.zs)();var yu=function t(e){var r=e.contentDripType;var n=We(),i=n.quizId,o=n.contentType;var s=(0,g.Gc)();var c=s.watch("quiz_option.feedback_mode");var l=(0,B.ro)(J.AO.CONTENT_DRIP)&&r==="unlock_sequentially"&&c==="retry";var d=s.watch("quiz_option.content_drip_settings.prerequisites");var f=(0,m.NL)();var v=f.getQueryData(["Topic",hu]);return(0,a.tZ)("div",{css:gu.settings},(0,a.tZ)(uu.Z,{title:(0,u.__)("Basic Settings","tutor"),collapsedAnimationDependencies:[c,d===null||d===void 0?void 0:d.length]},(0,a.tZ)("div",{css:gu.formWrapper},(0,a.tZ)(Z.Z,{when:o!=="tutor_h5p_quiz"},(0,a.tZ)("div",{css:gu.timeWrapper},(0,a.tZ)(g.Qr,{name:"quiz_option.time_limit.time_value",control:s.control,render:function t(e){return(0,a.tZ)(P.Z,vu({},e,{type:"number",label:(0,u.__)("Time Limit","tutor"),helpText:(0,u.__)("Set a time limit for this quiz. A value of “0” indicates no time limit","tutor"),selectOnFocus:true}))}}),(0,a.tZ)(g.Qr,{name:"quiz_option.time_limit.time_type",control:s.control,render:function t(e){return(0,a.tZ)(T.Z,vu({},e,{options:[{label:(0,u.__)("Seconds","tutor"),value:"seconds"},{label:(0,u.__)("Minutes","tutor"),value:"minutes"},{label:(0,u.__)("Hours","tutor"),value:"hours"},{label:(0,u.__)("Days","tutor"),value:"days"},{label:(0,u.__)("Weeks","tutor"),value:"weeks"}]}))}}))),(0,a.tZ)(Z.Z,{when:o!=="tutor_h5p_quiz"},(0,a.tZ)(g.Qr,{name:"quiz_option.hide_quiz_time_display",control:s.control,render:function t(e){return(0,a.tZ)(dt.Z,vu({},e,{label:(0,u.__)("Hide Quiz Time","tutor")}))}}),(0,a.tZ)(g.Qr,{name:"quiz_option.feedback_mode",control:s.control,render:function t(e){return(0,a.tZ)(T.Z,vu({},e,{label:(0,u.__)("Feedback Mode","tutor"),leftIcon:(0,a.tZ)(p.Z,{name:"eye",width:32,height:32}),options:[{label:(0,u.__)("Default","tutor"),value:"default",description:(0,u.__)("Answers are shown after finishing the quiz.","tutor")},{label:(0,u.__)("Reveal Mode","tutor"),value:"reveal",description:(0,u.__)("Show answer after attempting the question.","tutor")},{label:(0,u.__)("Retry","tutor"),value:"retry",description:(0,u.__)("Allows students to retake the quiz after their first attempt.","tutor")}]}))}})),(0,a.tZ)(Z.Z,{when:c==="retry"},(0,a.tZ)(g.Qr,{name:"quiz_option.attempts_allowed",control:s.control,rules:{max:20,min:0},render:function t(e){return(0,a.tZ)(P.Z,vu({},e,{type:"number",label:(0,u.__)("Attempts Allowed","tutor"),helpText:(0,u.__)('Define how many times a student can retake this quiz. Setting it to "0" allows unlimited attempts',"tutor"),selectOnFocus:true}))}})),(0,a.tZ)(Z.Z,{when:l&&o!=="tutor_h5p_quiz"},(0,a.tZ)(g.Qr,{name:"quiz_option.pass_is_required",control:s.control,render:function t(e){return(0,a.tZ)(dt.Z,vu({},e,{label:(0,u.__)("Passing is Required","tutor"),helpText:(0,u.__)("By enabling this option, the student must have to pass it to access the next quiz","tutor")}))}})),(0,a.tZ)(g.Qr,{name:"quiz_option.passing_grade",control:s.control,render:function t(e){return(0,a.tZ)(A.Z,vu({},e,{label:(0,u.__)("Passing Grade","tutor"),helpText:(0,u.__)("Set the minimum score percentage required to pass this quiz","tutor"),content:"%",contentPosition:"right",contentCss:pt.i.inputCurrencyStyle}))}}),(0,a.tZ)(Z.Z,{when:o!=="tutor_h5p_quiz"},(0,a.tZ)(g.Qr,{name:"quiz_option.max_questions_for_answer",control:s.control,render:function t(e){return(0,a.tZ)(P.Z,vu({},e,{type:"number",label:(0,u.__)("Max Question Allowed to Answer","tutor"),helpText:(0,u.__)("Set the number of quiz questions randomly from your question pool. If the set number exceeds available questions, all questions will be included","tutor"),selectOnFocus:true}))}})),(0,a.tZ)(Z.Z,{when:(0,B.ro)(J.AO.CONTENT_DRIP)&&o!=="tutor_h5p_quiz"},(0,a.tZ)(Z.Z,{when:r==="specific_days"},(0,a.tZ)(g.Qr,{name:"quiz_option.content_drip_settings.after_xdays_of_enroll",control:s.control,render:function t(e){return(0,a.tZ)(P.Z,vu({},e,{type:"number",label:(0,a.tZ)("div",{css:gu.contentDripLabel},(0,a.tZ)(p.Z,{name:"contentDrip",height:24,width:24}),(0,u.__)("Available after days","tutor")),helpText:(0,u.__)("This quiz will be available after the given number of days.","tutor"),placeholder:"0",selectOnFocus:true}))}})),(0,a.tZ)(Z.Z,{when:r==="unlock_by_date"},(0,a.tZ)(g.Qr,{name:"quiz_option.content_drip_settings.unlock_date",control:s.control,render:function t(e){return(0,a.tZ)(S.Z,vu({},e,{label:(0,a.tZ)("div",{css:gu.contentDripLabel},(0,a.tZ)(p.Z,{name:"contentDrip",height:24,width:24}),(0,u.__)("Unlock Date","tutor")),placeholder:(0,u.__)("Select Unlock Date","tutor"),helpText:(0,u.__)("This quiz will be available from the given date. Leave empty to make it available immediately.","tutor")}))}})),(0,a.tZ)(Z.Z,{when:r==="after_finishing_prerequisites"},(0,a.tZ)(g.Qr,{name:"quiz_option.content_drip_settings.prerequisites",control:s.control,render:function t(e){return(0,a.tZ)(C.Z,vu({},e,{label:(0,a.tZ)("div",{css:gu.contentDripLabel},(0,a.tZ)(p.Z,{name:"contentDrip",height:24,width:24}),(0,u.__)("Prerequisites","tutor")),placeholder:(0,u.__)("Select Prerequisite","tutor"),options:v.reduce((function(t,e){t.push(lu(lu({},e),{},{contents:e.contents.filter((function(t){return t.ID!==i}))}));return t}),[])||[],isSearchable:true,helpText:(0,u.__)("Select items that should be complete before this item","tutor")}))}}))))),(0,a.tZ)(uu.Z,{title:(0,u.__)("Advanced Settings","tutor")},(0,a.tZ)("div",{css:gu.formWrapper},(0,a.tZ)(g.Qr,{name:"quiz_option.quiz_auto_start",control:s.control,render:function t(e){return(0,a.tZ)(dt.Z,vu({},e,{label:(0,u.__)("Quiz Auto Start","tutor"),helpText:(0,u.__)("When enabled, the quiz begins immediately as soon as the page loads","tutor")}))}}),(0,a.tZ)("div",{css:gu.questionLayoutAndOrder},(0,a.tZ)(Z.Z,{when:o!=="tutor_h5p_quiz"},(0,a.tZ)(g.Qr,{name:"quiz_option.question_layout_view",control:s.control,render:function t(e){return(0,a.tZ)(T.Z,vu({},e,{label:(0,u.__)("Question Layout","tutor"),placeholder:(0,u.__)("Select an option","tutor"),options:[{label:(0,u.__)("Single question","tutor"),value:"single_question"},{label:(0,u.__)("Question pagination","tutor"),value:"question_pagination"},{label:(0,u.__)("Question below each other","tutor"),value:"question_below_each_other"}]}))}})),(0,a.tZ)(g.Qr,{name:"quiz_option.questions_order",control:s.control,render:function t(e){return(0,a.tZ)(T.Z,vu({},e,{label:(0,u.__)("Question Order","tutor"),placeholder:(0,u.__)("Select an option","tutor"),options:[{label:(0,u.__)("Random","tutor"),value:"rand"},{label:(0,u.__)("Sorting","tutor"),value:"sorting"},{label:(0,u.__)("Ascending","tutor"),value:"asc"},{label:(0,u.__)("Descending","tutor"),value:"desc"}]}))}})),(0,a.tZ)(Z.Z,{when:o!=="tutor_h5p_quiz"},(0,a.tZ)(g.Qr,{name:"quiz_option.hide_question_number_overview",control:s.control,render:function t(e){return(0,a.tZ)(dt.Z,vu({},e,{label:(0,u.__)("Hide Question Number","tutor")}))}}),(0,a.tZ)(g.Qr,{name:"quiz_option.short_answer_characters_limit",control:s.control,render:function t(e){return(0,a.tZ)(P.Z,vu({},e,{type:"number",label:(0,u.__)("Set Character Limit for Short Answers","tutor"),selectOnFocus:true}))}}),(0,a.tZ)(g.Qr,{name:"quiz_option.open_ended_answer_characters_limit",control:s.control,render:function t(e){return(0,a.tZ)(P.Z,vu({},e,{label:(0,u.__)("Set Character Limit for Open-Ended/Essay Answers","tutor"),selectOnFocus:true}))}})))),(0,a.tZ)(L.Z,{section:"Curriculum.Quiz.bottom_of_settings",form:s}))};const mu=yu;var gu={settings:(0,a.iv)(pt.i.display.flex("column")," gap:",Q.W0[24],";"+(true?"":0),true?"":0),formWrapper:(0,a.iv)(pt.i.display.flex("column")," gap:",Q.W0[20],";"+(true?"":0),true?"":0),timeWrapper:(0,a.iv)(pt.i.display.flex()," align-items:flex-end;gap:",Q.W0[8],";"+(true?"":0),true?"":0),questionLayoutAndOrder:(0,a.iv)(pt.i.display.flex()," gap:",Q.W0[20],";",Q.Uo.smallMobile,"{flex-direction:column;}"+(true?"":0),true?"":0),contentDripLabel:(0,a.iv)("display:flex;align-items:center;svg{margin-right:",Q.W0[4],";color:",Q.Jv.icon.success,";}"+(true?"":0),true?"":0)};var _u=r(7363);function bu(t){"@babel/helpers - typeof";return bu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},bu(t)}function wu(){wu=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return wu.apply(this,arguments)}function Zu(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Zu=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function t(e,r,n){return e[r]=n}}function c(t,e,r,i){var o=e&&e.prototype instanceof f?e:f,a=Object.create(o.prototype),u=new E(i||[]);return n(a,"_invoke",{value:w(t,r,u)}),a}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};s(h,o,(function(){return this}));var y=Object.getPrototypeOf,m=y&&y(y(k([])));m&&m!==e&&r.call(m,o)&&(h=m);var g=v.prototype=f.prototype=Object.create(h);function _(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function b(t,e){function i(n,o,a,u){var s=l(t[n],t,o);if("throw"!==s.type){var c=s.arg,d=c.value;return d&&"object"==bu(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){i("next",t,a,u)}),(function(t){i("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return i("throw",t,a,u)}))}u(s.arg)}var o;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){i(r,n,t,e)}))}return o=o?o.then(a,a):a()}})}function w(t,e,r){var n="suspendedStart";return function(i,o){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===i)throw o;return q()}for(r.method=i,r.arg=o;;){var a=r.delegate;if(a){var u=Z(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var s=l(t,e,r);if("normal"===s.type){if(n=r.done?"completed":"suspendedYield",s.arg===d)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(n="completed",r.method="throw",r.arg=s.arg)}}}function Z(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,Z(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var i=l(n,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,d;var o=i.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function x(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(x,this),this.reset(!0)}function k(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return i.next=i}}return{next:q}}function q(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=s(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,s(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},_(b.prototype),s(b.prototype,a,(function(){return this})),t.AsyncIterator=b,t.async=function(e,r,n,i,o){void 0===o&&(o=Promise);var a=new b(c(e,r,n,i),o);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},_(g),s(g,u,"Generator"),s(g,o,(function(){return this})),s(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,E.prototype={constructor:E,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function i(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],u=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(s&&c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function t(e,n){for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var i=n.completion;if("throw"===i.type){var o=i.arg;O(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function xu(t,e,r,n,i,o,a){try{var u=t[o](a);var s=u.value}catch(t){r(t);return}if(u.done){e(s)}else{Promise.resolve(s).then(n,i)}}function Ou(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var o=t.apply(e,r);function a(t){xu(o,n,i,a,u,"next",t)}function u(t){xu(o,n,i,a,u,"throw",t)}a(undefined)}))}}function Eu(t,e){return Pu(t)||ju(t,e)||qu(t,e)||ku()}function ku(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function qu(t,e){if(!t)return;if(typeof t==="string")return Su(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Su(t,e)}function Su(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function ju(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],s=!0,c=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,i=t}finally{try{if(!s&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw i}}return u}}function Pu(t){if(Array.isArray(t))return t}var Au=(0,N.zs)();var Tu=function t(e){var r=e.closeModal,n=e.icon,i=e.title,o=e.subtitle,c=e.quizId,l=e.topicId,v=e.contentDripType,h=e.contentType;var y=(0,W.l)(),m=y.fields;var _=(0,s.useState)(false),w=Eu(_,2),x=w[0],O=w[1];var E=(0,s.useState)("details"),k=Eu(E,2),q=k[0],S=k[1];var j=(0,s.useState)(!(0,Pr.$K)(c)),A=Eu(j,2),T=A[0],C=A[1];var I=(0,s.useRef)(null);var L=Nt();var z=Jt(c||"");var U=(0,b.p)(),Q=U.showToast;var F=(0,M.O)({defaultValues:{quiz_option:{time_limit:{time_value:0,time_type:"minutes"},hide_quiz_time_display:false,feedback_mode:"retry",attempts_allowed:10,passing_grade:80,max_questions_for_answer:h==="tutor_h5p_quiz"?0:10,quiz_auto_start:false,question_layout_view:h==="tutor_h5p_quiz"?"question_below_each_other":"single_question",questions_order:"rand",hide_question_number_overview:false,short_answer_characters_limit:200,open_ended_answer_characters_limit:500,content_drip_settings:{unlock_date:"",after_xdays_of_enroll:0,prerequisites:[]}},questions:[]},shouldFocusError:true});var G=F.formState.dirtyFields&&Object.keys(F.formState.dirtyFields).length>0;(0,s.useEffect)((function(){var t=function t(e){if(G){e.preventDefault();return}F.reset()};window.addEventListener("beforeunload",t);return function(){window.removeEventListener("beforeunload",t)}}),[G]);(0,s.useEffect)((function(){if(!z.data){return}var t=At(z.data,(0,B.hk)({fields:m.Curriculum.Quiz}));F.reset(t)}),[z.data]);var R=function(){var t=Ou(Zu().mark((function t(e,n,i){var o,a,s;return Zu().wrap((function t(c){while(1)switch(c.prev=c.next){case 0:if(e.quiz_title){c.next=4;break}S("details");Promise.resolve().then((function(){F.trigger("quiz_title",{shouldFocus:true})}));return c.abrupt("return");case 4:if(!(e.questions.length===0)){c.next=8;break}S("details");Q({message:(0,u.__)("Please add a question","tutor"),type:"danger"});return c.abrupt("return");case 8:o=(0,N.aU)(n,F);if(!(o!==true)){c.next=13;break}i(o);S("details");return c.abrupt("return");case 13:C(false);a=Tt(e,l,v,Au,(0,B.hk)({fields:m.Curriculum.Quiz,slotKey:"after_question_description"},{fields:m.Curriculum.Quiz,slotKey:"bottom_of_question_sidebar"}),(0,B.hk)({fields:m.Curriculum.Quiz,slotKey:"bottom_of_settings"}));c.next=17;return L.mutateAsync(a);case 17:s=c.sent;if(s.data){C(false);r({action:"CONFIRM"});F.reset()}case 19:case"end":return c.stop()}}),t)})));return function e(r,n,i){return t.apply(this,arguments)}}();(0,s.useEffect)((function(){if(T){F.setFocus("quiz_title")}}),[T]);return(0,a.tZ)(g.RV,F,(0,a.tZ)(ze,{quizId:c||"",contentType:h||"tutor_quiz"},(function(t){var e=t.activeQuestionIndex,s=t.activeQuestionId,l=t.setActiveQuestionId,y=t.setValidationError;return(0,a.tZ)(D.Z,{onClose:function t(){return r({action:"CLOSE"})},icon:G?(0,a.tZ)(p.Z,{name:"warning",width:24,height:24}):n,title:G?J.iM.isAboveDesktop?(0,u.__)("Unsaved Changes","tutor"):"":i,subtitle:J.iM.isAboveSmallMobile?o:"",maxWidth:1218,headerChildren:(0,a.tZ)(je.Z,{wrapperCss:Iu.tabsWrapper,activeTab:q,tabList:[{label:J.iM.isAboveMobile?(0,u.__)("Question Details","tutor"):"",value:"details",icon:!J.iM.isAboveMobile?(0,a.tZ)(p.Z,{name:"text",width:24,height:24}):null},{label:J.iM.isAboveMobile?(0,u.__)("Settings","tutor"):"",value:"settings",icon:!J.iM.isAboveMobile?(0,a.tZ)(p.Z,{name:"settings",width:24,height:24}):null}],onChange:function t(e){return S(e)}}),actions:G&&(0,a.tZ)(_u.Fragment,null,(0,a.tZ)(d.Z,{variant:"text",size:"small",onClick:function t(){if(G){O(true);return}r()},ref:I},c?J.iM.isAboveSmallMobile?(0,u.__)("Discard Changes","tutor"):(0,u.__)("Discard","tutor"):(0,u.__)("Cancel","tutor")),(0,a.tZ)(Z.Z,{when:q==="settings"||c,fallback:(0,a.tZ)(d.Z,{variant:"primary",size:"small",onClick:function t(){return S("settings")}},(0,u.__)("Next","tutor"))},(0,a.tZ)(d.Z,{loading:L.isPending,variant:"primary",size:"small",onClick:F.handleSubmit((function(t){return R(t,e,y)}))},(0,u.__)("Save","tutor"))))},(0,a.tZ)("div",{css:Iu.wrapper({activeTab:q,isH5pQuiz:h==="tutor_h5p_quiz"})},(0,a.tZ)(Z.Z,{when:!z.isLoading,fallback:(0,a.tZ)(f.fz,null)},(0,a.tZ)(Z.Z,{when:q==="details"},(0,a.tZ)("div",{css:Iu.left},(0,a.tZ)(Z.Z,{when:q==="details"},(0,a.tZ)("div",{css:Iu.quizTitleWrapper},(0,a.tZ)(Z.Z,{when:T,fallback:(0,a.tZ)("div",{role:"button",tabIndex:0,css:Iu.quizNameWithButton},(0,a.tZ)("span",{css:Iu.quizTitle},F.getValues("quiz_title")),(0,a.tZ)(d.Z,{variant:"text",type:"button",onClick:function t(){return C(true)}},(0,a.tZ)(p.Z,{name:"edit",width:24,height:24})))},(0,a.tZ)("div",{css:Iu.quizForm},(0,a.tZ)(g.Qr,{control:F.control,name:"quiz_title",rules:{required:(0,u.__)("Quiz title is required","tutor")},render:function t(e){return(0,a.tZ)(P.Z,wu({},e,{placeholder:(0,u.__)("Add quiz title","tutor"),selectOnFocus:true}))}}),(0,a.tZ)(g.Qr,{control:F.control,name:"quiz_description",render:function t(e){return(0,a.tZ)(qe.Z,wu({},e,{placeholder:(0,u.__)("Add a summary","tutor"),enableResize:false,rows:2}))}}),(0,a.tZ)("div",{css:Iu.quizFormButtonWrapper},(0,a.tZ)(d.Z,{variant:"text",type:"button",onClick:function t(){if(!F.watch("quiz_title")){r()}C(false)},size:"small"},(0,u.__)("Cancel","tutor")),(0,a.tZ)(d.Z,{loading:L.isPending,variant:"secondary",type:"submit",size:"small",onClick:function t(){if(!F.getValues("quiz_title")){F.trigger("quiz_title");return}C(false)}},(0,u.__)("Ok","tutor")))))),(0,a.tZ)(ou,{isEditing:T})))),(0,a.tZ)("div",{css:Iu.content({activeTab:q})},(0,a.tZ)(Z.Z,{when:q==="settings",fallback:(0,a.tZ)(Za,null)},(0,a.tZ)(mu,{contentDripType:v}))),(0,a.tZ)(Z.Z,{when:q==="details"&&h!=="tutor_h5p_quiz"},(0,a.tZ)("div",{css:Iu.right},(0,a.tZ)(Ye,null))))),(0,a.tZ)(Se.Z,{isOpen:x,triggerRef:I,closePopover:function t(){return O(false)},maxWidth:"258px",title:(0,u.__)("Your quiz has unsaved changes. If you cancel, you will lose your progress.","tutor"),message:(0,u.__)("Are you sure you want to continue?","tutor"),animationType:Oa.ru.slideUp,arrow:J.iM.isAboveMobile?"top":"absoluteCenter",positionModifier:{top:-50,left:c?88:q==="settings"?30:26},hideArrow:true,confirmButton:{text:(0,u.__)("Yes","tutor"),variant:"primary"},cancelButton:{text:(0,u.__)("No","tutor"),variant:"text"},onConfirmation:function t(){var e;F.reset();y(null);if(!((e=z.data)!==null&&e!==void 0&&e.questions.find((function(t){return t.question_id===s})))){l("")}if(!c){r()}}}))})))};const Cu=Tu;var Iu={wrapper:function t(e){var r=e.activeTab,n=e.isH5pQuiz;return(0,a.iv)("width:100%;display:grid;grid-template-columns:",r==="details"?n?"513px 1fr":"352px 1fr 280px":"1fr",";height:100%;",Q.Uo.smallTablet,"{width:100%;grid-template-columns:1fr;height:max-content;}"+(true?"":0),true?"":0)},tabsWrapper:(0,a.iv)("height:",J.oC.HEADER_HEIGHT,"px;",Q.Uo.smallMobile,"{button{min-width:auto;}}"+(true?"":0),true?"":0),left:(0,a.iv)("border-right:1px solid ",Q.Jv.stroke.divider,";"+(true?"":0),true?"":0),content:function t(e){var r=e.activeTab;return(0,a.iv)(pt.i.overflowYAuto,";padding:",Q.W0[32]," 0 ",Q.W0[48]," ",Q.W0[6],";",r==="settings"&&(0,a.iv)("padding-top:",Q.W0[24],";padding-inline:352px 352px;",Q.Uo.smallTablet,"{padding:",Q.W0[16]," ",Q.W0[8]," ",Q.W0[24]," ",Q.W0[8],";margin:0 auto;}",Q.Uo.smallMobile,"{padding-top:",Q.W0[8],";}"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},right:(0,a.iv)(pt.i.overflowYAuto,";",pt.i.display.flex("column"),";gap:",Q.W0[16],";border-left:1px solid ",Q.Jv.stroke.divider,";"+(true?"":0),true?"":0),quizTitleWrapper:(0,a.iv)(F.c.caption(),";color:",Q.Jv.text.subdued,";padding:",Q.W0[16]," ",Q.W0[32]," ",Q.W0[16]," ",Q.W0[28],";border-bottom:1px solid ",Q.Jv.stroke.divider,";",Q.Uo.smallTablet,"{padding:",Q.W0[8],";}"+(true?"":0),true?"":0),quizNameWithButton:(0,a.iv)("display:inline-flex;width:100%;transition:all 0.3s ease-in-out;button{display:none;}:hover,:focus-within{button{display:block;}}:focus-visible{outline:2px solid ",Q.Jv.stroke.brand,";border-radius:",Q.E0[6],";button{display:block;}}",Q.Uo.smallTablet,"{button{display:block;}}"+(true?"":0),true?"":0),quizTitle:(0,a.iv)("flex:1;padding:",Q.W0[8]," ",Q.W0[16]," ",Q.W0[8]," ",Q.W0[8],";background-color:",Q.Jv.background.white,";border-radius:",Q.E0[6],";"+(true?"":0),true?"":0),quizForm:(0,a.iv)("display:flex;flex-direction:column;gap:",Q.W0[12],";"+(true?"":0),true?"":0),quizFormButtonWrapper:(0,a.iv)("display:flex;justify-content:end;margin-top:",Q.W0[4],";gap:",Q.W0[8],";"+(true?"":0),true?"":0)};var Du=r(7363);function Lu(t){"@babel/helpers - typeof";return Lu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Lu(t)}function Wu(t,e){var r=typeof Symbol!=="undefined"&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=Fu(t))||e&&t&&typeof t.length==="number"){if(r)t=r;var n=0;var i=function t(){};return{s:i,n:function e(){if(n>=t.length)return{done:true};return{done:false,value:t[n++]}},e:function t(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o=true,a=false,u;return{s:function e(){r=r.call(t)},n:function t(){var e=r.next();o=e.done;return e},e:function t(e){a=true;u=e},f:function t(){try{if(!o&&r["return"]!=null)r["return"]()}finally{if(a)throw u}}}}function zu(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */zu=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function t(e,r,n){return e[r]=n}}function c(t,e,r,i){var o=e&&e.prototype instanceof f?e:f,a=Object.create(o.prototype),u=new E(i||[]);return n(a,"_invoke",{value:w(t,r,u)}),a}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};s(h,o,(function(){return this}));var y=Object.getPrototypeOf,m=y&&y(y(k([])));m&&m!==e&&r.call(m,o)&&(h=m);var g=v.prototype=f.prototype=Object.create(h);function _(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function b(t,e){function i(n,o,a,u){var s=l(t[n],t,o);if("throw"!==s.type){var c=s.arg,d=c.value;return d&&"object"==Lu(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){i("next",t,a,u)}),(function(t){i("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return i("throw",t,a,u)}))}u(s.arg)}var o;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){i(r,n,t,e)}))}return o=o?o.then(a,a):a()}})}function w(t,e,r){var n="suspendedStart";return function(i,o){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===i)throw o;return q()}for(r.method=i,r.arg=o;;){var a=r.delegate;if(a){var u=Z(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var s=l(t,e,r);if("normal"===s.type){if(n=r.done?"completed":"suspendedYield",s.arg===d)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(n="completed",r.method="throw",r.arg=s.arg)}}}function Z(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,Z(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var i=l(n,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,d;var o=i.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function x(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(x,this),this.reset(!0)}function k(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return i.next=i}}return{next:q}}function q(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=s(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,s(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},_(b.prototype),s(b.prototype,a,(function(){return this})),t.AsyncIterator=b,t.async=function(e,r,n,i,o){void 0===o&&(o=Promise);var a=new b(c(e,r,n,i),o);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},_(g),s(g,u,"Generator"),s(g,o,(function(){return this})),s(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,E.prototype={constructor:E,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function i(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],u=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(s&&c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function t(e,n){for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var i=n.completion;if("throw"===i.type){var o=i.arg;O(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function Nu(t,e,r,n,i,o,a){try{var u=t[o](a);var s=u.value}catch(t){r(t);return}if(u.done){e(s)}else{Promise.resolve(s).then(n,i)}}function Uu(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var o=t.apply(e,r);function a(t){Nu(o,n,i,a,u,"next",t)}function u(t){Nu(o,n,i,a,u,"throw",t)}a(undefined)}))}}function Ju(t,e){return Gu(t)||Bu(t,e)||Fu(t,e)||Qu()}function Qu(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Fu(t,e){if(!t)return;if(typeof t==="string")return Mu(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Mu(t,e)}function Mu(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Bu(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],s=!0,c=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,i=t}finally{try{if(!s&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw i}}return u}}function Gu(t){if(Array.isArray(t))return t}function Ru(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Vu=(0,N.zs)();var Hu=!!U.y.tutor_pro_url;var Ku=(0,B.ro)(J.AO.TUTOR_GOOGLE_MEET_INTEGRATION)||(0,B.ro)(J.AO.TUTOR_ZOOM_INTEGRATION);var Yu=function t(e){var r=e.topic;var n=(0,N.Wj)("topic-",r.id);var i=(0,b.p)(),o=i.showToast;var c=(0,w.d)(),l=c.showModal;var f=(0,g.Gc)();var v=(0,s.useState)(null),h=Ju(v,2),y=h[0],S=h[1];var j=(0,s.useState)(false),P=Ju(j,2),A=P[0],T=P[1];var C=(0,s.useRef)(null);var I=(0,s.useRef)(null);var D=Dt();var L=(0,m.NL)();var W=L.getQueryData(["CourseDetails",Number(Vu)]);var z=(0,x.tZ)({acceptedTypes:[".csv"],onUpload:function(){var t=Uu(zu().mark((function t(e){return zu().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:r.next=2;return D.mutateAsync({topic_id:n,csv_file:e[0]});case 2:T(false);case 3:case"end":return r.stop()}}),t)})));function e(e){return t.apply(this,arguments)}return e}(),onError:function t(e){var r=Wu(e),n;try{for(r.s();!(n=r.n()).done;){var i=n.value;o({message:i,type:"danger"})}}catch(t){r.e(t)}finally{r.f()}T(false)}}),U=z.fileInputRef,Q=z.handleChange;return(0,a.tZ)(Du.Fragment,null,(0,a.tZ)("div",{css:Xu.contentButtons},(0,a.tZ)("div",{css:Xu.leftButtons},(0,a.tZ)(d.Z,{variant:"tertiary",isOutlined:true,size:"small",icon:(0,a.tZ)(p.Z,{name:"plus",width:24,height:24}),disabled:!r.isSaved,buttonCss:Xu.contentButton,onClick:function t(){l({component:Ee,props:{contentDripType:f.watch("contentDripType"),topicId:n,title:(0,u.__)("Lesson","tutor"),icon:(0,a.tZ)(p.Z,{name:"lesson",width:24,height:24}),subtitle:(0,u.sprintf)((0,u.__)("Topic: %s","tutor"),r.title)}})}},(0,u.__)("Lesson","tutor")),(0,a.tZ)(d.Z,{variant:"tertiary",isOutlined:true,size:"small",icon:(0,a.tZ)(p.Z,{name:"plus",width:24,height:24}),disabled:!r.isSaved,buttonCss:Xu.contentButton,onClick:function t(){l({component:Cu,props:{topicId:n,contentDripType:f.watch("contentDripType"),title:(0,u.__)("Quiz","tutor"),icon:(0,a.tZ)(p.Z,{name:"quiz",width:24,height:24}),subtitle:(0,u.sprintf)((0,u.__)("Topic: %s","tutor"),r.title)},closeOnEscape:false})}},(0,u.__)("Quiz","tutor")),(0,a.tZ)(Z.Z,{when:!Hu,fallback:(0,a.tZ)(Z.Z,{when:(0,B.ro)(J.AO.H5P_INTEGRATION)},(0,a.tZ)(d.Z,{variant:"tertiary",isOutlined:true,size:"small",icon:(0,a.tZ)(p.Z,{name:"plus",width:24,height:24}),disabled:!r.isSaved,buttonCss:Xu.contentButton,onClick:function t(){l({component:Cu,props:{topicId:n,contentDripType:f.watch("contentDripType"),title:(0,u.__)("Interactive Quiz","tutor"),icon:(0,a.tZ)(p.Z,{name:"interactiveQuiz",width:24,height:24}),subtitle:(0,u.sprintf)((0,u.__)("Topic: %s","tutor"),r.title),contentType:"tutor_h5p_quiz"},closeOnEscape:false})}},(0,u.__)("Interactive Quiz","tutor")))},(0,a.tZ)(_.Z,null,(0,a.tZ)(d.Z,{variant:"tertiary",isOutlined:true,size:"small",icon:(0,a.tZ)(p.Z,{name:"plus",width:24,height:24}),disabled:true,onClick:B.ZT},(0,u.__)("Interactive Quiz","tutor")))),(0,a.tZ)(Z.Z,{when:!Hu,fallback:(0,a.tZ)(Z.Z,{when:(0,B.ro)(J.AO.TUTOR_ASSIGNMENTS)},(0,a.tZ)(d.Z,{variant:"tertiary",isOutlined:true,size:"small",icon:(0,a.tZ)(p.Z,{name:"plus",width:24,height:24}),disabled:!r.isSaved,buttonCss:Xu.contentButton,onClick:function t(){l({component:ut,props:{topicId:n,contentDripType:f.watch("contentDripType"),title:(0,u.__)("Assignment","tutor"),icon:(0,a.tZ)(p.Z,{name:"assignment",width:24,height:24}),subtitle:(0,u.sprintf)((0,u.__)("Topic: %s","tutor"),r.title)}})}},(0,u.__)("Assignment","tutor")))},(0,a.tZ)(_.Z,null,(0,a.tZ)(d.Z,{variant:"tertiary",isOutlined:true,size:"small",icon:(0,a.tZ)(p.Z,{name:"plus",width:24,height:24}),disabled:true,onClick:B.ZT},(0,u.__)("Assignment","tutor"))))),(0,a.tZ)("div",{css:Xu.rightButtons},(0,a.tZ)(Z.Z,{when:!Hu||Ku,fallback:(0,a.tZ)(Z.Z,{when:Hu,fallback:(0,a.tZ)(_.Z,null,(0,a.tZ)(d.Z,{variant:"tertiary",isOutlined:true,size:"small",icon:(0,a.tZ)(p.Z,{name:"import",width:24,height:24}),disabled:true,onClick:B.ZT},(0,u.__)("Import Quiz","tutor")))},(0,a.tZ)(Z.Z,{when:(0,B.ro)(J.AO.QUIZ_EXPORT_IMPORT)},(0,a.tZ)(d.Z,{variant:"tertiary",isOutlined:true,size:"small",icon:(0,a.tZ)(p.Z,{name:"import",width:24,height:24}),disabled:!r.isSaved,buttonCss:Xu.contentButton,onClick:function t(){var e;U===null||U===void 0?void 0:(e=U.current)===null||e===void 0?void 0:e.click()}},(0,u.__)("Import Quiz","tutor"))))},(0,a.tZ)(E.Z,{isOpen:A,onClick:function t(){return T(true)},closePopover:function t(){return T(false)},disabled:!r.isSaved,dotsOrientation:"vertical",maxWidth:Hu?"220px":"240px",isInverse:true,arrowPosition:"auto",hideArrow:true,closeOnEscape:false,size:J.iM.isAboveMobile?"medium":"small"},(0,a.tZ)(Z.Z,{when:!Hu||(0,B.ro)(J.AO.TUTOR_GOOGLE_MEET_INTEGRATION)},(0,a.tZ)(E.Z.Option,{text:(0,a.tZ)("span",{ref:C,css:Xu.threeDotButton},(0,u.__)("Meet live lesson","tutor"),(0,a.tZ)(Z.Z,{when:!Hu},(0,a.tZ)(_.Z,{size:"small",content:(0,u.__)("Pro","tutor")}))),disabled:!Hu,icon:(0,a.tZ)(p.Z,{width:24,height:24,name:"googleMeetColorize",isColorIcon:true}),onClick:function t(){return S("tutor-google-meet")}})),(0,a.tZ)(Z.Z,{when:!Hu||(0,B.ro)(J.AO.TUTOR_ZOOM_INTEGRATION)},(0,a.tZ)(E.Z.Option,{text:(0,a.tZ)("span",{ref:I,css:Xu.threeDotButton},(0,u.__)("Zoom live lesson","tutor"),(0,a.tZ)(Z.Z,{when:!Hu},(0,a.tZ)(_.Z,{size:"small",content:(0,u.__)("Pro","tutor")}))),disabled:!Hu,icon:(0,a.tZ)(p.Z,{width:24,height:24,name:"zoomColorize",isColorIcon:true}),onClick:function t(){return S("tutor_zoom_meeting")}})),(0,a.tZ)(Z.Z,{when:!Hu||(0,B.ro)(J.AO.QUIZ_EXPORT_IMPORT)},(0,a.tZ)(E.Z.Option,{text:(0,a.tZ)("span",{css:Xu.threeDotButton},(0,u.__)("Import Quiz","tutor"),(0,a.tZ)(Z.Z,{when:!Hu},(0,a.tZ)(_.Z,{size:"small",content:(0,u.__)("Pro","tutor")}))),disabled:!Hu,onClick:function t(){var e;U===null||U===void 0?void 0:(e=U.current)===null||e===void 0?void 0:e.click()},icon:(0,a.tZ)(p.Z,{name:"importColorized",width:24,height:24,isColorIcon:true})})))))),(0,a.tZ)("input",{css:Xu.input,type:"file",ref:U,onChange:Q,multiple:false,accept:".csv"}),(0,a.tZ)(O.Z,{triggerRef:C,isOpen:y==="tutor-google-meet",closePopover:B.ZT,maxWidth:"306px",closeOnEscape:false,arrow:J.iM.isAboveMobile?"auto":"absoluteCenter",hideArrow:true},(0,a.tZ)(k.Z,{topicId:n,data:null,onCancel:function t(){S(null);T(false)}})),(0,a.tZ)(O.Z,{triggerRef:I,isOpen:y==="tutor_zoom_meeting",closePopover:B.ZT,maxWidth:"306px",closeOnEscape:false,arrow:J.iM.isAboveMobile?"auto":"absoluteCenter",hideArrow:true},(0,a.tZ)(q.Z,{topicId:n,meetingHost:(W===null||W===void 0?void 0:W.zoom_users)||{},data:null,onCancel:function t(){S(null);T(false)}})))};const $u=Yu;var Xu={contentButtons:(0,a.iv)(pt.i.display.flex(),";justify-content:space-between;"+(true?"":0),true?"":0),leftButtons:(0,a.iv)(pt.i.display.flex(),";gap:",Q.W0[12],";",Q.Uo.smallMobile,"{flex-wrap:wrap;}"+(true?"":0),true?"":0),rightButtons:true?{name:"s5xdrg",styles:"display:flex;align-items:center"}:0,threeDotButton:(0,a.iv)("display:flex;align-items:center;gap:",Q.W0[4],";"+(true?"":0),true?"":0),contentButton:(0,a.iv)(":hover:not(:disabled){background-color:",Q.Jv.background.white,";color:",Q.Jv.text.brand,";outline:1px solid ",Q.Jv.stroke.brand,";}"+(true?"":0),true?"":0),input:true?{name:"14y6t8x",styles:"display:none!important"}:0};var ts=r(9313);var es=r(7363);function rs(t){"@babel/helpers - typeof";return rs="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},rs(t)}function ns(){ns=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return ns.apply(this,arguments)}function is(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */is=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function t(e,r,n){return e[r]=n}}function c(t,e,r,i){var o=e&&e.prototype instanceof f?e:f,a=Object.create(o.prototype),u=new E(i||[]);return n(a,"_invoke",{value:w(t,r,u)}),a}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};s(h,o,(function(){return this}));var y=Object.getPrototypeOf,m=y&&y(y(k([])));m&&m!==e&&r.call(m,o)&&(h=m);var g=v.prototype=f.prototype=Object.create(h);function _(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function b(t,e){function i(n,o,a,u){var s=l(t[n],t,o);if("throw"!==s.type){var c=s.arg,d=c.value;return d&&"object"==rs(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){i("next",t,a,u)}),(function(t){i("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return i("throw",t,a,u)}))}u(s.arg)}var o;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){i(r,n,t,e)}))}return o=o?o.then(a,a):a()}})}function w(t,e,r){var n="suspendedStart";return function(i,o){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===i)throw o;return q()}for(r.method=i,r.arg=o;;){var a=r.delegate;if(a){var u=Z(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var s=l(t,e,r);if("normal"===s.type){if(n=r.done?"completed":"suspendedYield",s.arg===d)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(n="completed",r.method="throw",r.arg=s.arg)}}}function Z(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,Z(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var i=l(n,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,d;var o=i.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function x(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(x,this),this.reset(!0)}function k(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return i.next=i}}return{next:q}}function q(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=s(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,s(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},_(b.prototype),s(b.prototype,a,(function(){return this})),t.AsyncIterator=b,t.async=function(e,r,n,i,o){void 0===o&&(o=Promise);var a=new b(c(e,r,n,i),o);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},_(g),s(g,u,"Generator"),s(g,o,(function(){return this})),s(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,E.prototype={constructor:E,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function i(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],u=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(s&&c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function t(e,n){for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var i=n.completion;if("throw"===i.type){var o=i.arg;O(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function os(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function as(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?os(Object(r),!0).forEach((function(e){us(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):os(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function us(t,e,r){e=ss(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function ss(t){var e=cs(t,"string");return rs(e)==="symbol"?e:String(e)}function cs(t,e){if(rs(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(rs(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function ls(t,e,r,n,i,o,a){try{var u=t[o](a);var s=u.value}catch(t){r(t);return}if(u.done){e(s)}else{Promise.resolve(s).then(n,i)}}function ds(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var o=t.apply(e,r);function a(t){ls(o,n,i,a,u,"next",t)}function u(t){ls(o,n,i,a,u,"throw",t)}a(undefined)}))}}function fs(t,e){return ms(t)||ys(t,e)||vs(t,e)||ps()}function ps(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function vs(t,e){if(!t)return;if(typeof t==="string")return hs(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hs(t,e)}function hs(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function ys(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],s=!0,c=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,i=t}finally{try{if(!s&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw i}}return u}}function ms(t){if(Array.isArray(t))return t}function gs(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var _s=(0,N.zs)();var bs=!!U.y.tutor_pro_url;var ws=function t(e){var r=e.topic,n=e.isEdit,i=e.listeners,o=e.isDragging,c=e.onCollapse,l=e.onEdit,v=e.onCopy,h=e.onDelete,m=e.setIsEdit;var b=(0,N.Wj)("topic-",r.id);var w=(0,M.O)({defaultValues:{title:r.title,summary:r.summary},shouldFocusError:true});var x=(0,s.useState)(false),O=fs(x,2),E=O[0],k=O[1];var q=(0,s.useRef)(null);var S=(0,s.useRef)(null);var j=(0,z.FV)();var A=(0,z.P_)();var T=(0,z.Wn)(_s);var C=(0,ts.m)({ref:q,isOpen:!r.isCollapsed,heightCalculator:"client"});var I=function(){var t=ds(is().mark((function t(e){var n;return is().wrap((function t(i){while(1)switch(i.prev=i.next){case 0:i.next=2;return j.mutateAsync(as(as({},r.isSaved&&{topic_id:b}),{},{course_id:_s,title:e.title,summary:e.summary}));case 2:n=i.sent;if(n.data){m(false)}if(n.status_code===201){l===null||l===void 0?void 0:l("topic-".concat(n.data))}case 5:case"end":return i.stop()}}),t)})));return function e(r){return t.apply(this,arguments)}}();var D=function(){var t=ds(is().mark((function t(){var e;return is().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:r.next=2;return A.mutateAsync({course_id:_s,content_id:b,content_type:"topic"});case 2:e=r.sent;if(e.data){v===null||v===void 0?void 0:v("topic-".concat(e.data))}case 4:case"end":return r.stop()}}),t)})));return function e(){return t.apply(this,arguments)}}();(0,s.useEffect)((function(){if(n){w.setFocus("title")}}),[n]);return(0,a.tZ)(es.Fragment,null,(0,a.tZ)("div",{css:ks.header({isCollapsed:r.isCollapsed,isEdit:n,isDeletePopoverOpen:E,isDragging:o})},(0,a.tZ)("div",{css:ks.headerContent({isSaved:r.isSaved})},(0,a.tZ)("div",{css:ks.grabberInput,onClick:function t(){return c(r.id)},onKeyDown:function t(e){if(e.key==="Enter"){c(r.id)}}},(0,a.tZ)("button",ns({},r.isSaved?i:{},{css:ks.grabButton({isDragging:o}),type:"button",disabled:!r.isSaved}),(0,a.tZ)(p.Z,{name:"dragVertical",width:24,height:24})),(0,a.tZ)(Z.Z,{when:n,fallback:(0,a.tZ)("div",{css:ks.title({isEdit:n}),onDoubleClick:function t(){return m(true)}},w.watch("title"))},(0,a.tZ)("div",{css:ks.title({isEdit:n})},(0,a.tZ)(g.Qr,{control:w.control,name:"title",rules:{required:(0,u.__)("Title is required","tutor")},render:function t(e){return(0,a.tZ)(P.Z,ns({},e,{placeholder:(0,u.__)("Add a title","tutor"),isSecondary:true,selectOnFocus:true}))}})))),(0,a.tZ)("div",{css:ks.actions,"data-visually-hidden":true},(0,a.tZ)(Z.Z,{when:!n},(0,a.tZ)(ct.Z,{content:(0,u.__)("Edit","tutor"),delay:200},(0,a.tZ)("button",{type:"button",css:pt.i.actionButton,disabled:!r.isSaved,onClick:function t(){m(true);if(r.isCollapsed){c===null||c===void 0?void 0:c(r.id)}}},(0,a.tZ)(p.Z,{name:"edit",width:24,height:24})))),(0,a.tZ)(Z.Z,{when:r.isSaved},(0,a.tZ)(Z.Z,{when:!A.isPending,fallback:(0,a.tZ)(f.ZP,{size:24})},(0,a.tZ)(ct.Z,{content:(0,u.__)("Duplicate","tutor"),delay:200},(0,a.tZ)(Z.Z,{when:!bs,fallback:(0,a.tZ)("button",{type:"button",css:pt.i.actionButton,disabled:!r.isSaved,onClick:D},(0,a.tZ)(p.Z,{name:"copyPaste",width:24,height:24}))},(0,a.tZ)(_.Z,{size:"tiny"},(0,a.tZ)("button",{type:"button",css:pt.i.actionButton,disabled:true,onClick:B.ZT},(0,a.tZ)(p.Z,{name:"copyPaste",width:24,height:24}))))))),(0,a.tZ)(Z.Z,{when:r.isSaved},(0,a.tZ)(ct.Z,{content:(0,u.__)("Delete","tutor"),delay:200},(0,a.tZ)("button",{type:"button",css:pt.i.actionButton,disabled:!r.isSaved,"data-visually-hidden":true,ref:S,onClick:function t(){k(true)}},(0,a.tZ)(p.Z,{name:"delete",width:24,height:24})))),(0,a.tZ)(Z.Z,{when:r.isSaved},(0,a.tZ)("button",{type:"button",css:pt.i.actionButton,disabled:!r.isSaved,onClick:function t(){c===null||c===void 0?void 0:c(r.id)},"data-toggle-collapse":true},(0,a.tZ)(p.Z,{name:"chevronDown",width:24,height:24}))))),(0,a.tZ)(Z.Z,{when:n,fallback:(0,a.tZ)(Z.Z,{when:r.summary.length>0},(0,a.tZ)(y.q.div,{style:as({},C)},(0,a.tZ)("div",{css:ks.description({isEdit:n}),ref:q,onDoubleClick:function t(){return m(true)}},w.watch("summary"))))},(0,a.tZ)("div",{css:ks.description({isEdit:n})},(0,a.tZ)(g.Qr,{control:w.control,name:"summary",render:function t(e){return(0,a.tZ)(qe.Z,ns({},e,{placeholder:(0,u.__)("Add a summary","tutor"),isSecondary:true,rows:2,enableResize:true}))}}))),(0,a.tZ)(Z.Z,{when:n},(0,a.tZ)("div",{css:ks.footer},(0,a.tZ)(d.Z,{variant:"text",size:"small",onClick:function t(){if(!w.formState.isValid&&!r.isSaved){h===null||h===void 0?void 0:h()}w.reset();m(false)}},(0,u.__)("Cancel","tutor")),(0,a.tZ)(d.Z,{loading:j.isPending,variant:"secondary",size:"small",onClick:w.handleSubmit(I)},(0,u.__)("Ok","tutor"))))),(0,a.tZ)(Se.Z,{isOpen:E,triggerRef:S,isLoading:T.isPending,closePopover:B.ZT,maxWidth:"258px",title:(0,u.sprintf)((0,u.__)('Delete topic "%s"',"tutor"),r.title),message:(0,u.__)("Are you sure you want to delete this content from your course? This cannot be undone.","tutor"),animationType:Oa.ru.slideUp,arrow:"auto",hideArrow:true,confirmButton:{text:(0,u.__)("Delete","tutor"),variant:"text",isDelete:true},cancelButton:{text:(0,u.__)("Cancel","tutor"),variant:"text"},onConfirmation:ds(is().mark((function t(){return is().wrap((function t(e){while(1)switch(e.prev=e.next){case 0:e.next=2;return T.mutateAsync(b);case 2:k(false);h===null||h===void 0?void 0:h();case 4:case"end":return e.stop()}}),t)}))),onCancel:function t(){return k(false)}}))};const Zs=ws;var xs=true?{name:"jjyo93",styles:"padding-right:0"}:0;var Os=true?{name:"18g08sj",styles:"padding-bottom:0"}:0;var Es=true?{name:"21xn5r",styles:"transform:rotate(180deg)"}:0;var ks={header:function t(e){var r=e.isCollapsed,n=e.isEdit,i=e.isDeletePopoverOpen,o=e.isDragging;return(0,a.iv)("padding:",Q.W0[12]," ",Q.W0[16],";",pt.i.display.flex("column"),";[data-toggle-collapse]{transition:transform 0.3s ease-in-out;",!r&&Es,";}",!r&&(0,a.iv)("border-bottom:1px solid ",Q.Jv.stroke.divider,";"+(true?"":0),true?"":0)," ",!n&&Os," ",!n&&!i&&(0,a.iv)("[data-visually-hidden]{opacity:0;transition:",!o?"opacity 0.3s ease-in-out":"none",";}:hover,:focus-within{[data-visually-hidden]{opacity:",o?0:1,";}}"+(true?"":0),true?"":0)," ",Q.Uo.smallTablet,"{[data-visually-hidden]{opacity:1;}}"+(true?"":0),true?"":0)},headerContent:function t(e){var r=e.isSaved,n=r===void 0?true:r;return(0,a.iv)("display:grid;grid-template-columns:",n?"1fr auto":"1fr",";gap:",Q.W0[12],";width:100%;padding-bottom:",Q.W0[12],";"+(true?"":0),true?"":0)},grabberInput:(0,a.iv)(pt.i.display.flex(),";align-items:center;gap:",Q.W0[8],";svg{color:",Q.Jv.color.black[40],";flex-shrink:0;}"+(true?"":0),true?"":0),grabButton:function t(e){var r=e.isDragging,n=r===void 0?false:r;return(0,a.iv)(pt.i.resetButton,";",pt.i.flexCenter(),";cursor:",n?"grabbing":"grab",";:disabled{cursor:not-allowed;}&:focus-visible{outline:2px solid ",Q.Jv.stroke.brand,";outline-offset:1px;border-radius:",Q.E0[2],";}"+(true?"":0),true?"":0)},title:function t(e){var r=e.isEdit;return(0,a.iv)(F.c.body(),";color:",Q.Jv.text.hints,";width:100%;",!r&&(0,a.iv)(pt.i.text.ellipsis(1),";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},description:function t(e){var r=e.isEdit;return(0,a.iv)(F.c.caption(),";color:",Q.Jv.text.hints,";padding-inline:",Q.W0[8],";margin-left:",Q.W0[24],";padding-bottom:",Q.W0[12],";",!r&&(0,a.iv)(pt.i.text.ellipsis(2),";"+(true?"":0),true?"":0)," ",r&&xs,";"+(true?"":0),true?"":0)},footer:(0,a.iv)("width:100%;text-align:right;",pt.i.display.flex(),";gap:",Q.W0[8],";justify-content:end;"+(true?"":0),true?"":0),actions:(0,a.iv)(pt.i.display.flex(),";align-items:start;gap:",Q.W0[8],";justify-content:end;"+(true?"":0),true?"":0)};var qs=r(7363);function Ss(t){"@babel/helpers - typeof";return Ss="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ss(t)}function js(){js=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return js.apply(this,arguments)}function Ps(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Ps=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function t(e,r,n){return e[r]=n}}function c(t,e,r,i){var o=e&&e.prototype instanceof f?e:f,a=Object.create(o.prototype),u=new E(i||[]);return n(a,"_invoke",{value:w(t,r,u)}),a}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};s(h,o,(function(){return this}));var y=Object.getPrototypeOf,m=y&&y(y(k([])));m&&m!==e&&r.call(m,o)&&(h=m);var g=v.prototype=f.prototype=Object.create(h);function _(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function b(t,e){function i(n,o,a,u){var s=l(t[n],t,o);if("throw"!==s.type){var c=s.arg,d=c.value;return d&&"object"==Ss(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){i("next",t,a,u)}),(function(t){i("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return i("throw",t,a,u)}))}u(s.arg)}var o;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){i(r,n,t,e)}))}return o=o?o.then(a,a):a()}})}function w(t,e,r){var n="suspendedStart";return function(i,o){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===i)throw o;return q()}for(r.method=i,r.arg=o;;){var a=r.delegate;if(a){var u=Z(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var s=l(t,e,r);if("normal"===s.type){if(n=r.done?"completed":"suspendedYield",s.arg===d)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(n="completed",r.method="throw",r.arg=s.arg)}}}function Z(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,Z(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var i=l(n,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,d;var o=i.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function x(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(x,this),this.reset(!0)}function k(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return i.next=i}}return{next:q}}function q(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=s(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,s(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},_(b.prototype),s(b.prototype,a,(function(){return this})),t.AsyncIterator=b,t.async=function(e,r,n,i,o){void 0===o&&(o=Promise);var a=new b(c(e,r,n,i),o);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},_(g),s(g,u,"Generator"),s(g,o,(function(){return this})),s(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,E.prototype={constructor:E,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function i(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],u=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(s&&c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function t(e,n){for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var i=n.completion;if("throw"===i.type){var o=i.arg;O(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function As(t,e,r,n,i,o,a){try{var u=t[o](a);var s=u.value}catch(t){r(t);return}if(u.done){e(s)}else{Promise.resolve(s).then(n,i)}}function Ts(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var o=t.apply(e,r);function a(t){As(o,n,i,a,u,"next",t)}function u(t){As(o,n,i,a,u,"throw",t)}a(undefined)}))}}function Cs(t,e){return zs(t)||Ws(t,e)||Ds(t,e)||Is()}function Is(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Ds(t,e){if(!t)return;if(typeof t==="string")return Ls(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ls(t,e)}function Ls(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Ws(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],s=!0,c=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,i=t}finally{try{if(!s&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw i}}return u}}function zs(t){if(Array.isArray(t))return t}function Ns(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Us(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ns(Object(r),!0).forEach((function(e){Js(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ns(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Js(t,e,r){e=Qs(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Qs(t){var e=Fs(t,"string");return Ss(e)==="symbol"?e:String(e)}function Fs(t,e){if(Ss(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Ss(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Ms(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Bs={lesson:{name:"lesson",color:Q.Jv.icon["default"]},tutor_quiz:{name:"quiz",color:Q.Jv.design.warning},tutor_assignments:{name:"assignment",color:Q.Jv.icon.processing},tutor_zoom_meeting:{name:"zoomColorize",color:""},"tutor-google-meet":{name:"googleMeetColorize",color:""},tutor_h5p_quiz:{name:"interactiveQuiz",color:"#C984FE"}};var Gs={tutor_assignments:(0,u.__)("Are you sure you want to delete this assignment? All existing assignment submissions will be permanently deleted.","tutor"),tutor_quiz:(0,u.__)("Are you sure you want to delete this quiz? All existing quiz attempts will be permanently deleted.","tutor"),tutor_h5p_quiz:(0,u.__)("Are you sure you want to delete this interactive quiz? All existing quiz attempts will be permanently deleted.","tutor")};var Rs={lesson:Ee,tutor_quiz:Cu,tutor_assignments:ut,tutor_h5p_quiz:Cu};var Vs={lesson:(0,u.__)("Lesson","tutor"),tutor_quiz:(0,u.__)("Quiz","tutor"),tutor_assignments:(0,u.__)("Assignment","tutor"),tutor_h5p_quiz:(0,u.__)("Interactive Quiz","tutor")};var Hs={lesson:"lesson",tutor_quiz:"quiz",tutor_assignments:"assignment",tutor_h5p_quiz:"interactiveQuiz"};var Ks=function t(e){return(0,o.cP)(Us(Us({},e),{},{wasDragging:true}))};var Ys=!!U.y.tutor_pro_url;var $s=(0,N.zs)();var Xs=function t(e){var r=e.type,n=e.topic,i=e.content,c=e.onCopy,l=e.onDelete,d=e.isOverlay,v=d===void 0?false:d;var y=(0,N.Wj)("topic-",n.id);var b=(0,N.Wj)("content-",i.id);var x=(0,m.NL)();var E=x.getQueryData(["CourseDetails",Number($s)]);var S=(0,g.Gc)();var j=(0,s.useState)(null),P=Cs(j,2),A=P[0],T=P[1];var C=(0,s.useState)(false),I=Cs(C,2),D=I[0],L=I[1];var W=(0,s.useRef)(null);var U=(0,s.useRef)(null);var F=Bs[r];var M=(0,o.nB)({id:i.id,data:{type:"content"},animateLayoutChanges:Ks}),G=M.attributes,R=M.listeners,V=M.setNodeRef,H=M.transform,K=M.transition,Y=M.isDragging;var $={transform:h.ux.Transform.toString(H),transition:K,opacity:Y?.3:undefined,background:Y?Q.Jv.stroke.hover:undefined};var X=(0,w.d)(),tt=X.showModal;var et=(0,z.P_)();var rt=(0,z.PI)();var nt=Vt();var it=(0,z.PI)();var ot=(0,z.PI)();var at=Wt();var ut=function t(){var e=r;if(Rs[e]){tt({component:Rs[e],props:Us({contentDripType:S.watch("contentDripType"),topicId:y,lessonId:b,assignmentId:b,quizId:b,title:Vs[e],subtitle:(0,u.sprintf)((0,u.__)("Topic: %s","tutor"),n.title),icon:(0,a.tZ)(p.Z,{name:Hs[e],height:24,width:24})},r==="tutor_h5p_quiz"&&{contentType:"tutor_h5p_quiz"}),closeOnEscape:!["tutor_quiz","tutor_h5p_quiz"].includes(r)})}if(r==="tutor_zoom_meeting"){T("tutor_zoom_meeting")}if(r==="tutor-google-meet"){T("tutor-google-meet")}};var st=function(){var t=Ts(Ps().mark((function t(){return Ps().wrap((function t(e){while(1)switch(e.prev=e.next){case 0:if(!["lesson","tutor_assignments"].includes(r)){e.next=5;break}e.next=3;return rt.mutateAsync(b);case 3:e.next=18;break;case 5:if(!["tutor_quiz","tutor_h5p_quiz"].includes(r)){e.next=10;break}e.next=8;return nt.mutateAsync(b);case 8:e.next=18;break;case 10:if(!(r==="tutor-google-meet")){e.next=15;break}e.next=13;return it.mutateAsync(b);case 13:e.next=18;break;case 15:if(!(r==="tutor_zoom_meeting")){e.next=18;break}e.next=18;return ot.mutateAsync(b);case 18:L(false);l===null||l===void 0?void 0:l();case 20:case"end":return e.stop()}}),t)})));return function e(){return t.apply(this,arguments)}}();var lt=function t(){var e={lesson:"lesson",tutor_assignments:"assignment",tutor_quiz:"quiz",tutor_h5p_quiz:"quiz"};et.mutateAsync({course_id:$s,content_id:b,content_type:e[r]});c===null||c===void 0?void 0:c()};return(0,a.tZ)(qs.Fragment,null,(0,a.tZ)("div",js({},G,{css:rc.wrapper({isDragging:Y,isOverlay:v,isActive:A===r||D||et.isPending}),ref:V,style:$}),(0,a.tZ)("div",js({css:rc.iconAndTitle({isDragging:v})},R),(0,a.tZ)("div",{"data-content-icon":true},(0,a.tZ)(p.Z,{name:F.name,width:24,height:24,style:(0,a.iv)("color:",F.color,";"+(true?"":0),true?"":0)})),(0,a.tZ)("div",{"data-bar-icon":true},(0,a.tZ)(p.Z,{name:"bars",width:24,height:24})),(0,a.tZ)("p",{css:rc.title,onClick:ut,onKeyDown:B.ZT},(0,a.tZ)("span",{dangerouslySetInnerHTML:{__html:i.title}}),(0,a.tZ)(Z.Z,{when:(r==="tutor_quiz"||r==="tutor_h5p_quiz")&&!!i.total_question},(0,a.tZ)("span",{"data-question-count":true},"(",(0,u.sprintf)((0,u.__)("%s Questions","tutor"),i.total_question),")")))),(0,a.tZ)("div",{css:rc.actions,"data-actions":true},(0,a.tZ)(Z.Z,{when:r==="tutor_quiz"},(0,a.tZ)(ct.Z,{content:(0,u.__)("Export Quiz","tutor"),delay:200},(0,a.tZ)(Z.Z,{when:!Ys,fallback:(0,a.tZ)(Z.Z,{when:(0,B.ro)(J.AO.QUIZ_EXPORT_IMPORT)},(0,a.tZ)("button",{type:"button",css:pt.i.actionButton,onClick:function t(){at.mutate(b)}},(0,a.tZ)(p.Z,{name:"export",width:24,height:24})))},(0,a.tZ)(_.Z,{size:"tiny"},(0,a.tZ)("button",{type:"button",css:pt.i.actionButton,disabled:true,onClick:B.ZT},(0,a.tZ)(p.Z,{name:"export",width:24,height:24})))))),(0,a.tZ)(ct.Z,{content:(0,u.__)("Edit","tutor"),delay:200},(0,a.tZ)("button",{ref:W,type:"button",css:pt.i.actionButton,onClick:ut},(0,a.tZ)(p.Z,{name:"edit",width:24,height:24}))),(0,a.tZ)(Z.Z,{when:!["tutor_zoom_meeting","tutor_zoom_meeting"].includes(r)},(0,a.tZ)(Z.Z,{when:!et.isPending,fallback:(0,a.tZ)(f.ZP,{size:24})},(0,a.tZ)(ct.Z,{content:(0,u.__)("Duplicate","tutor"),delay:200},(0,a.tZ)(Z.Z,{when:!Ys,fallback:(0,a.tZ)("button",{type:"button",css:pt.i.actionButton,onClick:lt},(0,a.tZ)(p.Z,{name:"copyPaste",width:24,height:24}))},(0,a.tZ)(_.Z,{size:"tiny"},(0,a.tZ)("button",{disabled:true,type:"button",css:pt.i.actionButton,onClick:B.ZT},(0,a.tZ)(p.Z,{name:"copyPaste",width:24,height:24}))))))),(0,a.tZ)(ct.Z,{content:(0,u.__)("Delete","tutor"),delay:200},(0,a.tZ)("button",{ref:U,type:"button",css:pt.i.actionButton,onClick:function t(){L(true)}},(0,a.tZ)(p.Z,{name:"delete",width:24,height:24}))))),(0,a.tZ)(O.Z,{triggerRef:W,isOpen:A!==null,closePopover:B.ZT,maxWidth:"306px",closeOnEscape:false,arrow:J.iM.isAboveMobile?"auto":"absoluteCenter",hideArrow:true},(0,a.tZ)(Z.Z,{when:A==="tutor_zoom_meeting"},(0,a.tZ)(q.Z,{data:null,topicId:y,meetingHost:(E===null||E===void 0?void 0:E.zoom_users)||{},onCancel:function t(){return T(null)},meetingId:b})),(0,a.tZ)(Z.Z,{when:A==="tutor-google-meet"},(0,a.tZ)(k.Z,{data:null,topicId:y,onCancel:function t(){return T(null)},meetingId:b}))),(0,a.tZ)(Se.Z,{isOpen:D,isLoading:rt.isPending||nt.isPending||it.isPending||ot.isPending,triggerRef:U,closePopover:B.ZT,maxWidth:"258px",title:(0,u.sprintf)((0,u.__)('Delete "%s"',"tutor"),i.title),message:Gs[r]||(0,u.__)("Are you sure you want to delete this content from your course? This cannot be undone.","tutor"),animationType:Oa.ru.slideUp,arrow:"auto",hideArrow:true,confirmButton:{text:(0,u.__)("Delete","tutor"),variant:"text",isDelete:true},cancelButton:{text:(0,u.__)("Cancel","tutor"),variant:"text"},onConfirmation:st,onCancel:function t(){return L(false)}}))};const tc=Xs;var ec=true?{name:"1jfpmb0",styles:"[data-content-icon]{display:none;}[data-bar-icon]{display:block;}cursor:grabbing"}:0;var rc={wrapper:function t(e){var r=e.isDragging,n=r===void 0?false:r,i=e.isOverlay,o=i===void 0?false:i,u=e.isActive,s=u===void 0?false:u;return(0,a.iv)("width:100%;padding:",Q.W0[10]," ",Q.W0[8],";border:1px solid transparent;border-radius:",Q.E0[6],";display:flex;justify-content:space-between;align-items:center;[data-content-icon],[data-bar-icon]{display:flex;height:24px;}:hover,:focus-within{border-color:",Q.Jv.stroke.border,";background-color:",Q.Jv.background.white,";[data-content-icon]{display:none;}[data-bar-icon]{display:block;}[data-actions]{opacity:",n?0:1,";}}",s&&(0,a.iv)("border-color:",Q.Jv.stroke.border,";background-color:",Q.Jv.background.white,";[data-content-icon]{display:flex;}[data-bar-icon]{display:none;}[data-actions]{opacity:1;}"+(true?"":0),true?"":0)," ",o&&(0,a.iv)("box-shadow:",Q.AF.drag,";border-color:",Q.Jv.stroke.border,";background-color:",Q.Jv.background.white,";[data-actions]{opacity:1;}"+(true?"":0),true?"":0)," ",Q.Uo.smallTablet,"{[data-actions]{opacity:1;}}"+(true?"":0),true?"":0)},title:(0,a.iv)(F.c.caption(),";color:",Q.Jv.text.title,";display:flex;align-items:center;gap:",Q.W0[4],";cursor:pointer;span{&:first-of-type{",pt.i.text.ellipsis(2),";}}[data-question-count]{color:",Q.Jv.text.hints,";}"+(true?"":0),true?"":0),iconAndTitle:function t(e){var r=e.isDragging,n=r===void 0?false:r;return(0,a.iv)("display:flex;align-items:center;gap:",Q.W0[8],";cursor:grab;flex-grow:1;[data-bar-icon]{display:none;}",n&&ec,";"+(true?"":0),true?"":0)},actions:(0,a.iv)("display:flex;opacity:0;align-items:start;gap:",Q.W0[8],";justify-content:end;"+(true?"":0),true?"":0)};function nc(t){"@babel/helpers - typeof";return nc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},nc(t)}function ic(){ic=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return ic.apply(this,arguments)}function oc(t,e){return lc(t)||cc(t,e)||uc(t,e)||ac()}function ac(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function uc(t,e){if(!t)return;if(typeof t==="string")return sc(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sc(t,e)}function sc(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function cc(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],s=!0,c=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,i=t}finally{try{if(!s&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw i}}return u}}function lc(t){if(Array.isArray(t))return t}function dc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function fc(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?dc(Object(r),!0).forEach((function(e){pc(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):dc(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function pc(t,e,r){e=vc(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function vc(t){var e=hc(t,"string");return nc(e)==="symbol"?e:String(e)}function hc(t,e){if(nc(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(nc(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var yc=function t(e){return(0,o.cP)(fc(fc({},e),{},{wasDragging:true}))};var mc=function t(e){var r;var n=e.topic,i=e.onDelete,u=e.onCopy,c=e.onCollapse,l=e.onEdit,d=e.isOverlay,f=d===void 0?false:d;var p=(0,s.useState)(false),v=oc(p,2),m=v[0],g=v[1];var _=(0,s.useState)(!n.isSaved),b=oc(_,2),w=b[0],x=b[1];var O=(0,s.useRef)(null);var E=(0,s.useRef)(null);var k=(0,y.q_)({height:!n.isCollapsed?(r=O.current)===null||r===void 0?void 0:r.scrollHeight:0,opacity:!n.isCollapsed?1:0,overflow:"hidden",config:{duration:300,easing:function t(e){return e*(2-e)}}},[n.contents.length]),q=oc(k,2),S=q[0],j=q[1];(0,s.useEffect)((function(){var t=function t(e){if((0,Pr.$K)(E.current)&&!E.current.contains(e.target)){g(false)}};document.addEventListener("click",t);return function(){return document.removeEventListener("click",t)}}),[w]);var P=(0,o.nB)({id:n.id,data:{type:"topic"},animateLayoutChanges:yc}),A=P.attributes,T=P.listeners,C=P.setNodeRef,I=P.transform,D=P.transition,L=P.isDragging;var W=(0,s.useCallback)((function(t){if(t){C(t);E.current=t}}),[C]);var z={transform:h.ux.Transform.toString(I),transition:D,opacity:L?.3:undefined,background:L?Q.Jv.stroke.hover:undefined};(0,s.useEffect)((function(){if((0,Pr.$K)(O.current)){j.start({height:!n.isCollapsed?O.current.scrollHeight:0,opacity:!n.isCollapsed?1:0})}}),[n.isCollapsed,n.contents.length]);return(0,a.tZ)("div",ic({},n.isSaved?A:{},{css:_c.wrapper({isActive:m||w,isOverlay:f}),onClick:function t(){return g(true)},onKeyDown:B.ZT,tabIndex:-1,ref:W,style:z}),(0,a.tZ)(Zs,{isActive:m,isDragging:L,isEdit:w,listeners:T,onCollapse:function t(e){c===null||c===void 0?void 0:c(e)},onDelete:i,onEdit:function t(e){l===null||l===void 0?void 0:l(e)},onCopy:function t(e){u===null||u===void 0?void 0:u(e)},topic:n,setIsEdit:x}),(0,a.tZ)(y.q.div,{style:fc({},S)},(0,a.tZ)("div",{css:_c.content,ref:O},(0,a.tZ)(Z.Z,{when:n.contents.length>0},(0,a.tZ)(o.Fo,{items:n.contents.map((function(t){return fc(fc({},t),{},{id:t.ID})})),strategy:o.qw},(0,a.tZ)("div",null,(0,a.tZ)(Kr.Z,{each:n.contents},(function(t){return(0,a.tZ)(tc,{key:t.ID,type:t.post_type,topic:n,content:{id:t.ID,title:t.post_title,total_question:t.total_question||0}})}))))),(0,a.tZ)($u,{topic:n}))))};const gc=mc;var _c={wrapper:function t(e){var r=e.isActive,n=r===void 0?false:r,i=e.isOverlay,o=i===void 0?false:i;return(0,a.iv)("border:1px solid ",Q.Jv.stroke["default"],";border-radius:",Q.E0[8],";transition:background-color 0.3s ease-in-out,border-color 0.3s ease-in-out;background-color:",Q.Jv.bg.white,";width:100%;",n&&(0,a.iv)("border-color:",Q.Jv.stroke.brand,";background-color:",Q.Jv.background.hover,";"+(true?"":0),true?"":0)," :hover{background-color:",Q.Jv.background.hover,";}",o&&(0,a.iv)("box-shadow:",Q.AF.drag,";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},content:(0,a.iv)("padding:",Q.W0[16],";",pt.i.display.flex("column"),";gap:",Q.W0[12],";"+(true?"":0),true?"":0)};var bc=function t(e){var r=e.title;return(0,a.tZ)("div",{css:Zc.wrapper},(0,a.tZ)(p.Z,{name:"dragVertical",width:24,height:24}),(0,a.tZ)("span",null,r))};const wc=bc;var Zc={wrapper:(0,a.iv)("display:flex;align-items:center;gap:",Q.W0[8],";border:1px solid ",Q.Jv.stroke["default"],";border-radius:",Q.E0[8],";background-color:",Q.Jv.background.hover,";padding:",Q.W0[12]," ",Q.W0[16],";box-shadow:",Q.AF.drag,";",F.c.body(),";color:",Q.Jv.text.hints,";cursor:grabbing;svg{color:",Q.Jv.color.black[40],";flex-shrink:0;}"+(true?"":0),true?"":0)};var xc=r(2322);var Oc=r(6051);var Ec=r(551);const kc=r.p+"images/7a3f20a77fdf442a94bc38151eadcfcd-curriculum-empty-state-2x.webp";const qc=r.p+"images/986ea3637213dd5da688bb6f1e1319a6-curriculum-empty-state.webp";function Sc(t){"@babel/helpers - typeof";return Sc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Sc(t)}function jc(t){return Tc(t)||Ac(t)||Uc(t)||Pc()}function Pc(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Ac(t){if(typeof Symbol!=="undefined"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function Tc(t){if(Array.isArray(t))return Jc(t)}function Cc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Ic(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Cc(Object(r),!0).forEach((function(e){Dc(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Cc(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Dc(t,e,r){e=Lc(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Lc(t){var e=Wc(t,"string");return Sc(e)==="symbol"?e:String(e)}function Wc(t,e){if(Sc(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Sc(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function zc(t,e){return Fc(t)||Qc(t,e)||Uc(t,e)||Nc()}function Nc(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Uc(t,e){if(!t)return;if(typeof t==="string")return Jc(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Jc(t,e)}function Jc(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Qc(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],s=!0,c=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,i=t}finally{try{if(!s&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw i}}return u}}function Fc(t){if(Array.isArray(t))return t}var Mc=(0,N.zs)();var Bc=function t(){var e=(0,l.s0)();(0,s.useEffect)((function(){if(!Mc){e(Ec.L.Home.buildLink(),{replace:true})}}),[e]);var r=(0,s.useState)(true),h=zc(r,2),y=h[0],m=h[1];var g=(0,s.useState)(null),_=zc(g,2),b=_[0],w=_[1];var x=(0,s.useState)([]),O=zc(x,2),E=O[0],k=O[1];var q=(0,s.useRef)([]);var S=(0,z.rM)(Mc);var j=(0,z.AI)();(0,s.useEffect)((function(){if(E.length===0){return}k((function(t){if(y){q.current=[]}if(!y){q.current=t.reduce((function(t,e){if(e.isSaved){t.push(e.id)}return t}),[])}return t.map((function(t){if(!t.isSaved){return t}return Ic(Ic({},t),{},{isCollapsed:y})}))}))}),[y]);(0,s.useEffect)((function(){var t;if(!((t=S.data)!==null&&t!==void 0&&t.length)){return}var e=function t(e){return S.data.map((function(t,r){var n="topic-".concat(t.id);var i=r===0;var o=q.current.includes(n);var a=e.length?!o:!i;if(i&&!e.length){q.current=[n]}return Ic(Ic({},t),{},{id:n,isCollapsed:a,isSaved:true,contents:t.contents.map((function(t){return Ic(Ic({},t),{},{ID:"content-".concat(t.ID)})}))})}))};k(e)}),[S.data]);var P=(0,s.useMemo)((function(){if(!b){return null}var t=b.toString().includes("topic")?"topic":"content";var e=E.flatMap((function(t){return t.contents}));var r=t==="topic"?E.find((function(t){return t.id===b})):e.find((function(t){return t.ID===b}));return r}),[b,E]);var A=(0,n.Dy)((0,n.VT)(n.we,{activationConstraint:{distance:10}}),(0,n.VT)(n.Lg,{coordinateGetter:o.is}));if(!Mc){return null}if(S.isLoading){return(0,a.tZ)(f.fz,null)}var T=function t(e,r){if(r==="topic"){return E.find((function(t){return t.id===e}))}if(r==="content"){return E.find((function(t){return t.contents.find((function(t){return t.ID===e}))}))}};var C=function t(e){var r=e.active,n=e.over;if(!n||!r){return}if(r.id.toString().includes("content")&&n.id.toString().includes("content")&&r.id!==n.id){var i=T(r.id,"content");var o=T(n.id,"content");if(!i||!o)return;var a=E.findIndex((function(t){return t.id===i.id}));var u=E.findIndex((function(t){return t.id===o.id}));if(o.isCollapsed||a===u){return}var s=i.contents.findIndex((function(t){return t.ID===r.id}));var c=o.contents.findIndex((function(t){return t.ID===n.id}));var l=jc(E);var d=l[a].contents.splice(s,1),f=zc(d,1),p=f[0];l[u].contents.splice(c,0,p);k(l)}if(r.id.toString().includes("content")&&n!==null&&n!==void 0&&n.id.toString().includes("topic")&&r.id!==n.id){var v=T(r.id,"content");var h=T(n.id,"topic");if(!v||!h||h.contents.length>0||h.isCollapsed){return}var y=E.findIndex((function(t){return t.id===v.id}));var m=E.findIndex((function(t){return t.id===h.id}));if(y===m){return}var g=v.contents.findIndex((function(t){return t.ID===r.id}));var _=jc(E);var b=_[y].contents.splice(g,1),w=zc(b,1),Z=w[0];_[m].contents.push(Z);k(_)}};var I=function t(e){var r,n;var i=e.active,o=e.over;if(!o){w(null);return}var a=jc(E);if(i.id.toString().includes("topic")&&o!==null&&o!==void 0&&o.id.toString().includes("topic")&&i&&i.id!==o.id){var u=E.findIndex((function(t){return t.id===i.id}));var s=E.findIndex((function(t){return t.id===o.id}));a=(0,B.Ao)(a,u,s);k(a)}if(i.id.toString().includes("content")&&o!==null&&o!==void 0&&o.id.toString().includes("content")&&i&&i.id!==o.id){var c=T(i.id,"content");var l=T(o.id,"content");if(!c||!l||l.isCollapsed){return}var d=E.findIndex((function(t){return t.id===c.id}));var f=E.findIndex((function(t){return t.id===l.id}));var p=c.contents.findIndex((function(t){return t.ID===i.id}));var v=l.contents.findIndex((function(t){return t.ID===o.id}));if(d===f){a[d].contents=(0,B.Ao)(a[d].contents,p,v);k(a)}else{var h=a[d].contents.splice(p,1),y=zc(h,1),m=y[0];a[f].contents.splice(v,0,m);k(a)}}if(i.id.toString().includes("content")&&o.id.toString().includes("topic")&&i&&i.id!==o.id){var g=T(i.id,"content");var _=T(o.id,"topic");if(!g||!_||_.isCollapsed){return}var b=E.findIndex((function(t){return t.id===g.id}));var Z=E.findIndex((function(t){return t.id===_.id}));var x=g.contents.findIndex((function(t){return t.ID===i.id}));var O=a[b].contents.splice(x,1),q=zc(O,1),P=q[0];a[Z].contents.push(P);k(a)}var A=a.reduce((function(t,e,r){var n=0;t[r]={topic_id:(0,N.Wj)("topic-",e.id),lesson_ids:e.contents.reduce((function(t,e){t[n]=(0,N.Wj)("content-",e.ID);n++;return t}),{})};return t}),{});j.mutate(Ic({tutor_topics_lessons_sorting:A},i.id.toString().includes("content")&&{"content_parent[parent_topic_id]":(r=S.data)===null||r===void 0?void 0:(n=r.find((function(t){return t.contents.find((function(t){return String(t.ID)===(0,N.Wj)("content-",o.id)}))})))===null||n===void 0?void 0:n.id,"content_parent[content_id]":(0,N.Wj)("content-",i.id)}));w(null)};var D=function t(e,r){k((function(t){return t.filter((function(t,r){return r!==e}))}));q.current=q.current.filter((function(t){return t!==r}))};var L=function t(e){k((function(t){return t.map((function(t){if(t.id===e){return Ic(Ic({},t),{},{isCollapsed:!t.isCollapsed})}return t}))}));if(!q.current.includes(e)){q.current=[].concat(jc(q.current),[e])}else{q.current=q.current.filter((function(t){return t!==e}))}};return(0,a.tZ)("div",{css:Rc.container},(0,a.tZ)("div",{css:Rc.wrapper},(0,a.tZ)(xc.Z,{title:(0,u.__)("Curriculum","tutor"),backUrl:"/basics",rightButton:(0,a.tZ)(Z.Z,{when:E.some((function(t){return t.isSaved}))},(0,a.tZ)(d.Z,{variant:"text",size:"small",onClick:function t(){return m((function(t){return!t}))}},y?(0,u.__)("Expand All","tutor"):(0,u.__)("Collapse All","tutor")))}),(0,a.tZ)("div",{css:Rc.content},(0,a.tZ)(Z.Z,{when:!S.isLoading&&S.data&&(S.data.length>0||E.length>0),fallback:(0,a.tZ)(v.Z,{emptyStateImage:qc,emptyStateImage2x:kc,imageAltText:(0,u.__)("Empty State Illustration","tutor"),title:(0,u.__)("Start building your course!","tutor"),description:(0,u.__)("Add Topics, Lessons, and Quizzes to get started.","tutor"),actions:(0,a.tZ)(d.Z,{variant:"secondary",icon:(0,a.tZ)(p.Z,{name:"plusSquareBrand",width:24,height:25}),onClick:function t(){k((function(){return[{id:(0,B.x0)(),title:"",summary:"",contents:[],isCollapsed:false,isSaved:false}]}))}},(0,u.__)("Add Topic","tutor"))})},(0,a.tZ)("div",{css:Rc.topicWrapper},(0,a.tZ)(n.LB,{sensors:A,collisionDetection:n.ey,measuring:xn.O,modifiers:[i.hg],onDragStart:function t(e){w(e.active.id)},onDragOver:function t(e){return C(e)},onDragEnd:function t(e){return I(e)}},(0,a.tZ)(o.Fo,{items:E.map((function(t){return Ic(Ic({},t),{},{id:t.id})})),strategy:o.qw},(0,a.tZ)(Kr.Z,{each:E},(function(t,e){return t.isSaved&&(0,a.tZ)(gc,{key:t.id,topic:Ic(Ic({},t),{},{isCollapsed:b!==null&&b!==void 0&&b.toString().includes("topic")?true:t.isCollapsed}),onDelete:function r(){return D(e,t.id)},onCollapse:function t(e){return L(e)},onCopy:function t(e){q.current=[e]},onEdit:function t(e){q.current=[e]}})}))),(0,c.createPortal)((0,a.tZ)(n.y9,null,(0,a.tZ)(Z.Z,{when:P},(function(t){return(0,a.tZ)(wc,{title:b!==null&&b!==void 0&&b.toString().includes("topic")?t.title:t.post_title})}))),document.body)),(0,a.tZ)(Kr.Z,{each:E},(function(t,e){return!t.isSaved&&(0,a.tZ)(gc,{key:t.id,topic:Ic(Ic({},t),{},{isCollapsed:false}),onDelete:function r(){return D(e,t.id)},onCollapse:function t(e){return L(e)},onCopy:function t(e){q.current=[e]},onEdit:function t(e){q.current=[e]}})}))))),(0,a.tZ)(Z.Z,{when:E.length>0},(0,a.tZ)("div",{css:Rc.addButtonWrapper},(0,a.tZ)(d.Z,{variant:"secondary",icon:(0,a.tZ)(p.Z,{name:"plusSquareBrand",width:24,height:24}),onClick:function t(){k((function(t){return[].concat(jc(t.map((function(t){return Ic(Ic({},t),{},{isCollapsed:true})}))),[{id:(0,B.x0)(),title:"",summary:"",contents:[],isCollapsed:false,isSaved:false}])}));q.current=[]}},(0,u.__)("Add Topic","tutor"))))),(0,a.tZ)(Oc.Z,{styleModifier:Rc.navigator}))};const Gc=Bc;var Rc={container:(0,a.iv)("margin-top:",Q.W0[32],";width:100%;",Q.Uo.smallTablet,"{margin-top:",Q.W0[16],";}"+(true?"":0),true?"":0),wrapper:(0,a.iv)("width:100%;",pt.i.display.flex("column"),";gap:",Q.W0[16],";margin:0 auto;"+(true?"":0),true?"":0),content:(0,a.iv)("margin-top:",Q.W0[16],";",Q.Uo.smallMobile,"{margin-top:0;}"+(true?"":0),true?"":0),topicWrapper:(0,a.iv)(pt.i.display.flex("column"),";gap:",Q.W0[16],";align-items:center;"+(true?"":0),true?"":0),addButtonWrapper:(0,a.iv)("path{stroke:",Q.Jv.icon.brand,";}"+(true?"":0),true?"":0),navigator:(0,a.iv)("margin:",Q.W0[40]," auto;"+(true?"":0),true?"":0)}}}]);