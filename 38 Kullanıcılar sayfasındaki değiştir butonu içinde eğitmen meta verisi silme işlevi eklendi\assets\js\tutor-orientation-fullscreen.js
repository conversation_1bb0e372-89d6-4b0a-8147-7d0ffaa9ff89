/**
 * <PERSON><PERSON> LMS Video Orientation and Fullscreen Handler
 *
 * Bu script, mobil cihazlarda ekran döndürüldüğünde veya tam ekran ikonuna tıklandığında
 * videoyu otomatik olarak tam ekrana geçirir.
 */

(function() {
    'use strict';

    // DOM yüklendiğinde çalışacak
    document.addEventListener('DOMContentLoaded', function() {
        // Video elementlerini seç
        const videoPlayers = document.querySelectorAll('.tutor-video-player');
        const plyrPlayers = document.querySelectorAll('.plyr');

        // Ekran yönü değişikliğini dinle
        window.addEventListener('orientationchange', function() {
            // Yatay modda ise (landscape)
            if (window.orientation === 90 || window.orientation === -90) {
                // Tüm video oynatıcıları kontrol et
                videoPlayers.forEach(function(player) {
                    if (player && typeof player.requestFullscreen === 'function') {
                        player.requestFullscreen().catch(err => {
                            console.log('Tam ekran yapılamadı:', err);
                        });
                    }
                });

                // Plyr oynatıcıları kontrol et
                plyrPlayers.forEach(function(player) {
                    if (player && player.plyr && typeof player.plyr.fullscreen === 'object') {
                        player.plyr.fullscreen.enter().catch(err => {
                            console.log('Plyr tam ekran yapılamadı:', err);
                        });
                    }
                });
            }
        });

        // Tam ekran butonlarını özelleştir
        const fullscreenButtons = document.querySelectorAll('.plyr__control--fullscreen, .tutor-icon-maximize');

        fullscreenButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                const player = button.closest('.tutor-video-player') || button.closest('.plyr');

                if (player) {
                    // Plyr oynatıcı için
                    if (player.classList.contains('plyr')) {
                        if (player.plyr && typeof player.plyr.fullscreen === 'object') {
                            player.plyr.fullscreen.enter().catch(err => {
                                console.log('Plyr tam ekran yapılamadı:', err);

                                // Alternatif yöntem
                                if (player && typeof player.requestFullscreen === 'function') {
                                    player.requestFullscreen().catch(err => {
                                        console.log('Alternatif tam ekran yapılamadı:', err);
                                    });
                                }
                            });
                        }
                    }
                    // Tutor video oynatıcı için
                    else {
                        if (typeof player.requestFullscreen === 'function') {
                            player.requestFullscreen().catch(err => {
                                console.log('Tam ekran yapılamadı:', err);
                            });
                        }
                    }
                }
            });
        });

        // İframe videoları için (YouTube, Vimeo, vb.) - Tam ekran butonu ekleme kısmı kaldırıldı
    });

    // Tarayıcı desteğini kontrol et ve uygun tam ekran API'sini kullan
    function requestFullScreen(element) {
        const requestMethod = element.requestFullscreen ||
                              element.webkitRequestFullscreen ||
                              element.mozRequestFullScreen ||
                              element.msRequestFullscreen;

        if (requestMethod) {
            requestMethod.call(element);
        }
    }
})();
