<?php
/**
 * Tutor LMS için özel fonksiyonlar
 *
 * <PERSON><PERSON>, Tutor LMS eklentisine özel stil ve script dosyalarını ekler.
 * Kurs izleme ekranı için optimize edilmiş kodlar içerir.
 *
 * @package Tutor
 * @subpackage Functions
 * <AUTHOR> <<EMAIL>>
 * @since 1.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Özel işleyicileri dahil et
 *
 * @since 1.0.0
 */
require_once __DIR__ . '/includes/video-fullscreen-handler.php';

/**
 * Modern video oynatıcı stil ve scriptlerini ekle
 *
 * @since 1.0.0
 */
function tutor_modern_video_player_assets() {
    // Modern video oynatıcı stili
    wp_enqueue_style(
        'tutor-video-player-modern',
        plugin_dir_url( __FILE__ ) . 'assets/css/tutor-video-player-modern.css',
        array(),
        TUTOR_VERSION
    );

    // Modern yükleme animasyonu stili
    wp_enqueue_style(
        'tutor-loading-spinner',
        plugin_dir_url( __FILE__ ) . 'assets/css/tutor-loading-spinner.css',
        array(),
        TUTOR_VERSION
    );

    // Modern video player JavaScript - Optimize edilmiş
    wp_enqueue_script(
        'tutor-video-player-modern',
        plugin_dir_url( __FILE__ ) . 'assets/js/tutor-video-player-modern.js',
        array('jquery'),
        TUTOR_VERSION,
        true
    );

    // Kurs içeriği arama özelliği için JavaScript
    wp_enqueue_script(
        'tutor-content-search',
        plugin_dir_url( __FILE__ ) . 'assets/js/tutor-content-search.js',
        array('jquery'),
        TUTOR_VERSION,
        true
    );

    // Arama sırasında açıklama alanını gizlemek için JavaScript
    wp_enqueue_script(
        'tutor-search',
        plugin_dir_url( __FILE__ ) . 'assets/js/tutor-search.js',
        array('jquery'),
        time(),
        true
    );
}
add_action( 'wp_enqueue_scripts', 'tutor_modern_video_player_assets' );

/**
 * Tutor LMS için özel CSS dosyalarını ekle
 *
 * @since 1.0.0
 */
function tutor_custom_css() {
    wp_enqueue_style(
        'tutor-custom-styles',
        plugin_dir_url( __FILE__ ) . 'assets/css/custom-learning-page.css',
        array(),
        time() // Cache busting için zaman damgası kullanıyoruz
    );

    // Yorum sistemi için özel CSS
    wp_enqueue_style(
        'tutor-comments-custom',
        plugin_dir_url( __FILE__ ) . 'assets/css/tutor-comments-custom.css',
        array(),
        time()
    );

    // Sidebar tamamlama ikonları düzeltmesi için CSS
    wp_enqueue_style(
        'tutor-sidebar-completion-icons-fix',
        plugin_dir_url( __FILE__ ) . 'assets/css/sidebar-completion-icons-fix.css',
        array(),
        time()
    );

    // Dashboard için özel CSS
    wp_enqueue_style(
        'tutor-dashboard-custom',
        plugin_dir_url( __FILE__ ) . 'assets/css/custom-dashboard-page.css',
        array(),
        time()
    );

    // Avatar dropdown menüsü için CSS
    wp_enqueue_style(
        'tutor-avatar-dropdown',
        plugin_dir_url( __FILE__ ) . 'assets/css/avatar-dropdown.css',
        array(),
        time()
    );

    // Özel kaydırma çubuğu için CSS
    wp_enqueue_style(
        'tutor-custom-scrollbar',
        plugin_dir_url( __FILE__ ) . 'assets/css/custom-scrollbar.css',
        array(),
        time()
    );

    // Modern dashboard stilleri için CSS
    wp_enqueue_style(
        'tutor-modern-dashboard',
        plugin_dir_url( __FILE__ ) . 'assets/css/modern-dashboard.css',
        array('tutor-dashboard-custom', 'tutor-avatar-dropdown', 'tutor-custom-scrollbar'),
        time()
    );

    // Görünürlük düzeltmeleri için CSS
    wp_enqueue_style(
        'tutor-visibility-fix',
        plugin_dir_url( __FILE__ ) . 'assets/css/visibility-fix.css',
        array('tutor-modern-dashboard'),
        time()
    );

    // Kurs Oluştur butonu düzeltmesi için CSS
    wp_enqueue_style(
        'tutor-kurs-olustur-button-fix',
        plugin_dir_url( __FILE__ ) . 'assets/css/kurs-olustur-button-fix.css',
        array('tutor-visibility-fix'),
        time()
    );

    // Doğrudan buton düzeltmesi için CSS
    wp_enqueue_style(
        'tutor-direct-button-fix',
        plugin_dir_url( __FILE__ ) . 'assets/css/direct-button-fix.css',
        array('tutor-kurs-olustur-button-fix'),
        time()
    );

    // Header'daki Kurs Oluştur butonunu gizleme CSS
    wp_enqueue_style(
        'tutor-header-button-hide',
        plugin_dir_url( __FILE__ ) . 'assets/css/header-button-hide.css',
        array('tutor-direct-button-fix'),
        time()
    );

    // Çıkış Yap butonu hover düzeltmesi için CSS
    wp_enqueue_style(
        'tutor-logout-hover-fix',
        plugin_dir_url( __FILE__ ) . 'assets/css/logout-hover-fix.css',
        array('tutor-header-button-hide'),
        time()
    );
}
add_action( 'wp_enqueue_scripts', 'tutor_custom_css', 100 ); // Yüksek öncelik (100) eklendi

/**
 * Dashboard Spotlight modu için JavaScript
 *
 * @since 1.0.0
 */
function tutor_dashboard_spotlight_js() {
    // Buton düzeltme JavaScript'i
    wp_enqueue_script(
        'tutor-button-fix',
        plugin_dir_url( __FILE__ ) . 'assets/js/button-fix.js',
        array('jquery'),
        time(),
        true
    );

    wp_enqueue_script(
        'tutor-dashboard-spotlight',
        plugin_dir_url( __FILE__ ) . 'assets/js/dashboard-spotlight.js',
        array('jquery'),
        time(),
        true
    );

    // JavaScript'e gerekli verileri sağla
    $logo_url = plugin_dir_url( __FILE__ ) . 'logo 2.png';
    $site_name = get_bloginfo('name');

    // Spotlight modu aktif mi kontrol et
    $enable_spotlight_mode = tutor_utils()->get_option('enable_spotlight_mode');

    // JavaScript'e veri gönder
    wp_localize_script(
        'tutor-dashboard-spotlight',
        'tutor_dashboard_spotlight_data',
        array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'logo_url' => $logo_url,
            'site_name' => $site_name,
            'spotlight_enabled' => $enable_spotlight_mode === 'on' ? true : false
        )
    );

    // Avatar dropdown menüsü için JavaScript
    wp_enqueue_script(
        'tutor-avatar-dropdown',
        plugin_dir_url( __FILE__ ) . 'assets/js/avatar-dropdown.js',
        array('jquery'),
        time(),
        true
    );

    // Özel kaydırma çubuğu için JavaScript
    wp_enqueue_script(
        'tutor-custom-scrollbar',
        plugin_dir_url( __FILE__ ) . 'assets/js/custom-scrollbar.js',
        array('jquery'),
        time(),
        true
    );

    // Görünürlük düzeltmeleri için JavaScript
    wp_enqueue_script(
        'tutor-visibility-fix',
        plugin_dir_url( __FILE__ ) . 'assets/js/visibility-fix.js',
        array('jquery'),
        time(),
        true
    );
}
add_action( 'wp_enqueue_scripts', 'tutor_dashboard_spotlight_js' );

/**
 * Admin bar margin ayarlaması için JavaScript
 *
 * @since 1.0.0
 */
function tutor_admin_bar_margin_js() {
    // Sadece kurs içeriği sayfalarında yükle
    if (function_exists('is_singular') && (is_singular('lesson') || is_singular('tutor_quiz') || is_singular(tutor()->course_post_type))) {
        wp_enqueue_script(
            'tutor-admin-bar-margin',
            plugin_dir_url( __FILE__ ) . 'assets/js/admin-bar-margin.js',
            array('jquery'),
            time(),
            true
        );
    }
}
add_action( 'wp_enqueue_scripts', 'tutor_admin_bar_margin_js' );

/**
 * Yorum cevaplama sistemi için JavaScript
 *
 * @since 1.0.0
 */
function tutor_comments_toggle_js() {
    // Sadece kurs içeriği sayfalarında yükle
    if (function_exists('is_singular') && (is_singular('lesson') || is_singular('tutor_quiz') || is_singular(tutor()->course_post_type))) {
        wp_enqueue_script(
            'tutor-comments-toggle',
            plugin_dir_url( __FILE__ ) . 'assets/js/tutor-comments-toggle.js',
            array('jquery'),
            time(),
            true
        );
    }
}
add_action( 'wp_enqueue_scripts', 'tutor_comments_toggle_js' );

/**
 * Video oynatıcı toggle işlevi için JavaScript
 *
 * @since 1.0.0
 */
function tutor_video_toggle_js() {
    // Sadece kurs içeriği sayfalarında yükle
    if (function_exists('is_singular') && (is_singular('lesson') || is_singular('tutor_quiz') || is_singular(tutor()->course_post_type))) {
        wp_enqueue_script(
            'tutor-video-toggle',
            plugin_dir_url( __FILE__ ) . 'assets/js/tutor-video-toggle-optimized.js',
            array('jquery'),
            TUTOR_VERSION,
            true
        );

        // Sidebar toggle işlevi için JavaScript
        wp_enqueue_script(
            'tutor-sidebar-toggle',
            plugin_dir_url( __FILE__ ) . 'assets/js/tutor-sidebar-toggle.js',
            array('jquery'),
            time(),
            true
        );

        // Sidebar overlay işlevi için JavaScript
        wp_enqueue_script(
            'tutor-sidebar-overlay',
            plugin_dir_url( __FILE__ ) . 'assets/js/tutor-sidebar-overlay.js',
            array('jquery'),
            time(),
            true
        );

        // Özel çeviriler için JavaScript
        wp_enqueue_script(
            'tutor-custom-translations',
            plugin_dir_url( __FILE__ ) . 'assets/js/custom-translations.js',
            array('jquery'),
            time(),
            true
        );

        // Kurs arka planı için JavaScript
        wp_enqueue_script(
            'tutor-course-background',
            plugin_dir_url( __FILE__ ) . 'assets/js/course-background.js',
            array('jquery'),
            time(),
            true
        );
    }
}
add_action( 'wp_enqueue_scripts', 'tutor_video_toggle_js' );

/**
 * Renk değişkenlerini hesaplayan JavaScript'i ekle
 *
 * @since 1.0.0
 */
function tutor_color_variables_js() {
    // Tüm sayfalarda yükle
    wp_enqueue_script(
        'tutor-color-variables',
        plugin_dir_url( __FILE__ ) . 'assets/js/color-variables.js',
        array('jquery'),
        time(),
        true
    );
}
add_action( 'wp_enqueue_scripts', 'tutor_color_variables_js', 5 ); // Düşük öncelik (5) ile erken yükle

/**
 * Kurs izleme deneyimi için optimize edilmiş JavaScript
 * Gereksiz elementleri kaldırır ve performansı artırır
 *
 * @since 1.0.0
 */
function tutor_add_progress_to_sidebar() {
    ?>
    <script>
    jQuery(document).ready(function($) {
        // Hide the video title in header (CSS approach is more reliable, but this ensures it works)
        $('.tutor-course-topic-single-header-title').hide();

        // Only for mobile view (under 1200px)
        if ($(window).width() < 1200) {
            // Get course title
            var courseTitle = $('.tutor-course-topic-single-header-title').text().trim();

            // Add course title to mobile progress bar if not already present
            if ($('.mobile-course-title').length === 0) {
                $('.tutor-spotlight-mobile-progress-complete').prepend('<div class="mobile-course-title">' + courseTitle + '</div>');
                $('body').addClass('mobile-progress-added');
            }
        }

        // Optimize image loading in sidebar
        $('.tutor-course-topic-item-thumbnail').each(function() {
            // Add loading="lazy" attribute to images that don't already have it
            if (!$(this).attr('loading')) {
                $(this).attr('loading', 'lazy');
            }

            // Add error handler to replace broken images with gradient placeholder
            $(this).on('error', function() {
                $(this).replaceWith('<div class="tutor-course-topic-item-gradient"></div>');
            });
        });
    });
    </script>
    <?php
}
add_action('wp_footer', 'tutor_add_progress_to_sidebar', 99); // Lower priority for faster page load

/**
 * Ders tamamlama JavaScript dosyasını ekle
 *
 * @since 1.0.0
 */
function tutor_lesson_completion_js() {
    // Sadece ders sayfalarında yükle
    if (function_exists('is_singular') && is_singular('lesson')) {
        // Ders tamamlama script'i
        wp_enqueue_script(
            'tutor-lesson-completion-direct',
            plugin_dir_url( __FILE__ ) . 'assets/js/lesson-completion-direct.js',
            array('jquery'),
            time(),
            true
        );

        // AJAX için gerekli verileri ekle
        wp_localize_script(
            'tutor-lesson-completion-direct',
            'tutor_data',
            array(
                'ajaxurl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('tutor_nonce'),
                'course_id' => tutor_utils()->get_course_id_by_lesson(get_the_ID())
            )
        );
    }
}
add_action('wp_enqueue_scripts', 'tutor_lesson_completion_js');

/**
 * Ders tamamlama durumunu değiştiren AJAX işleyicisi
 *
 * @since 1.0.0
 */
function toggle_lesson_completion() {
    // Nonce kontrolü
    check_ajax_referer('tutor_nonce', '_wpnonce');

    // Kullanıcı giriş yapmış mı kontrol et
    if (!is_user_logged_in()) {
        wp_send_json_error(array('message' => 'Lütfen giriş yapın'));
        return;
    }

    // Parametreleri al
    $lesson_id = isset($_POST['lesson_id']) ? intval($_POST['lesson_id']) : 0;
    $is_completed = isset($_POST['is_completed']) ? $_POST['is_completed'] === 'true' : false;
    $user_id = get_current_user_id();

    if (!$lesson_id) {
        wp_send_json_error(array('message' => 'Geçersiz ders ID'));
        return;
    }

    // Kurs ID'sini bul
    $course_id = tutor_utils()->get_course_id_by_lesson($lesson_id);

    if (!$course_id) {
        wp_send_json_error(array('message' => 'Geçersiz kurs ID'));
        return;
    }

    // Ders tamamlama durumunu değiştir
    if ($is_completed) {
        // Tamamlanmış dersi geri al
        delete_user_meta($user_id, '_tutor_completed_lesson_id_' . $lesson_id);

        // Tutor LMS'in kendi hook'larını çağır
        do_action('tutor_mark_lesson_uncomplete_before', $lesson_id, $user_id);
        do_action('tutor_mark_lesson_uncomplete_after', $lesson_id, $user_id);

        // Kurs ilerleme durumunu al
        $course_progress = tutor_utils()->get_course_completed_percent($course_id);
        $completed_lessons = tutor_utils()->get_completed_lesson_count_by_course($course_id);
        $total_lessons = tutor_utils()->get_lesson_count_by_course($course_id);

        wp_send_json_success(array(
            'completed' => false,
            'message' => 'Ders tamamlanma durumu kaldırıldı',
            'completed_percent' => $course_progress,
            'completed_lessons' => $completed_lessons,
            'total_lessons' => $total_lessons,
            'course_id' => $course_id,
            'lesson_id' => $lesson_id
        ));
    } else {
        // Dersi tamamla
        do_action('tutor_mark_lesson_complete_before', $lesson_id, $user_id);
        update_user_meta($user_id, '_tutor_completed_lesson_id_' . $lesson_id, tutor_time());
        do_action('tutor_mark_lesson_complete_after', $lesson_id, $user_id);

        // Kurs ilerleme durumunu al
        $course_progress = tutor_utils()->get_course_completed_percent($course_id);
        $completed_lessons = tutor_utils()->get_completed_lesson_count_by_course($course_id);
        $total_lessons = tutor_utils()->get_lesson_count_by_course($course_id);

        wp_send_json_success(array(
            'completed' => true,
            'message' => 'Ders tamamlandı',
            'completed_percent' => $course_progress,
            'completed_lessons' => $completed_lessons,
            'total_lessons' => $total_lessons,
            'course_id' => $course_id,
            'lesson_id' => $lesson_id
        ));
    }
}
add_action('wp_ajax_toggle_lesson_completion', 'toggle_lesson_completion');

/**
 * Kurs ilerleme durumunu getiren AJAX işleyicisi
 *
 * @since 1.0.0
 */
function get_course_progress() {
    // Kullanıcı giriş yapmış mı kontrol et
    if (!is_user_logged_in()) {
        wp_send_json_error('Lütfen giriş yapın');
        return;
    }

    // Mevcut ders ID'sini al - AJAX isteğinden veya mevcut sayfadan
    $lesson_id = isset($_POST['lesson_id']) ? intval($_POST['lesson_id']) : get_the_ID();

    if (!$lesson_id) {
        // Referrer URL'den ders ID'sini almaya çalış
        $referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';
        if (preg_match('/\/(\d+)\/?$/', $referer, $matches)) {
            $lesson_id = intval($matches[1]);
        }

        if (!$lesson_id) {
            wp_send_json_error('Geçersiz ders ID');
            return;
        }
    }

    // Dersin ait olduğu kursu bul
    $course_id = 0;
    $ancestors = get_post_ancestors($lesson_id);
    if (!empty($ancestors)) {
        $course_id = array_pop($ancestors);
    }

    if (!$course_id) {
        // Alternatif yöntem: Tutor LMS fonksiyonlarını kullan
        $course_id = tutor_utils()->get_course_id_by_lesson($lesson_id);
    }

    if (!$course_id) {
        wp_send_json_error(array(
            'message' => 'Geçersiz kurs ID',
            'lesson_id' => $lesson_id
        ));
        return;
    }

    // Kurs ilerleme durumunu al
    $course_progress = tutor_utils()->get_course_completed_percent($course_id);

    // Tamamlanmış dersleri al
    $completed_lessons = tutor_utils()->get_completed_lesson_count_by_course($course_id);
    $total_lessons = tutor_utils()->get_lesson_count_by_course($course_id);

    wp_send_json_success(array(
        'completed_percent' => $course_progress,
        'completed_lessons' => $completed_lessons,
        'total_lessons' => $total_lessons,
        'course_id' => $course_id,
        'lesson_id' => $lesson_id
    ));
}




add_action('wp_ajax_get_course_progress', 'get_course_progress');

/**
 * Ders sidebar'ını yeniden yükleyen AJAX işleyicisi
 *
 * @since 1.0.0
 */
function tutor_load_lesson_sidebar() {
    // Nonce kontrolü
    check_ajax_referer('tutor_nonce', '_wpnonce');

    // Kullanıcı giriş yapmış mı kontrol et
    if (!is_user_logged_in()) {
        wp_send_json_error(array('message' => 'Lütfen giriş yapın'));
        return;
    }

    // Kurs ID'sini al
    $course_id = isset($_POST['course_id']) ? intval($_POST['course_id']) : 0;

    if (!$course_id) {
        wp_send_json_error(array('message' => 'Geçersiz kurs ID'));
        return;
    }

    // Sidebar içeriğini oluştur
    ob_start();
    include tutor()->path . 'templates/single/lesson/lesson_sidebar.php';
    $sidebar_content = ob_get_clean();

    wp_send_json_success($sidebar_content);
}
add_action('wp_ajax_tutor_load_lesson_sidebar', 'tutor_load_lesson_sidebar');

/**
 * Topic tamamlanma sayılarını getiren AJAX işleyicisi
 *
 * @since 1.0.0
 */
function get_topic_completion_counts() {
    // Nonce kontrolü
    check_ajax_referer('tutor_nonce', '_wpnonce');

    // Kullanıcı giriş yapmış mı kontrol et
    if (!is_user_logged_in()) {
        wp_send_json_error(array('message' => 'Lütfen giriş yapın'));
        return;
    }

    // Kurs ID'sini al
    $course_id = isset($_POST['course_id']) ? intval($_POST['course_id']) : 0;
    // Ders ID'sini al
    $lesson_id = isset($_POST['lesson_id']) ? intval($_POST['lesson_id']) : 0;

    if (!$course_id) {
        wp_send_json_error(array('message' => 'Geçersiz kurs ID'));
        return;
    }

    // Ders ID varsa, sadece o dersin bulunduğu topic'i güncelle
    if ($lesson_id) {
        // Dersin bulunduğu topic'i bul
        $topic_id = get_post_meta($lesson_id, '_tutor_course_topic_id', true);

        // Topic ID bulunamadıysa alternatif yöntem dene
        if (!$topic_id) {
            // Dersin parent'larını kontrol et
            $parents = get_post_ancestors($lesson_id);
            foreach ($parents as $parent_id) {
                $parent = get_post($parent_id);
                if ($parent && $parent->post_type === 'topics') {
                    $topic_id = $parent_id;
                    break;
                }
            }
        }

        // Topic ID hala bulunamadıysa, tüm topic'leri kontrol et
        if (!$topic_id) {
            $topics = tutor_utils()->get_topics($course_id);
            if ($topics->have_posts()) {
                while ($topics->have_posts()) {
                    $topics->the_post();
                    $current_topic_id = get_the_ID();
                    $topic_lessons = tutor_utils()->get_course_contents_by_topic($current_topic_id, -1);

                    foreach ($topic_lessons->posts as $topic_lesson) {
                        if ($topic_lesson->ID == $lesson_id) {
                            $topic_id = $current_topic_id;
                            break 2; // İki döngüden de çık
                        }
                    }
                }
                wp_reset_postdata();
            }
        }

        // Topic ID bulunduysa, sadece o topic'in tamamlanma sayısını döndür
        if ($topic_id) {
            $total_contents = tutor_utils()->count_completed_contents_by_topic($topic_id);
            $topic_counts = array(
                $topic_id => array(
                    'completed' => isset($total_contents['completed']) ? $total_contents['completed'] : 0,
                    'total' => isset($total_contents['contents']) ? $total_contents['contents'] : 0
                )
            );
            wp_send_json_success($topic_counts);
            return;
        }
    }

    // Ders ID yoksa veya topic bulunamadıysa, tüm topic'leri döndür
    $topics = tutor_utils()->get_topics($course_id);
    $topic_counts = array();

    if ($topics->have_posts()) {
        while ($topics->have_posts()) {
            $topics->the_post();
            $topic_id = get_the_ID();
            $total_contents = tutor_utils()->count_completed_contents_by_topic($topic_id);

            $topic_counts[$topic_id] = array(
                'completed' => isset($total_contents['completed']) ? $total_contents['completed'] : 0,
                'total' => isset($total_contents['contents']) ? $total_contents['contents'] : 0
            );
        }
        wp_reset_postdata();
    }

    wp_send_json_success($topic_counts);
}
add_action('wp_ajax_get_topic_completion_counts', 'get_topic_completion_counts');

/**
 * Spotlight modunun aktif olup olmadığını kontrol eden AJAX işleyicisi
 *
 * @since 1.0.0
 */
function check_spotlight_mode() {
    // Spotlight modu aktif mi kontrol et
    $enable_spotlight_mode = tutor_utils()->get_option('enable_spotlight_mode');

    // Logo URL'sini al
    $logo_url = plugin_dir_url( __FILE__ ) . 'logo 2.png';
    $site_name = get_bloginfo('name');

    wp_send_json_success(array(
        'spotlight_enabled' => $enable_spotlight_mode === 'on' ? true : false,
        'logo_url' => $logo_url,
        'site_name' => $site_name
    ));
}
add_action('wp_ajax_check_spotlight_mode', 'check_spotlight_mode');
add_action('wp_ajax_nopriv_check_spotlight_mode', 'check_spotlight_mode');

/**
 * Include Student Course Assignment class
 *
 * @since 1.0.0
 */
require_once __DIR__ . '/classes/Student_Course_Assignment.php';

/**
 * Abone ve tutor eğitmen rollerine sahip kullanıcılar için admin araç çubuğunu gizle
 * Admin kullanıcılar için ayarları değiştirme
 *
 * @since 1.0.0
 */
function disable_admin_bar_for_specific_roles() {
    // Kullanıcı giriş yapmış mı kontrol et
    if (!is_user_logged_in()) {
        return;
    }

    // Mevcut kullanıcıyı al
    $current_user = wp_get_current_user();

    // Admin kullanıcıları etkileme
    if (in_array('administrator', $current_user->roles)) {
        return;
    }

    // Kullanıcı rollerini kontrol et
    if (!empty($current_user->roles)) {
        // Abone (subscriber) veya tutor eğitmen (tutor_instructor) rolüne sahipse
        if (in_array('subscriber', $current_user->roles) || in_array('tutor_instructor', $current_user->roles)) {
            // Admin araç çubuğunu kapat
            show_admin_bar(false);

            // Kullanıcı meta verisini güncelle
            update_user_meta($current_user->ID, 'show_admin_bar_front', 'false');
        }
    }
}

/**
 * Kurs oluşturucu ekranında admin araç çubuğunu gizle
 *
 * @since 1.0.0
 */
function disable_admin_bar_in_course_builder() {
    // Kullanıcı giriş yapmış mı kontrol et
    if (!is_user_logged_in()) {
        return;
    }

    // Mevcut kullanıcıyı al
    $current_user = wp_get_current_user();

    // Admin kullanıcıları etkileme
    if (in_array('administrator', $current_user->roles)) {
        return;
    }

    // Kullanıcı rollerini kontrol et
    if (!empty($current_user->roles)) {
        // Abone (subscriber) veya tutor eğitmen (tutor_instructor) rolüne sahipse
        if (in_array('subscriber', $current_user->roles) || in_array('tutor_instructor', $current_user->roles)) {
            // Admin araç çubuğunu kapat
            show_admin_bar(false);

            // CSS ile admin araç çubuğunu gizle ve admin bar yükseklik değişkenini kaldır
            echo '<style>
                #wpadminbar { display: none !important; }
                html {
                    margin-top: 0 !important;
                    --wp-admin--admin-bar--height: 0px !important;
                }
                * html body { margin-top: 0 !important; }
                body.admin-bar { margin-top: 0 !important; }
                body.admin-bar .tutor-frontend-dashboard-header { top: 0 !important; }
            </style>';

            // JavaScript ile CSS değişkenini kaldır (bazı temalar için gerekli olabilir)
            echo '<script>
                document.addEventListener("DOMContentLoaded", function() {
                    // HTML etiketindeki admin bar yükseklik değişkenini kaldır
                    document.documentElement.style.setProperty("--wp-admin--admin-bar--height", "0px");

                    // Admin bar sınıfını body elementinden kaldır
                    document.body.classList.remove("admin-bar");
                });
            </script>';
        }
    }
}
add_action('tutor_before_course_builder_load', 'disable_admin_bar_in_course_builder');
add_action('after_setup_theme', 'disable_admin_bar_for_specific_roles');

/**
 * Yeni kullanıcı kaydı sırasında admin araç çubuğu ayarını kapat
 *
 * @since 1.0.0
 * @param int $user_id Yeni oluşturulan kullanıcının ID'si
 */
function disable_admin_bar_on_user_register($user_id) {
    // Kullanıcı nesnesini al
    $user = get_userdata($user_id);

    // Admin kullanıcıları etkileme
    if (in_array('administrator', $user->roles)) {
        return;
    }

    // Kullanıcı rollerini kontrol et
    if (!empty($user->roles)) {
        // Abone (subscriber) veya tutor eğitmen (tutor_instructor) rolüne sahipse
        if (in_array('subscriber', $user->roles) || in_array('tutor_instructor', $user->roles)) {
            // Admin araç çubuğu ayarını kapat
            update_user_meta($user_id, 'show_admin_bar_front', 'false');
        }
    }
}
add_action('user_register', 'disable_admin_bar_on_user_register');

/**
 * Kullanıcı rolü değiştiğinde admin araç çubuğu ayarını kontrol et
 * ve eğitmen rolünden abone rolüne geçişte eğitmen meta verilerini temizle
 *
 * @since 1.0.0
 * @param int $user_id Kullanıcı ID'si
 * @param string $role Yeni rol
 * @param array $old_roles Eski roller
 */
function check_admin_bar_on_role_change($user_id, $role, $old_roles) {
    // Admin rolü ise işlem yapma
    if ($role === 'administrator') {
        return;
    }

    // Yeni rol abone (subscriber) veya tutor eğitmen (tutor_instructor) ise
    if ($role === 'subscriber' || $role === 'tutor_instructor') {
        // Admin araç çubuğu ayarını kapat
        update_user_meta($user_id, 'show_admin_bar_front', 'false');
    }

    // Eğer kullanıcı tutor_instructor rolünden subscriber rolüne geçiyorsa
    if ($role === 'subscriber' && (in_array('tutor_instructor', $old_roles) ||
        (function_exists('tutor') && in_array(tutor()->instructor_role, $old_roles)))) {

        // Eğitmen meta verilerini temizle fonksiyonunu çağır
        clean_instructor_meta_data($user_id);

        // Kullanıcıya bildirim ekle
        add_action('admin_notices', function() use ($user_id) {
            $user = get_userdata($user_id);
            if ($user) {
                ?>
                <div class="notice notice-success is-dismissible">
                    <p><?php echo esc_html($user->display_name); ?> kullanıcısının eğitmen meta verileri başarıyla temizlendi.</p>
                </div>
                <?php
            }
        });
    }
}
add_action('set_user_role', 'check_admin_bar_on_role_change', 10, 3);

/**
 * Kullanıcı rolü değişikliği sonrası eğitmen meta verilerini temizleyen fonksiyon
 * Bu fonksiyon doğrudan çağrılabilir
 *
 * @since 1.0.0
 * @param int $user_id Kullanıcı ID'si
 * @return bool İşlem başarılı oldu mu
 */
function clean_instructor_meta_data($user_id) {
    // Kullanıcı ID'si geçerli mi kontrol et
    if (!$user_id || !get_userdata($user_id)) {
        return false;
    }

    // Eğitmen meta verilerini temizle
    delete_user_meta($user_id, '_is_tutor_instructor');
    delete_user_meta($user_id, '_tutor_instructor_status');
    delete_user_meta($user_id, '_tutor_instructor_approved');
    delete_user_meta($user_id, '_tutor_instructor_show_rejection_message');
    delete_user_meta($user_id, '_is_tutor_instructor_rejected');

    // Kullanıcının rollerini kontrol et
    $user = new WP_User($user_id);

    // Eğer kullanıcı tutor_instructor rolüne sahipse, bu rolü kaldır
    if (function_exists('tutor') && in_array(tutor()->instructor_role, $user->roles)) {
        $user->remove_role(tutor()->instructor_role);
    } else if (in_array('tutor_instructor', $user->roles)) {
        $user->remove_role('tutor_instructor');
    }

    // Eğitmen ile ilgili diğer meta verileri de temizle
    global $wpdb;
    $wpdb->query(
        $wpdb->prepare(
            "DELETE FROM {$wpdb->usermeta} WHERE user_id = %d AND meta_key LIKE %s",
            $user_id,
            '%_tutor_instructor%'
        )
    );

    // Tutor LMS'in kendi fonksiyonlarını kullanarak eğitmen rolünü kaldır
    if (function_exists('tutor_utils')) {
        tutor_utils()->remove_instructor_role($user_id);

        // Tutor LMS'in önbelleğini temizle
        tutor_utils()->delete_tutor_cache_all();
    }

    // Özel bir hook çağır, böylece diğer eklentiler de bu değişikliği yakalayabilir
    do_action('tutor_instructor_to_subscriber', $user_id);

    return true;
}

/**
 * Kullanıcılar listesindeki "Değiştir" butonuna rol değişikliği yapıldığında eğitmen meta verilerini otomatik temizleme özelliği ekle
 * Ayrıca "Eğitmen Meta Verilerini Temizle" butonunu kaldırır
 *
 * @since 1.0.0
 */
function add_role_change_handler_to_users_page() {
    // Sadece admin kullanıcıları için
    if (!current_user_can('administrator')) {
        return;
    }

    // Kullanıcılar sayfasında olup olmadığımızı kontrol et
    $screen = get_current_screen();
    if (!$screen || $screen->base !== 'users') {
        return;
    }

    // JavaScript ekle
    ?>
    <script type="text/javascript">
    jQuery(document).ready(function($) {
        // "Eğitmen Meta Verilerini Temizle" butonunu kaldır
        $('#clean-instructor-meta').remove();

        // Toplu işlem menüsünden "Eğitmen Meta Verilerini Temizle" seçeneğini kaldır
        $('select[name="action"] option[value="clean_instructor_meta"]').remove();
        $('select[name="action2"] option[value="clean_instructor_meta"]').remove();

        // "Değiştir" butonlarına tıklandığında
        $(document).on('click', '.row-actions .edit a, a.editinline', function(e) {
            // Varsayılan davranışı engelleme
            e.preventDefault();

            // Orijinal URL'yi al
            var originalUrl = $(this).attr('href');

            // Kullanıcı düzenleme sayfasına yönlendir
            window.location.href = originalUrl;
        });

        // Hızlı düzenleme formunda rol değişikliği yapıldığında
        $(document).on('change', '.inline-edit-row select[name="role"]', function() {
            var selectedRole = $(this).val();

            // Eğer seçilen rol "subscriber" ise, kullanıcıya bilgi ver
            if (selectedRole === 'subscriber') {
                console.log('Rol "subscriber" olarak değiştirildi. Eğitmen meta verileri otomatik olarak temizlenecek.');

                // Kullanıcı ID'sini al
                var userId = $(this).closest('tr').attr('id').replace('edit-', '');

                // AJAX ile eğitmen meta verilerini temizle
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'clean_instructor_meta',
                        users: [userId],
                        nonce: '<?php echo wp_create_nonce('clean_instructor_meta_nonce'); ?>'
                    },
                    success: function(response) {
                        console.log('Eğitmen meta verileri temizlendi:', response);
                    }
                });
            }
        });

        // "Değiştir" butonlarını güncelle
        $('.row-actions .edit a').each(function() {
            $(this).attr('title', 'Düzenle (Rol değişikliği yapıldığında eğitmen meta verileri otomatik olarak temizlenecektir)');
        });

        // Kullanıcılar listesindeki "Değiştir" butonlarını güncelle
        $('.wp-list-table.users .row-actions').each(function() {
            var $actions = $(this);
            var $editLink = $actions.find('.edit a');

            // Eğer "Değiştir" butonu varsa
            if ($editLink.length > 0) {
                // Butonun metnini güncelle
                $editLink.text('Düzenle');
            }
        });
    });
    </script>
    <?php
}
add_action('admin_footer', 'add_role_change_handler_to_users_page');

/**
 * Eğitmen meta verilerini temizleme AJAX işleyicisi
 *
 * @since 1.0.0
 */
function clean_instructor_meta_ajax_handler() {
    // Nonce kontrolü
    check_ajax_referer('clean_instructor_meta_nonce', 'nonce');

    // Yetki kontrolü
    if (!current_user_can('administrator')) {
        wp_send_json_error('Bu işlemi gerçekleştirmek için yetkiniz yok.');
        return;
    }

    // Kullanıcı ID'lerini al
    $user_ids = isset($_POST['users']) ? array_map('intval', $_POST['users']) : array();

    if (empty($user_ids)) {
        wp_send_json_error('Lütfen en az bir kullanıcı seçin.');
        return;
    }

    $success_count = 0;
    $error_count = 0;

    // Her kullanıcı için eğitmen meta verilerini temizle
    foreach ($user_ids as $user_id) {
        if (clean_instructor_meta_data($user_id)) {
            $success_count++;
        } else {
            $error_count++;
        }
    }

    wp_send_json_success(array(
        'message' => sprintf(
            '%d kullanıcının eğitmen meta verileri başarıyla temizlendi. %d kullanıcı için hata oluştu.',
            $success_count,
            $error_count
        )
    ));
}
add_action('wp_ajax_clean_instructor_meta', 'clean_instructor_meta_ajax_handler');

// Bu bölüm kaldırıldı - Eğitmen meta verileri bilgisi artık gösterilmiyor

/**
 * Kullanıcı profil güncellendiğinde eğitmen meta verilerini otomatik olarak temizle
 *
 * @since 1.0.0
 * @param int $user_id Kullanıcı ID'si
 * @param object $old_user_data Eski kullanıcı verileri
 */
function clean_instructor_meta_on_profile_update($user_id, $old_user_data) {
    // Yetki kontrolü
    if (!current_user_can('administrator')) {
        return;
    }

    // Kullanıcı nesnesini al
    $user = get_userdata($user_id);
    if (!$user) {
        return;
    }

    // Kullanıcının yeni rollerini al
    $new_roles = $user->roles;

    // Kullanıcının eski rollerini al
    $old_roles = $old_user_data->roles;

    // Eğer kullanıcı tutor_instructor rolünden subscriber rolüne geçmişse
    $was_instructor = in_array('tutor_instructor', $old_roles) || (function_exists('tutor') && in_array(tutor()->instructor_role, $old_roles));
    $is_subscriber = in_array('subscriber', $new_roles);

    if ($was_instructor && $is_subscriber) {
        // Eğitmen meta verilerini temizle
        clean_instructor_meta_data($user_id);

        // Kullanıcıya bildirim ekle
        add_action('admin_notices', function() use ($user) {
            ?>
            <div class="notice notice-success is-dismissible">
                <p><?php echo esc_html($user->display_name); ?> kullanıcısının eğitmen meta verileri başarıyla temizlendi.</p>
            </div>
            <?php
        });
    }

    // Eğer kullanıcı subscriber rolünde ve eğitmen meta verileri varsa
    $is_instructor_meta = get_user_meta($user_id, '_is_tutor_instructor', true) || get_user_meta($user_id, '_tutor_instructor_status', true);

    if ($is_subscriber && $is_instructor_meta) {
        // Eğitmen meta verilerini temizle
        clean_instructor_meta_data($user_id);

        // Kullanıcıya bildirim ekle
        add_action('admin_notices', function() use ($user) {
            ?>
            <div class="notice notice-success is-dismissible">
                <p><?php echo esc_html($user->display_name); ?> kullanıcısının eğitmen meta verileri başarıyla temizlendi.</p>
            </div>
            <?php
        });
    }
}
add_action('profile_update', 'clean_instructor_meta_on_profile_update', 10, 2);

/**
 * Kullanıcı profil sayfasında rol değişikliği yapıldığında otomatik temizleme işlemi
 * Bilgi mesajı gösterilmeden çalışır
 *
 * @since 1.0.0
 */
function add_auto_clean_instructor_meta_on_role_change() {
    // Sadece admin kullanıcıları için
    if (!current_user_can('administrator')) {
        return;
    }

    // Kullanıcı düzenleme sayfasında olup olmadığımızı kontrol et
    $screen = get_current_screen();
    if (!$screen || ($screen->base !== 'user-edit' && $screen->base !== 'profile')) {
        return;
    }

    // JavaScript ekle - bilgi mesajı göstermeden sadece AJAX işlemini hazırla
    ?>
    <script type="text/javascript">
    jQuery(document).ready(function($) {
        // Sayfa yüklendiğinde rol "subscriber" ise eğitmen meta verilerini kontrol et
        if ($('#role').val() === 'subscriber') {
            // Kullanıcının eğitmen meta verilerini kontrol et
            var userId = $('#user_id').val() || $('input[name="user_id"]').val();

            // AJAX ile kullanıcının eğitmen meta verilerini kontrol et
            if (userId) {
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'check_instructor_meta',
                        user_id: userId,
                        nonce: '<?php echo wp_create_nonce('check_instructor_meta_nonce'); ?>'
                    },
                    success: function(response) {
                        // Bilgi mesajı gösterme - sadece arka planda kontrol et
                        console.log('Eğitmen meta verileri kontrol edildi:', response);
                    }
                });
            }
        }
    });
    </script>
    <?php
}
add_action('admin_footer', 'add_auto_clean_instructor_meta_on_role_change');

/**
 * Kullanıcının eğitmen meta verilerini kontrol etmek için AJAX işleyicisi
 *
 * @since 1.0.0
 */
function check_instructor_meta_ajax_handler() {
    // Nonce kontrolü
    check_ajax_referer('check_instructor_meta_nonce', 'nonce');

    // Yetki kontrolü
    if (!current_user_can('administrator')) {
        wp_send_json_error('Bu işlemi gerçekleştirmek için yetkiniz yok.');
        return;
    }

    // Kullanıcı ID'sini al
    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;

    if (!$user_id) {
        wp_send_json_error('Geçersiz kullanıcı ID\'si.');
        return;
    }

    // Kullanıcının eğitmen meta verilerini kontrol et
    $is_instructor = get_user_meta($user_id, '_is_tutor_instructor', true);
    $instructor_status = get_user_meta($user_id, '_tutor_instructor_status', true);

    wp_send_json_success(array(
        'has_instructor_meta' => ($is_instructor || $instructor_status) ? true : false
    ));
}
add_action('wp_ajax_check_instructor_meta', 'check_instructor_meta_ajax_handler');

/**
 * Dashboard menüsünde eğitmen öğelerini gösterip göstermemeyi kontrol eden filtre
 * Bu fonksiyon, kullanıcının gerçek rolünü kontrol eder, meta verileri değil
 *
 * @since 1.0.0
 * @param array $nav_items Menü öğeleri
 * @return array Filtrelenmiş menü öğeleri
 */
function filter_dashboard_instructor_menu_items($nav_items) {
    // Kullanıcı giriş yapmış mı kontrol et
    if (!is_user_logged_in()) {
        return $nav_items;
    }

    // Mevcut kullanıcıyı al
    $user = wp_get_current_user();
    $user_roles = $user->roles;

    // Kullanıcı admin ise, tüm menü öğelerini göster
    if (in_array('administrator', $user_roles)) {
        return $nav_items;
    }

    // Kullanıcı eğitmen rolüne sahip mi kontrol et
    $is_instructor = false;
    if (function_exists('tutor') && in_array(tutor()->instructor_role, $user_roles)) {
        $is_instructor = true;
    } else if (in_array('tutor_instructor', $user_roles)) {
        $is_instructor = true;
    }

    // Eğer kullanıcı eğitmen değilse, eğitmen menü öğelerini kaldır
    if (!$is_instructor) {
        foreach ($nav_items as $key => $item) {
            // Eğitmen başlığını kaldır
            if ($key === 'separator-1') {
                unset($nav_items[$key]);
                continue;
            }

            // Eğitmen menü öğelerini kaldır
            if (is_array($item) && isset($item['auth_cap']) && $item['auth_cap'] === tutor()->instructor_role) {
                unset($nav_items[$key]);
            }
        }
    }

    return $nav_items;
}
add_filter('tutor_dashboard/nav_items_all', 'filter_dashboard_instructor_menu_items', 999);

/**
 * Dashboard içeriğinde eğitmen özelliklerini gösterip göstermemeyi kontrol eden filtre
 * Bu fonksiyon, kullanıcının gerçek rolünü kontrol eder, meta verileri değil
 *
 * @since 1.0.0
 * @return void
 */
function filter_dashboard_instructor_content() {
    // Kullanıcı giriş yapmış mı kontrol et
    if (!is_user_logged_in()) {
        return;
    }

    // Mevcut kullanıcıyı al
    $user = wp_get_current_user();
    $user_roles = $user->roles;

    // Kullanıcı admin ise, tüm içeriği göster
    if (in_array('administrator', $user_roles)) {
        return;
    }

    // Kullanıcı eğitmen rolüne sahip mi kontrol et
    $is_instructor = false;
    if (function_exists('tutor') && in_array(tutor()->instructor_role, $user_roles)) {
        $is_instructor = true;
    } else if (in_array('tutor_instructor', $user_roles)) {
        $is_instructor = true;
    }

    // Eğer kullanıcı eğitmen değilse, eğitmen içeriklerini gizle
    if (!$is_instructor) {
        // CSS ile eğitmen içeriklerini gizle
        ?>
        <style>
            /* Eğitmen başlığını gizle */
            .tutor-dashboard-permalinks li:has(span.tutor-dashboard-menu-item-text:contains("Eğitmen")) {
                display: none !important;
            }

            /* Eğitmen menü öğelerini gizle */
            .tutor-dashboard-permalinks li:has(a[href*="my-courses"]),
            .tutor-dashboard-permalinks li:has(a[href*="announcements"]),
            .tutor-dashboard-permalinks li:has(a[href*="withdraw"]),
            .tutor-dashboard-permalinks li:has(a[href*="quiz-attempts"]) {
                display: none !important;
            }

            /* Eğitmen içeriklerini gizle */
            .tutor-dashboard-content-inner .tutor-fs-5:contains("My Courses"),
            .tutor-dashboard-content-inner .popular-courses-heading-dashboard,
            .tutor-dashboard-content-inner .tutor-col-lg-6:has(.tutor-icon-user-graduate),
            .tutor-dashboard-content-inner .tutor-col-lg-6:has(.tutor-icon-dollar-sign) {
                display: none !important;
            }

            /* Kurs oluştur butonunu gizle */
            .tutor-btn.tutor-btn-outline-primary.tutor-create-new-course,
            .tutor-btn-outline-primary:contains("Kurs Oluştur") {
                display: none !important;
            }
        </style>
        <?php
    }
}
add_action('tutor_dashboard/before_dashboard_content', 'filter_dashboard_instructor_content');

/**
 * Tutor LMS'in is_instructor fonksiyonunu geçersiz kılarak, kullanıcının gerçek rolünü kontrol et
 * Bu fonksiyon, kullanıcının meta verilerini değil, gerçek rolünü kontrol eder
 *
 * @since 1.0.0
 * @param bool $result Orijinal sonuç
 * @param int $user_id Kullanıcı ID'si
 * @param bool $is_approved Onaylı eğitmen mi
 * @return bool Filtrelenmiş sonuç
 */
function filter_is_instructor($result, $user_id, $is_approved) {
    // Kullanıcı ID'si geçerli mi kontrol et
    if (!$user_id) {
        $user_id = get_current_user_id();
    }

    if (!$user_id) {
        return false;
    }

    // Kullanıcı nesnesini al
    $user = get_userdata($user_id);
    if (!$user) {
        return false;
    }

    // Kullanıcı rollerini al
    $user_roles = $user->roles;

    // Kullanıcı admin ise, true döndür
    if (in_array('administrator', $user_roles)) {
        return true;
    }

    // Kullanıcı eğitmen rolüne sahip mi kontrol et
    if (function_exists('tutor') && in_array(tutor()->instructor_role, $user_roles)) {
        return true;
    } else if (in_array('tutor_instructor', $user_roles)) {
        return true;
    }

    // Kullanıcı eğitmen değilse, false döndür
    return false;
}
add_filter('tutor_is_instructor', 'filter_is_instructor', 999, 3);

/**
 * Ayarlar sayfasındaki eğitmen ayarlarını gizle
 *
 * @since 1.0.0
 * @param array $setting_menus Ayarlar menüsü
 * @return array Filtrelenmiş ayarlar menüsü
 */
function filter_settings_instructor_menu_items($setting_menus) {
    // Kullanıcı giriş yapmış mı kontrol et
    if (!is_user_logged_in()) {
        return $setting_menus;
    }

    // Mevcut kullanıcıyı al
    $user = wp_get_current_user();
    $user_roles = $user->roles;

    // Kullanıcı admin ise, tüm menü öğelerini göster
    if (in_array('administrator', $user_roles)) {
        return $setting_menus;
    }

    // Kullanıcı eğitmen rolüne sahip mi kontrol et
    $is_instructor = false;
    if (function_exists('tutor') && in_array(tutor()->instructor_role, $user_roles)) {
        $is_instructor = true;
    } else if (in_array('tutor_instructor', $user_roles)) {
        $is_instructor = true;
    }

    // Eğer kullanıcı eğitmen değilse, eğitmen ayarlarını kaldır
    if (!$is_instructor) {
        foreach ($setting_menus as $key => $menu) {
            if (isset($menu['role']) && $menu['role'] === 'instructor') {
                unset($setting_menus[$key]);
            }
        }
    }

    return $setting_menus;
}
add_filter('tutor_dashboard/nav_items/settings/nav_items', 'filter_settings_instructor_menu_items', 999);

/**
 * Profil sayfasındaki araç çubuğu seçeneğini gizle (admin kullanıcıları hariç)
 *
 * @since 1.0.0
 */
function hide_admin_bar_option_for_users() {
    // Kullanıcı giriş yapmış mı kontrol et
    if (!is_user_logged_in()) {
        return;
    }

    // Sadece admin panelinde ve profil sayfasında çalış
    $screen = get_current_screen();
    if (!$screen || $screen->base !== 'profile') {
        return;
    }

    // Mevcut kullanıcıyı al
    $current_user = wp_get_current_user();

    // Admin kullanıcıları etkileme
    if (in_array('administrator', $current_user->roles)) {
        return;
    }

    // Admin olmayan tüm kullanıcılar için araç çubuğu seçeneğini gizle
    echo '<style>
        /* Profil sayfasındaki araç çubuğu seçeneğini gizle - tüm olası seçiciler */
        .user-admin-bar-front-wrap,
        tr.user-admin-bar-front-wrap,
        label[for="admin_bar_front"],
        #admin_bar_front,
        .show-admin-bar,
        /* WordPress 5.x ve 6.x için */
        #your-profile .form-table tr:nth-child(4),
        /* Genel seçici - tüm form tablolarında */
        .form-table tr:has(input#admin_bar_front),
        /* Alternatif seçici */
        .form-table tr:has(label[for="admin_bar_front"]),
        /* Kırmızı ile işaretlenen alan */
        tr:has(.show-admin-bar) {
            display: none !important;
        }
    </style>';

    // JavaScript ile kırmızı ile işaretlenen alanı gizle (daha geniş tarayıcı desteği için)
    echo '<script>
        document.addEventListener("DOMContentLoaded", function() {
            // Yöntem 1: ID ile doğrudan hedefleme
            var adminBarOption = document.getElementById("admin_bar_front");
            if (adminBarOption) {
                var parentRow = adminBarOption.closest("tr");
                if (parentRow) {
                    parentRow.style.display = "none";
                }
            }

            // Yöntem 2: Metin içeriği ile hedefleme
            var allTableRows = document.querySelectorAll("table.form-table tr");
            allTableRows.forEach(function(row) {
                // Türkçe ve İngilizce metinleri kontrol et
                if (row.textContent.includes("Araç çubuğu") ||
                    row.textContent.includes("Toolbar") ||
                    row.textContent.includes("Admin Bar")) {
                    row.style.display = "none";
                }
            });

            // Yöntem 3: Label ile hedefleme
            var adminBarLabel = document.querySelector("label[for=\'admin_bar_front\']");
            if (adminBarLabel) {
                var parentRow = adminBarLabel.closest("tr");
                if (parentRow) {
                    parentRow.style.display = "none";
                }
            }

            // Yöntem 4: Class ile hedefleme
            var showAdminBarElements = document.querySelectorAll(".show-admin-bar");
            showAdminBarElements.forEach(function(element) {
                var parentRow = element.closest("tr");
                if (parentRow) {
                    parentRow.style.display = "none";
                }
            });
        });
    </script>';
}
add_action('admin_head', 'hide_admin_bar_option_for_users');