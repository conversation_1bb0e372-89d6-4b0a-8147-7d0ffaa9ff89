/**
 * Tutor Video Oynatıcı Toggle (Aç/Kapat)
 * Bu script, hamburger menü butonuna tıklandığında video oynatıcının stilini değiştirir
 *
 * @package Tutor
 * @subpackage VideoPlayer
 * <AUTHOR> <<EMAIL>>
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Değişkenler
    let isExpanded = false;

    /**
     * Video oynatıcının stilini değiştir
     */
    function toggleVideoPlayerStyle() {
        // Video oynatıcı elementlerini seç
        const videoPlayer = $('.tutor-video-player');
        const videoWrapper = $('.plyr__video-wrapper');
        const videoPlayerWrapper = $('.tutor-video-player-wrapper-modern');

        if (!isExpanded) {
            // Genişletilmiş görünüme geç
            videoPlayer.css({
                // 'border-radius': '0px',
                // 'width': '100%',
                // 'margin-left': 'auto',
                // 'margin-right': 'auto'
            });

            videoWrapper.css({
                // 'width': '80%',
                // 'margin-left': 'auto',
                // 'margin-right': 'auto',
                // 'border-radius': '0px'
                // 'aspect-ratio': '16 / 9'
            });

            videoPlayerWrapper.css({
                // 'padding': '0'
            });

            // Genişletilmiş durumu kaydet
            localStorage.setItem('tutorVideoExpanded', 'true');
            isExpanded = true;
        } else {
            // Normal görünüme geri dön
            videoPlayer.css({
                'border-radius': '12px',
                'width': '90%',
                'margin-left': 'auto',
                'margin-right': 'auto'
            });

            videoWrapper.css({
                'width': '100%',
                'margin-left': 'auto',
                'margin-right': 'auto',
                'border-radius': '10px'
            });

            videoPlayerWrapper.css({
                'padding': '20px 0'
            });

            // Normal durumu kaydet
            localStorage.setItem('tutorVideoExpanded', 'false');
            isExpanded = false;
        }
    }

    /**
     * Video toggle işlevselliğini başlat
     */
    function initVideoToggle() {
        // Hamburger menü butonlarını seç
        const hamburgerButtons = $('[tutor-course-topics-sidebar-toggler], [tutor-course-topics-sidebar-offcanvas-toggler]');

        // Butonlara tıklama olayı ekle
        hamburgerButtons.on('click', function(e) {
            // Sadece masaüstü görünümünde video stilini değiştir
            if (window.matchMedia('(min-width: 1200px)').matches) {
                e.preventDefault();
                toggleVideoPlayerStyle();
            }
            // Mobil/tablet için hiçbir şey yapma, varsayılan davranışı koru
        });
    }

    // DOM yüklendiğinde çalışacak
    $(document).ready(function() {
        // Önceki durumu kontrol et
        const savedState = localStorage.getItem('tutorVideoExpanded');
        if (savedState === 'true') {
            isExpanded = true;
            // Sayfa yüklendiğinde görünümü uygula
            setTimeout(function() {
                toggleVideoPlayerStyle();
            }, 300);
        }

        initVideoToggle();
    });

})(jQuery);
