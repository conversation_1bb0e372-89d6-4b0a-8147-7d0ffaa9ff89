"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[583],{1343:(e,t,r)=>{r.d(t,{Z:()=>E});var n=r(81);var a=r(9795);var o=r(7363);var i=r(917);var c=function e(t){var r=t.component;return(0,i.tZ)(a.Z,{componentName:"content"},r)};const l=c;var u=r(4063);var f=r(5519);var s=r(7941);var p=r(5453);var d=r(4857);var v=r(9768);var b=r(1961);var h=r(8777);var m=r(830);var g=r(8898);var y=r(9447);var Z=r(4436);var w=r(9528);var x=r(7536);function O(){O=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){e[n]=r[n]}}}return e};return O.apply(this,arguments)}var j=function e(t){var r=t.name,n=t.label,o=t.buttonText,c=t.helpText,l=t.infoText,j=t.placeholder,T=t.type,P=t.options,S=t.defaultValue,A=t.rules,k=t.form;var C=function e(t){var x=function(){switch(T){case"text":return(0,i.tZ)(v.Z,O({},t,{label:n,placeholder:j,helpText:c}));case"number":return(0,i.tZ)(v.Z,O({},t,{type:"number",label:n,placeholder:j,helpText:c}));case"password":return(0,i.tZ)(v.Z,O({},t,{type:"password",label:n,placeholder:j,helpText:c}));case"textarea":return(0,i.tZ)(g.Z,O({},t,{label:n,placeholder:j,helpText:c}));case"select":return(0,i.tZ)(h.Z,O({},t,{label:n,options:P||[],placeholder:j,helpText:c}));case"radio":return(0,i.tZ)(b.Z,O({},t,{label:n,options:P||[]}));case"checkbox":return(0,i.tZ)(f.Z,O({},t,{label:n}));case"switch":return(0,i.tZ)(m.Z,O({},t,{label:n,helpText:c}));case"date":return(0,i.tZ)(s.Z,O({},t,{label:n,placeholder:j,helpText:c}));case"time":return(0,i.tZ)(y.Z,O({},t,{label:n,placeholder:j,helpText:c}));case"image":return(0,i.tZ)(d.Z,O({},t,{label:n,buttonText:o,helpText:c,infoText:l}));case"video":return(0,i.tZ)(Z.Z,O({},t,{label:n,buttonText:o,helpText:c,infoText:l}));case"uploader":return(0,i.tZ)(p.Z,O({},t,{label:n,buttonText:o,helpText:c}));case"WPEditor":return(0,i.tZ)(w.Z,O({},t,{label:n,placeholder:j,helpText:c}));default:return(0,i.tZ)(u.Z,{type:"danger"},"Unsupported field type: ",T)}}();return(0,i.tZ)(a.Z,{componentName:"field ".concat(r),onError:function e(t,n){console.warn("Field ".concat(r," failed to render:"),{error:t,errorInfo:n})}},x)};return(0,i.tZ)(x.Qr,{name:r,control:k.control,defaultValue:S!==null&&S!==void 0?S:"",rules:A,render:function e(t){return C(t)}})};const T=j;var P=r(7363);function S(){S=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){e[n]=r[n]}}}return e};return S.apply(this,arguments)}function A(e,t){var r=typeof Symbol!=="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=k(e))||t&&e&&typeof e.length==="number"){if(r)e=r;var n=0;var a=function e(){};return{s:a,n:function t(){if(n>=e.length)return{done:true};return{done:false,value:e[n++]}},e:function e(t){throw t},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o=true,i=false,c;return{s:function t(){r=r.call(e)},n:function e(){var t=r.next();o=t.done;return t},e:function e(t){i=true;c=t},f:function e(){try{if(!o&&r["return"]!=null)r["return"]()}finally{if(i)throw c}}}}function k(e,t){if(!e)return;if(typeof e==="string")return C(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor)r=e.constructor.name;if(r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return C(e,t)}function C(e,t){if(t==null||t>e.length)t=e.length;for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var W=function e(t){var r=t.section,a=t.namePrefix,o=t.form;var c=(0,n.l)(),u=c.fields,f=c.contents;var s=function e(){var t=r.split(".");var n=u;var a=A(t),o;try{for(a.s();!(o=a.n()).done;){var i=o.value;if(!n[i])return[];n=n[i]}}catch(e){a.e(e)}finally{a.f()}return Array.isArray(n)?n:[]};var p=function e(){var t=r.split(".");var n=f;var a=A(t),o;try{for(a.s();!(o=a.n()).done;){var i=o.value;if(!n[i])return[];n=n[i]}}catch(e){a.e(e)}finally{a.f()}return Array.isArray(n)?n:[]};return(0,i.tZ)(P.Fragment,null,s().map((function(e){return(0,i.tZ)(T,S({key:e.name,form:o},e,{name:a?"".concat(a).concat(e.name):e.name}))})),p().map((function(e,t){var r=e.component;return(0,i.tZ)(l,{key:t,component:r})})))};const E=W},6051:(e,t,r)=>{r.d(t,{Z:()=>O});var n=r(3098);var a=r(7886);var o=r(74);var i=r(6595);var c=r(6413);var l=r(1537);var u=r(4900);var f=r(2704);var s=r(917);var p=r(8003);var d=r.n(p);var v=r(7536);var b=r(9250);function h(e){"@babel/helpers - typeof";return h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},h(e)}function m(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function g(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?m(Object(r),!0).forEach((function(t){y(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function y(e,t,r){t=Z(t);if(t in e){Object.defineProperty(e,t,{value:r,enumerable:true,configurable:true,writable:true})}else{e[t]=r}return e}function Z(e){var t=w(e,"string");return h(t)==="symbol"?t:String(t)}function w(e,t){if(h(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==undefined){var n=r.call(e,t||"default");if(h(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var x=function e(t){var r=t.styleModifier;var d=(0,a.O)(),h=d.steps,m=d.setSteps;var y=(0,b.s0)();var Z=(0,f.J)(n.Z);var w=(0,v.Gc)();var x=h.findIndex((function(e){return e.path===Z}));var O=Math.max(-1,x-1);var T=Math.min(h.length,x+1);var P=h[O];var S=h[T];var A=w.watch("post_title");var k=function e(){m((function(e){return e.map((function(e,t){if(t===x){return g(g({},e),{},{isActive:false})}if(t===O){return g(g({},e),{},{isActive:true})}return e}))}));y(P.path)};var C=function e(){m((function(e){return e.map((function(e,t){if(t===x){return g(g({},e),{},{isActive:false})}if(t===T){return g(g({},e),{},{isActive:true})}return e}))}));y(S.path)};return(0,s.tZ)("div",{css:[j.wrapper,r,true?"":0,true?"":0]},(0,s.tZ)(u.Z,{when:x>0},(0,s.tZ)(o.Z,{variant:"tertiary",iconPosition:"right",size:"small",onClick:k,buttonCss:(0,s.iv)("padding:",l.W0[4],";svg{color:",l.Jv.icon["default"],";}"+(true?"":0),true?"":0),disabled:O<0},(0,s.tZ)(i.Z,{name:!c.dZ?"chevronLeft":"chevronRight",height:24,width:24}))),(0,s.tZ)(u.Z,{when:x<h.length-1&&A},(0,s.tZ)(o.Z,{variant:"tertiary",icon:(0,s.tZ)(i.Z,{name:!c.dZ?"chevronRight":"chevronLeft",height:24,width:24}),iconPosition:"right",size:"small",onClick:C,buttonCss:(0,s.iv)("padding:",l.W0[4]," ",l.W0[4]," ",l.W0[4]," ",l.W0[12],";svg{color:",l.Jv.icon["default"],";}"+(true?"":0),true?"":0),disabled:!A||T>=h.length},(0,p.__)("Next","tutor"))))};const O=x;var j={wrapper:(0,s.iv)("width:100%;display:flex;justify-content:end;height:32px;align-items:center;gap:",l.W0[16],";"+(true?"":0),true?"":0)}},7692:(e,t,r)=>{r.d(t,{Z:()=>n});const n=r.p+"images/56f20c93d8e28423f724fe4e914fbd21-3d.png"},2663:(e,t,r)=>{r.d(t,{Z:()=>n});const n=r.p+"images/7a53b07b7f13e48b7b7b47dff35d9946-black-and-white.png"},8505:(e,t,r)=>{r.d(t,{Z:()=>n});const n=r.p+"images/9613f2a35fc147cbde38998fc279f6e9-concept.png"},9554:(e,t,r)=>{r.d(t,{Z:()=>n});const n=r.p+"images/ff5a8a3d6c18c02f00d659da3824176b-dreamy.png"},628:(e,t,r)=>{r.d(t,{Z:()=>n});const n=r.p+"images/bff40839481a6e109932774fea006137-filmic.png"},7210:(e,t,r)=>{r.d(t,{Z:()=>n});const n=r.p+"images/dec5e33b385ba1a7c841dde2b6c1a5af-illustration.png"},4246:(e,t,r)=>{r.d(t,{Z:()=>n});const n=r.p+"images/83571e85f649c56b82349466a5b4c844-neon.png"},121:(e,t,r)=>{r.d(t,{Z:()=>n});const n=r.p+"images/9dcf3f4907036dd08b31bf2a7181bed0-none.jpg"},7758:(e,t,r)=>{r.d(t,{Z:()=>n});const n=r.p+"images/fc8edfd709e8f6ed349b59a0f0a00647-painting.png"},4121:(e,t,r)=>{r.d(t,{Z:()=>n});const n=r.p+"images/32925d4873712d856f4abc340b3334cb-photo.png"},4446:(e,t,r)=>{r.d(t,{Z:()=>n});const n=r.p+"images/fb8df26f9102747dfafc31d912d6d074-retro.png"},9463:(e,t,r)=>{r.d(t,{Z:()=>n});const n=r.p+"images/7c935ca7690aecae8c42142d8cec660e-sketch.png"},9502:(e,t,r)=>{r.d(t,{Z:()=>n});const n=r.p+"images/e67e28356e87045281d41cd6583f5c41-generate-image-2x.webp"},8037:(e,t,r)=>{r.d(t,{Z:()=>n});const n=r.p+"images/9c13bda85170ee68f15380378d920fd1-generate-image.webp"}}]);