/**
 * <PERSON><PERSON><PERSON><PERSON>i
 * Bu CSS dosyası, "Kurs Oluştur" butonunu doğrudan hedefleyen CSS kuralları içerir.
 * !important kullanarak diğer stilleri geçersiz kılar.
 */

/* <PERSON><PERSON><PERSON><PERSON> buton se<PERSON> */
.tutor-btn.tutor-btn-outline-primary.tutor-create-new-course {
    border: 1px solid var(--tutor-color-primary) !important;
    color: var(--tutor-color-primary) !important;
    background-color: transparent !important;
    background-image: none !important;
    box-shadow: none !important;
    transform: none !important;
    padding: 8px 16px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    border-radius: 6px !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    text-align: center !important;
    display: inline-flex !important;
    align-items: center !important;
}

/* Hover durumu */
.tutor-btn.tutor-btn-outline-primary.tutor-create-new-course:hover {
    background-color: color-mix(in srgb, var(--tutor-color-primary) 10%, transparent) !important;
    color: var(--tutor-color-primary) !important;
    transform: none !important;
    box-shadow: none !important;
}

/* İkon */
.tutor-btn.tutor-btn-outline-primary.tutor-create-new-course i {
    color: var(--tutor-color-primary) !important;
    margin-right: 8px !important;
}

/* Inline stil geçersiz kılma */
.tutor-btn.tutor-btn-outline-primary.tutor-create-new-course[style] {
    border: 1px solid var(--tutor-color-primary) !important;
    color: var(--tutor-color-primary) !important;
    background-color: transparent !important;
}

/* Inline stil geçersiz kılma - hover */
.tutor-btn.tutor-btn-outline-primary.tutor-create-new-course[style]:hover {
    background-color: color-mix(in srgb, var(--tutor-color-primary) 10%, transparent) !important;
    color: var(--tutor-color-primary) !important;
}
