/**
 * Tutor LMS Arama Fonksiyonları
 * Arama sırasında kurs açıklama alanını gizlemek için kullanılır
 */

document.addEventListener('DOMContentLoaded', function() {
    // Arama kutusunu seç
    const searchInput = document.getElementById('tutor-course-content-search');

    // Sidebar wrapper'ı seç
    const sidebarWrapper = document.querySelector('.tutor-course-single-sidebar-wrapper');

    if (searchInput && sidebarWrapper) {
        // Arama kutusuna yazı yazıldığında
        searchInput.addEventListener('input', function() {
            if (this.value.trim() !== '') {
                // Arama yapılıyorsa tutor-searching sınıfını ekle
                sidebarWrapper.classList.add('tutor-searching');
            } else {
                // Arama kutusu boşsa tutor-searching sınıfını kaldır
                sidebarWrapper.classList.remove('tutor-searching');
            }
        });

        // Arama kutusu temizlendiğinde
        searchInput.addEventListener('search', function() {
            if (this.value.trim() === '') {
                // Arama kutusu boşsa tutor-searching sınıfını kaldır
                sidebarWrapper.classList.remove('tutor-searching');
            }
        });

        // Çarpı ikonuna veya arama ikonuna tıklama olayını dinle (delegasyon kullanarak)
        document.addEventListener('click', function(event) {
            // Tıklanan eleman çarpı ikonu mu kontrol et
            if ((event.target.classList.contains('tutor-icon-times') &&
                (event.target.classList.contains('tutor-search-icon') ||
                 event.target.closest('.tutor-form-control-wrapper'))) ||
                event.target.id === 'tutor-course-search-icon') {

                // Arama kutusunu temizle
                searchInput.value = '';

                // Arama olayını tetikle
                const searchEvent = new Event('search');
                searchInput.dispatchEvent(searchEvent);

                // tutor-searching sınıfını kaldır
                sidebarWrapper.classList.remove('tutor-searching');
            }
        });

        // Sayfa yüklendiğinde arama kutusu boş değilse
        if (searchInput.value.trim() !== '') {
            sidebarWrapper.classList.add('tutor-searching');
        }
    }
});
