/**
 * Tutor LMS Yorum Cevaplama Sistemi
 * Bu script, yorum cevaplama butonlarına tıklandığında cevapları ve cevap formunu gösterir/gizler
 */

(function($) {
    'use strict';
    
    $(document).ready(function() {
        initCommentReplySystem();
    });
    
    /**
     * Yorum cevaplama sistemini başlat
     */
    function initCommentReplySystem() {
        // Cevapla butonlarına tıklama olayı ekle
        $(document).on('click', '.tutor-comment-actions span', function(e) {
            e.preventDefault();
            
            // En yakın üst yorum container'ını bul
            const commentContainer = $(this).closest('.tutor-comments-list');
            
            // Tüm diğer açık cevapları kapat
            $('.tutor-comments-list.show-replies').not(commentContainer).removeClass('show-replies');
            
            // Bu yorumun cevaplarını aç/kapat (toggle)
            commentContainer.toggleClass('show-replies');
            
            // Buton metnini <PERSON> - her zaman "Cevapla (X)" olarak kalsın
            // Burada hiçbir şey yapmıyoruz, böylece buton metni değişmeyecek
        });
        
        // Yorum gönderildikten sonra formu gizle ve cevapları göster
        $(document).on('submit', 'form[tutor-comment-reply]', function() {
            // Form gönderildiğinde otomatik olarak cevaplar görünecek
            // Bu kısım AJAX ile yorum eklendiğinde çalışacak (Tutor LMS'in kendi işleyişi)
            setTimeout(function() {
                // Sayfayı yenilemeden önce, kullanıcıya geri bildirim vermek için
                // Bu kısım isteğe bağlı, çünkü form submit sonrası sayfa yenilenecek
            }, 100);
            
            // Form submit işlemini engelleme (varsayılan Tutor LMS davranışı devam etsin)
            return true;
        });
    }
    
})(jQuery);
