<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit631a460b6010ba6d03c73cfd415f87ca
{
    public static $prefixLengthsPsr4 = array (
        'T' => 
        array (
            '<PERSON><PERSON>\\Traits\\' => 13,
            '<PERSON><PERSON>\\PaymentGateways\\' => 22,
            '<PERSON><PERSON>\\Models\\' => 13,
            '<PERSON><PERSON>\\Helpers\\' => 14,
            '<PERSON><PERSON>\\Ecommerce\\' => 16,
            '<PERSON><PERSON>\\Cache\\' => 12,
        ),
    );

    public static $prefixDirsPsr4 = array (
        '<PERSON><PERSON>\\Traits\\' => 
        array (
            0 => __DIR__ . '/../..' . '/traits',
        ),
        '<PERSON><PERSON>\\PaymentGateways\\' => 
        array (
            0 => __DIR__ . '/../..' . '/ecommerce/PaymentGateways',
        ),
        'Tutor\\Models\\' => 
        array (
            0 => __DIR__ . '/../..' . '/models',
        ),
        '<PERSON><PERSON>\\Helpers\\' => 
        array (
            0 => __DIR__ . '/../..' . '/helpers',
        ),
        '<PERSON><PERSON>\\Ecommerce\\' => 
        array (
            0 => __DIR__ . '/../..' . '/ecommerce',
        ),
        'Tutor\\Cache\\' => 
        array (
            0 => __DIR__ . '/../..' . '/cache',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit631a460b6010ba6d03c73cfd415f87ca::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit631a460b6010ba6d03c73cfd415f87ca::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit631a460b6010ba6d03c73cfd415f87ca::$classMap;

        }, null, ClassLoader::class);
    }
}
