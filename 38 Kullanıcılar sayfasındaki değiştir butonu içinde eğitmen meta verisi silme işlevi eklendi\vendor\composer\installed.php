<?php return array(
    'root' => array(
        'name' => 'themeum/tutor',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => 'eedafe0f811620b4c9032162fdfecca7f342fd23',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => false,
    ),
    'versions' => array(
        'themeum/tutor' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => 'eedafe0f811620b4c9032162fdfecca7f342fd23',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
