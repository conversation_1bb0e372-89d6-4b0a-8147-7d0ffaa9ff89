/**
 * Custom translations for <PERSON><PERSON> L<PERSON>
 * This script changes specific text elements in the UI
 */

document.addEventListener('DOMContentLoaded', function() {
    // Change "Mark as Complete" text to "Dersi tamamla"
    const markCompleteButtons = document.querySelectorAll('.tutor-topbar-mark-btn span:not([class^="tutor-icon-"])');

    markCompleteButtons.forEach(function(button) {
        if (button.textContent.trim() === 'Mark as Complete') {
            button.textContent = 'Dersi tamamla';
        }
    });

    // Change course viewing tab texts to Turkish
    const tabTexts = {
        'Overview': '<PERSON><PERSON> Bakış',
        'Exercise Files': 'Ders Dosyaları',
        'Comments': '<PERSON>rumlar'
    };

    // Select all tab navigation links
    const tabLinks = document.querySelectorAll('.tutor-course-spotlight-nav .tutor-nav-link span:not([class^="tutor-icon-"])');

    // Replace the text content of each tab
    tabLinks.forEach(function(span) {
        const originalText = span.textContent.trim();
        if (tabTexts[originalText]) {
            span.textContent = tabTexts[originalText];
        }
    });

    // Also change the headings inside the tabs
    const exerciseFilesHeading = document.querySelector('#tutor-course-spotlight-files .tutor-fs-5.tutor-fw-medium.tutor-color-black');
    if (exerciseFilesHeading && exerciseFilesHeading.textContent.trim() === 'Exercise Files') {
        exerciseFilesHeading.textContent = 'Ders Dosyaları';
    }

    const aboutLessonHeading = document.querySelector('#tutor-course-spotlight-overview .tutor-fs-5.tutor-fw-medium.tutor-color-black');
    if (aboutLessonHeading && aboutLessonHeading.textContent.trim() === 'About Lesson') {
        aboutLessonHeading.textContent = 'Ders Hakkında';
    }
});
