/**
 * Tutor LMS Course Content Search
 * Bu script, kurs içeriğinde arama yapma işlevselliği sağlar
 */

(function($) {
    'use strict';
    
    let isSearchActive = false;
    let originalStates = {};
    
    // DOM yüklendikten sonra çalıştır
    $(document).ready(function() {
        initCourseContentSearch();
    });
    
    /**
     * Kurs içeriği arama işlevselliğini başlat
     */
    function initCourseContentSearch() {
        const searchInput = $('#tutor-course-content-search');
        const searchIcon = $('#tutor-course-search-icon');
        
        if (!searchInput.length) {
            return;
        }
        
        // İlk yüklemede topiclerin durumunu kaydet
        saveOriginalStates();
        
        // Accordion başlıklarına tıklandığında durumları güncelle
        $('.tutor-accordion-item-header').on('click', function() {
            // Arama aktif değilse durumları güncelle
            if (!isSearchActive) {
                // Kısa bir gecikme ile durumları yeniden kaydet (animasyon tamamlandıktan sonra)
                setTimeout(saveOriginalStates, 300);
            }
        });
        
        // Arama ikonu tıklama olayı 
        searchIcon.on('click', function() {
            // Eğer arama kutusunda yazı varsa ve ikon çarpı ise temizle
            if (searchInput.val().length > 0) {
                searchInput.val('');
                searchInput.trigger('keyup'); // Arama temizlendiğinde keyup event'i tetikle
                toggleSearchIcon(false);
            }
        });
        
        // Arama yapma olayını dinle
        searchInput.on('keyup', function() {
            const searchTerm = $(this).val().toLowerCase();
            
            // İkonu güncelle
            toggleSearchIcon(searchTerm.length > 0);
            
            // Arama başladıysa ve aktif değilse
            if (searchTerm.length > 0 && !isSearchActive) {
                saveOriginalStates();
                isSearchActive = true;
            }
            
            // Arama temizlendiyse
            if (searchTerm.length === 0) {
                isSearchActive = false;
                restoreOriginalStates();
            } else {
                // Aramaya devam ediliyorsa
                showOnlyMatchingContent(searchTerm);
            }
        });
    }
    
    /**
     * Arama ikonunu değiştir
     * @param {boolean} isActive - Arama aktif mi?
     */
    function toggleSearchIcon(isActive) {
        const searchIcon = $('#tutor-course-search-icon');
        
        if (isActive) {
            searchIcon.removeClass('tutor-icon-search').addClass('tutor-icon-times');
        } else {
            searchIcon.removeClass('tutor-icon-times').addClass('tutor-icon-search');
        }
    }
    
    /**
     * Topiclerin orijinal durumlarını sakla
     */
    function saveOriginalStates() {
        originalStates = {};
        
        $('.tutor-course-topic').each(function() {
            const $topic = $(this);
            const topicId = $topic.attr('id') || $topic.index();
            const $header = $topic.find('.tutor-accordion-item-header');
            const $body = $topic.find('.tutor-accordion-item-body');
            
            originalStates[topicId] = {
                isOpen: $header.hasClass('is-active'),
                bodyDisplay: $body.css('display'),
                hasDisplayNone: $body.hasClass('tutor-display-none')
            };
        });
        
        console.log('Orijinal durumlar kaydedildi:', originalStates);
    }
    
    /**
     * Topicleri orijinal durumlarına geri getir
     */
    function restoreOriginalStates() {
        console.log('Orijinal durumlar geri yükleniyor');
        
        // Önce tüm topic içeriklerini görünür yap
        $('.tutor-course-topic-item').show();
        
        // Şimdi topicleri orijinal durumlarına getir
        $('.tutor-course-topic').each(function() {
            const $topic = $(this);
            const topicId = $topic.attr('id') || $topic.index();
            const state = originalStates[topicId];
            
            if (!state) return;
            
            // Önce topic'i görünür yap
            $topic.show();
            
            const $header = $topic.find('.tutor-accordion-item-header');
            const $body = $topic.find('.tutor-accordion-item-body');
            
            // Orijinal açık/kapalı durumuna getir
            if (state.isOpen) {
                $header.addClass('is-active');
                $body.removeClass('tutor-display-none');
                $body.css('display', state.bodyDisplay || 'block');
            } else {
                $header.removeClass('is-active');
                if (state.hasDisplayNone) {
                    $body.addClass('tutor-display-none');
                }
                $body.css('display', state.bodyDisplay || 'none');
            }
        });
    }
    
    /**
     * Sadece aranan içeriğe uyan topic ve içerikleri göster
     */
    function showOnlyMatchingContent(searchTerm) {
        // Tüm topic ve içerik öğelerini hazırla
        const $contentItems = $('.tutor-course-topic-item');
        const $topics = $('.tutor-course-topic');
        
        // Önce tüm içerik öğelerini gizle
        $contentItems.hide();
        
        // Eşleşen topic listesi
        const matchedTopics = new Set();
        
        // Arama terimini içeren içerik öğelerini göster
        $contentItems.each(function() {
            const itemTitle = $(this).find('.tutor-course-topic-item-title').text().toLowerCase();
            
            if (itemTitle.includes(searchTerm)) {
                $(this).show();
                
                // Bu içeriğin parent topic'ini bul ve kaydet
                const $topicContainer = $(this).closest('.tutor-course-topic');
                matchedTopics.add($topicContainer[0]);
            }
        });
        
        // Tüm topicleri kontrol et
        $topics.each(function() {
            const $topic = $(this);
            const $header = $topic.find('.tutor-accordion-item-header');
            const $body = $topic.find('.tutor-accordion-item-body');
            
            if (matchedTopics.has(this)) {
                // Eşleşen içeriğe sahip topic - aç
                $topic.show();
                $header.addClass('is-active');
                $body.removeClass('tutor-display-none');
                $body.css('display', 'block');
            } else {
                // Eşleşmeyen topic - gizle
                $topic.hide();
                $header.removeClass('is-active');
                $body.addClass('tutor-display-none');
                $body.css('display', 'none');
            }
        });
    }
    
})(jQuery); 