/**
 * Avatar Stilleri
 * <PERSON><PERSON>, dashboard'daki avatar gö<PERSON><PERSON><PERSON>ümünü düzenlemek için kullanılı<PERSON>.
 */

/* Avatar boyutunu ayarla ve ortala */
.tutor-dashboard-header-avatar .tutor-avatar.tutor-avatar-xl {
    width: 40px !important;
    height: 40px !important;
}

/* Avatar içindeki resmi ortala */
.tutor-dashboard-header-avatar .tutor-avatar img {
    margin: 0 auto !important;
    display: block !important;
}

/* Header ve Avatar stilleri */
.tutor-frontend-dashboard-header {
    background-color: #f9f9f9 !important;
    padding: 15px 10px 15px 0px !important;
    border-bottom: 1px solid #e9ecef !important;
    display: flex !important;
}

/* <PERSON>bil görünüm için stiller */
@media (max-width: 991px) {
    .tutor-frontend-dashboard-header {
        width: 100% !important;
    }
}
