"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[297],{2297:(t,e,r)=>{r.r(e);r.d(e,{default:()=>ze});var n=r(917);var i=r(202);var o=r(2141);var a=r(8003);var u=r(7363);var c=r(7536);var s=r(9250);var l=r(6907);var d=r(2798);var f=r(96);var p=r(1624);var v=r(5453);var h=r(1162);var m=r(8898);var y=r(6595);var g=r(5043);var b=r(6895);var Z=r(74);var w=r(9592);var _=r(1537);var x=r(5460);var k=r(6413);var S=r(4900);var W=r(125);function C(t,e){return L(t)||T(t,e)||A(t,e)||E()}function E(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function A(t,e){if(!t)return;if(typeof t==="string")return O(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return O(t,e)}function O(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function T(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],c=!0,s=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){s=!0,i=t}finally{try{if(!c&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(s)throw i}}return u}}function L(t){if(Array.isArray(t))return t}function I(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var j=function t(e){var r=e.certificates,i=e.selectedCertificate,o=e.currentCertificate,c=e.onSelectCertificate,s=e.closeModal;var l=(0,u.useState)(i),d=C(l,2),f=d[0],p=d[1];var v=(0,u.useState)(o),h=C(v,2),m=h[0],b=h[1];var w=(0,u.useRef)(null);var _=r.findIndex((function(t){return t.key===m.key}));var x=Math.max(-1,_-1);var W=Math.min(r.length,_+1);(0,u.useEffect)((function(){var t=function t(e){if(e.key==="ArrowLeft"){A("previous")}else if(e.key==="ArrowRight"){A("next")}else if(e.key==="Enter"){E(m)}else if(e.key==="Escape"){s({action:"CLOSE"})}};window.addEventListener("keydown",t);return function(){window.removeEventListener("keydown",t)}}),[_,r]);(0,u.useEffect)((function(){if(w.current){w.current.focus()}}),[]);var E=function t(e){if(e.key===f){return}c(e);p(e.key)};var A=function t(e){if(e==="previous"&&_>0){b(r[x])}else if(e==="next"&&_<r.length-1){b(r[W])}};return(0,n.tZ)("div",{css:R.container},m&&(0,n.tZ)("div",{css:R.content},(0,n.tZ)("div",{css:R.certificateAndActions},(0,n.tZ)("img",{css:R.certificate,src:m.preview_src,alt:m.name}),(0,n.tZ)("div",{css:R.actionsWrapper},(0,n.tZ)(g.Z,{placement:"right",content:(0,a.__)("Close","tutor")},(0,n.tZ)("button",{ref:w,type:"button",css:[R.actionButton,R.closeButton,true?"":0,true?"":0],onClick:function t(){s({action:"CLOSE"})}},(0,n.tZ)(y.Z,{name:"cross",width:40,height:40}))),(0,n.tZ)(S.Z,{when:m.edit_url},(function(t){return(0,n.tZ)(g.Z,{placement:"right",content:(0,a.__)("Edit in Certificate Builder","tutor")},(0,n.tZ)("button",{type:"button",css:[R.actionButton,R.editButton,true?"":0,true?"":0],onClick:function e(){window.open(t,"_blank","noopener")}},(0,n.tZ)(y.Z,{name:"edit",width:40,height:40})))}))))),(0,n.tZ)("div",{css:R.navigatorWrapper},(0,n.tZ)("div",{css:R.navigator},(0,n.tZ)("button",{type:"button",css:[R.actionButton,R.navigatorButton,true?"":0,true?"":0],onClick:function t(){return A("previous")},disabled:x<0},(0,n.tZ)(y.Z,{name:!k.dZ?"chevronLeft":"chevronRight",width:40,height:40})),(0,n.tZ)(Z.Z,{variant:"primary",onClick:function t(){E(m);s({action:"CONFIRM"})},disabled:f===m.key},f===m.key?(0,a.__)("Selected","tutor"):(0,a.__)("Select","tutor")),(0,n.tZ)("button",{type:"button",css:[R.actionButton,R.navigatorButton,true?"":0,true?"":0],onClick:function t(){return A("next")},disabled:W>r.length-1},(0,n.tZ)(y.Z,{name:!k.dZ?"chevronRight":"chevronLeft",width:40,height:40})))))};const P=j;var R={container:(0,n.iv)("width:100%;height:100%;",W.i.display.flex("column"),";justify-content:center;align-items:center;gap:",_.W0[16],";position:relative;"+(true?"":0),true?"":0),content:(0,n.iv)(W.i.display.flex("column"),";justify-content:center;align-items:center;object-fit:contain;max-width:80vw;max-height:calc(100vh - 200px);width:100%;height:100%;"+(true?"":0),true?"":0),certificateAndActions:(0,n.iv)("position:relative;",W.i.display.flex(),";justify-content:center;align-items:center;gap:",_.W0[20],";height:100%;"+(true?"":0),true?"":0),certificate:true?{name:"ukfjzf",styles:"width:100%;height:100%;object-fit:contain"}:0,actionsWrapper:(0,n.iv)("position:absolute;top:0;right:-",_.W0[56],";bottom:0;",W.i.display.flex("column"),";justify-content:space-between;",_.Uo.smallMobile,"{right:-",_.W0[32],";}"+(true?"":0),true?"":0),actionButton:(0,n.iv)("place-self:center start;",W.i.resetButton,";display:inline-flex;align-items:center;justify-content:center;svg{color:",_.Jv.action.secondary["default"],";transition:color 0.3s ease-in-out;}"+(true?"":0),true?"":0),closeButton:true?{name:"j2duhg",styles:"place-self:center start"}:0,editButton:true?{name:"ak8e88",styles:"place-self:center end"}:0,navigatorWrapper:(0,n.iv)(true?"":0,true?"":0),navigator:(0,n.iv)(W.i.display.flex(),";gap:",_.W0[16],";justify-content:center;background:",_.Jv.background.white,";padding:",_.W0[12],";border-radius:",_.E0[8],";"+(true?"":0),true?"":0),navigatorButton:(0,n.iv)("svg{color:",_.Jv.icon["default"],";}:disabled{cursor:not-allowed;svg{color:",_.Jv.icon.hints,";}}"+(true?"":0),true?"":0)};var M=r(7034);var J=(0,M.zs)();var N=function t(e){var r,o;var u=e.selectedCertificate,c=u===void 0?"":u,s=e.data,l=e.orientation,d=e.onSelectCertificate;var f=(0,w.d)(),p=f.showModal;var v=(0,i.NL)();var h=v.getQueryData(["CourseDetails",J]);var m=(r=((o=h===null||h===void 0?void 0:h.course_certificates_templates)!==null&&o!==void 0?o:[]).filter((function(t){return t.orientation===l&&(s.is_default?t.is_default===true:t.is_default===false)})))!==null&&r!==void 0?r:[];return(0,n.tZ)("div",{css:U.wrapper({isSelected:c===s.key,isLandScape:l==="landscape"})},(0,n.tZ)("div",{"data-overlay":true,onClick:function t(){return d(s.key)},onKeyDown:function t(e){if(e.key==="Enter"||e.key===" "){d(s.key)}}}),(0,n.tZ)(S.Z,{when:s.preview_src,fallback:(0,n.tZ)("div",{css:U.emptyCard},(0,n.tZ)(y.Z,{name:"outlineNone",width:49,height:49}),(0,n.tZ)("span",null,(0,a.__)("None","tutor")))},(function(t){return(0,n.tZ)("img",{css:U.certificateImage,src:t,alt:s.name})})),(0,n.tZ)(S.Z,{when:s.preview_src||s.key!==c},(0,n.tZ)("div",{"data-footer-actions":true,css:U.footerWrapper},(0,n.tZ)(S.Z,{when:s.preview_src},(0,n.tZ)(Z.Z,{variant:"secondary",isOutlined:true,size:"small",onClick:function t(){p({component:P,props:{certificates:m,currentCertificate:s,selectedCertificate:c,onSelectCertificate:function t(e){d(e.key)}}})}},(0,a.__)("Preview","tutor"))),(0,n.tZ)(S.Z,{when:s.key!==c},(0,n.tZ)(Z.Z,{variant:"primary",size:"small",onClick:function t(){return d(s.key)}},(0,a.__)("Select","tutor"))))),(0,n.tZ)("div",{css:U.checkIcon({isSelected:c===s.key})},(0,n.tZ)(y.Z,{name:"checkFilledWhite",width:32,height:32})))};const B=N;var U={wrapper:function t(e){var r=e.isSelected,i=r===void 0?false:r,o=e.isLandScape,a=o===void 0?false:o;return(0,n.iv)(W.i.centeredFlex,";background-color:",_.Jv.surface.courseBuilder,";max-height:",a?"154px":"217px",";min-height:",a?"154px":"217px",";height:100%;position:relative;outline:",i?"2px":"1px"," solid ",i?_.Jv.stroke.brand:_.Jv.stroke["default"],";border-radius:",_.E0.card,";transition:all 0.15s ease-in-out;[data-overlay]{position:absolute;top:0;left:0;right:0;bottom:0;border-radius:",_.E0.card,";}",i&&(0,n.iv)("[data-overlay]{background:",_.Jv.brand.blue,";opacity:0.1;}"+(true?"":0),true?"":0)," &:hover,&:focus-within{border-color:",_.Jv.stroke.brand,";[data-footer-actions]{opacity:1;}[data-overlay]{background:",_.Jv.brand.blue,";opacity:0.1;}}"+(true?"":0),true?"":0)},emptyCard:(0,n.iv)(W.i.flexCenter(),";flex-direction:column;height:100%;width:100%;gap:",_.W0[8],";",x.c.caption("medium"),";svg{color:",_.Jv.color.black[20],";}"+(true?"":0),true?"":0),certificateImage:(0,n.iv)("width:100%;height:100%;object-fit:contain;border-radius:",_.E0.card,";"+(true?"":0),true?"":0),footerWrapper:(0,n.iv)("opacity:0;position:absolute;left:0px;right:0px;bottom:0px;",W.i.flexCenter(),";align-items:center;gap:",_.W0[4],";padding-block:",_.W0[8],";background:",_.Jv.bg.white,";border-bottom-left-radius:",_.E0.card,";border-bottom-right-radius:",_.E0.card,";"+(true?"":0),true?"":0),checkIcon:function t(e){var r=e.isSelected,i=r===void 0?false:r;return(0,n.iv)("opacity:",i?1:0,";position:absolute;top:-14px;right:-14px;border-bottom-left-radius:",_.E0.card,";svg{color:",_.Jv.icon.brand,";}"+(true?"":0),true?"":0)}};var D=r(8305);var G=r(7583);var F=r(5219);var z=r(2676);var q=r(1604);const Q=r.p+"images/ce66848f153ead20b9a1a038f1d68fe7-certificates-2x.webp";const Y=r.p+"images/d68012d900d127a80f22fbc9302fad33-certificates.webp";var $=!!D.y.tutor_pro_url;var H=function t(){if($){return null}return(0,n.tZ)("div",{css:K.emptyState},(0,n.tZ)("img",{css:K.placeholderImage,src:Y,srcSet:"".concat(Y," 1x, ").concat(Q," 2x"),alt:(0,a.__)("Pro Placeholder","tutor")}),(0,n.tZ)("div",{css:K.featureAndActionWrapper},(0,n.tZ)("h5",{css:K.title},(0,a.__)("Award Students with Custom Certificates","tutor")),(0,n.tZ)("div",{css:K.featuresWithTitle},(0,n.tZ)("div",null,(0,a.__)("Celebrate success with personalized certificates. Recognize student achievements with unique designs that inspire and motivate students.","tutor")),(0,n.tZ)("div",{css:K.features},(0,n.tZ)("div",{css:K.feature},(0,n.tZ)(y.Z,{name:"materialCheck",width:20,height:20,style:K.checkIcon}),(0,n.tZ)("span",null,(0,a.__)("Design personalized certificates that highlight their accomplishments and boost their confidence.","tutor"))),(0,n.tZ)("div",{css:K.feature},(0,n.tZ)(y.Z,{name:"materialCheck",width:20,height:20,style:K.checkIcon}),(0,n.tZ)("span",null,(0,a.__)("Inspire them with a touch of credibility and recognition tailored just for them.","tutor")))))),(0,n.tZ)("div",{css:K.actionsButton},(0,n.tZ)(Z.Z,{variant:"primary",icon:(0,n.tZ)(y.Z,{name:"crown",width:24,height:24}),onClick:function t(){window.open(D.Z.TUTOR_PRICING_PAGE,"_blank","noopener")}},(0,a.__)("Get Tutor LMS Pro","tutor"))))};const V=H;var K={emptyState:(0,n.iv)("padding-bottom:",_.W0[12],";",W.i.display.flex("column")," gap:",_.W0[20],";"+(true?"":0),true?"":0),placeholderImage:function t(e){var r=e.notFound;return(0,n.iv)("max-width:100%;width:100%;height:",r?"189px":"312px;",";object-fit:cover;object-position:center;border-radius:",_.E0[6],";"+(true?"":0),true?"":0)},featureAndActionWrapper:(0,n.iv)(W.i.display.flex("column")," align-items:center;gap:",_.W0[12],";"+(true?"":0),true?"":0),title:(0,n.iv)(x.c.heading5("medium")," color:",_.Jv.text.primary,";"+(true?"":0),true?"":0),featuresWithTitle:(0,n.iv)(W.i.display.flex("column")," max-width:500px;width:100%;gap:",_.W0[8],";",x.c.body("regular"),";"+(true?"":0),true?"":0),features:(0,n.iv)(W.i.display.flex("column")," gap:",_.W0[8],";"+(true?"":0),true?"":0),feature:(0,n.iv)(W.i.display.flex()," gap:",_.W0[12],";color:",_.Jv.text.title,";text-wrap:pretty;"+(true?"":0),true?"":0),checkIcon:(0,n.iv)("flex-shrink:0;color:",_.Jv.text.success,";"+(true?"":0),true?"":0),actionsButton:(0,n.iv)(W.i.flexCenter()," margin-top:",_.W0[4],";"+(true?"":0),true?"":0)};function X(t){return rt(t)||et(t)||ot(t)||tt()}function tt(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function et(t){if(typeof Symbol!=="undefined"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function rt(t){if(Array.isArray(t))return at(t)}function nt(t,e){return ct(t)||ut(t,e)||ot(t,e)||it()}function it(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function ot(t,e){if(!t)return;if(typeof t==="string")return at(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return at(t,e)}function at(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function ut(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],c=!0,s=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){s=!0,i=t}finally{try{if(!c&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(s)throw i}}return u}}function ct(t){if(Array.isArray(t))return t}function st(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var lt=(0,M.zs)();var dt=!!D.y.tutor_pro_url;var ft=(0,F.ro)(k.AO.TUTOR_CERTIFICATE);var pt=function t(e){var r;var o=e.isSidebarVisible;var s=(0,i.NL)();var l=s.getQueryData(["CourseDetails",lt]);var d=(r=l===null||l===void 0?void 0:l.course_certificates_templates)!==null&&r!==void 0?r:[];var f=d.filter((function(t){return t.is_default}));var p=(0,c.Gc)();var v=p.watch("tutor_course_certificate_template");var h=(0,u.useState)("templates"),m=nt(h,2),Z=m[0],w=m[1];var C=(0,u.useState)("landscape"),E=nt(C,2),A=E[0],O=E[1];var T=(0,u.useState)(v),L=nt(T,2),I=L[0],j=L[1];var P=d.some((function(t){return t.orientation==="landscape"&&(Z==="templates"?t.is_default:!t.is_default)}));var R=d.some((function(t){return t.orientation==="portrait"&&(Z==="templates"?t.is_default:!t.is_default)}));(0,u.useEffect)((function(){if(d.length){if(f.length===0){w("custom_certificates")}var t=d.some((function(t){return t.orientation==="landscape"}));if(!t&&A==="landscape"){O("portrait")}}if(v==="none"){j(v);return}var e=d.find((function(t){return t.key===v}));if(e){if(A!==e.orientation){O(e.orientation)}w(e.is_default?"templates":"custom_certificates");j(e.key)}}),[v,d]);var M=d.filter((function(t){return t.orientation===A&&(Z==="templates"?t===null||t===void 0?void 0:t.is_default:!(t!==null&&t!==void 0&&t.is_default))}));var J=function t(e){w(e);var r=d.some((function(t){return t.orientation==="landscape"&&(e==="templates"?t.is_default:!t.is_default)}));var n=d.some((function(t){return t.orientation==="portrait"&&(e==="templates"?t.is_default:!t.is_default)}));O((function(t){if(r&&n||!r&&!n){return t}return r?"landscape":"portrait"}))};var N=function t(e){O(e)};var U=function t(e){p.setValue("tutor_course_certificate_template",e);j(e)};var D=[].concat(X(f.length?[{label:(0,a.__)("Templates","tutor"),value:"templates"}]:[]),[{label:k.iM.isAboveSmallMobile?(0,a.__)("Custom Certificates","tutor"):(0,a.__)("Certificates","tutor"),value:"custom_certificates"}]);return(0,n.tZ)(S.Z,{when:dt&&ft,fallback:(0,n.tZ)(V,null)},(0,n.tZ)(S.Z,{when:ft},(0,n.tZ)("div",{css:mt.tabs},(0,n.tZ)(b.Z,{wrapperCss:mt.tabsWrapper,tabList:D,activeTab:Z,onChange:J}),(0,n.tZ)("div",{css:mt.orientation},(0,n.tZ)(S.Z,{when:P&&R},(0,n.tZ)(g.Z,{delay:200,content:(0,a.__)("Landscape","tutor")},(0,n.tZ)("button",{type:"button",css:[W.i.resetButton,mt.orientationButton({isActive:A==="landscape"}),true?"":0,true?"":0],onClick:function t(){return N("landscape")}},(0,n.tZ)(y.Z,{name:A==="landscape"?"landscapeFilled":"landscape",width:32,height:32}))),(0,n.tZ)(g.Z,{delay:200,content:(0,a.__)("Portrait","tutor")},(0,n.tZ)("button",{type:"button",css:[W.i.resetButton,mt.orientationButton({isActive:A==="portrait"}),true?"":0,true?"":0],onClick:function t(){return N("portrait")}},(0,n.tZ)(y.Z,{name:A==="portrait"?"portraitFilled":"portrait",width:32,height:32})))))),(0,n.tZ)("div",{css:mt.certificateWrapper({hasCertificates:M.length>0,isSidebarVisible:o})},(0,n.tZ)(S.Z,{when:d.length&&(f.length===0||Z==="templates")},(0,n.tZ)(B,{selectedCertificate:I,onSelectCertificate:U,data:{key:"none",name:(0,a.__)("None","tutor"),preview_src:"",background_src:"",orientation:"landscape",url:""},orientation:A})),(0,n.tZ)(S.Z,{when:M.length>0,fallback:(0,n.tZ)("div",{css:mt.emptyState},(0,n.tZ)("img",{css:mt.placeholderImage({notFound:true}),src:q.Z,srcSet:"".concat(q.Z," 1x, ").concat(z.Z," 2x"),alt:(0,a.__)("Not Found","tutor")}),(0,n.tZ)("div",{css:mt.featureAndActionWrapper},(0,n.tZ)("p",{css:(0,n.iv)(x.c.body("medium")," color:",_.Jv.text.subdued,";"+(true?"":0),true?"":0)},(0,a.__)("You didn’t create any certificate yet!","tutor"))))},(0,n.tZ)(G.Z,{each:M},(function(t){return(0,n.tZ)(B,{key:t.key,selectedCertificate:I,onSelectCertificate:U,data:t,orientation:A})}))))))};const vt=pt;var ht=true?{name:"1vm53vd",styles:"grid-template-columns:1fr;place-items:center"}:0;var mt={tabs:true?{name:"bjn8wh",styles:"position:relative"}:0,tabsWrapper:true?{name:"1vqsuxb",styles:"button{min-width:auto;}"}:0,certificateWrapper:function t(e){var r=e.hasCertificates,i=e.isSidebarVisible;return(0,n.iv)("display:grid;grid-template-columns:repeat(",i?3:4,", 1fr);gap:",_.W0[16],";padding-top:",_.W0[12],";",!r&&ht," ",_.Uo.smallMobile,"{grid-template-columns:1fr 1fr;}"+(true?"":0),true?"":0)},orientation:(0,n.iv)(W.i.display.flex()," gap:",_.W0[8],";position:absolute;height:32px;right:0;bottom:",_.W0[4],";"+(true?"":0),true?"":0),orientationButton:function t(e){var r=e.isActive;return(0,n.iv)("display:inline-flex;color:",r?_.Jv.icon.brand:_.Jv.icon["default"],";border-radius:",_.E0[4],";&:focus-visible{outline:2px solid ",_.Jv.stroke.brand,";outline-offset:1px;}"+(true?"":0),true?"":0)},emptyState:(0,n.iv)("padding-block:",_.W0[16]," ",_.W0[12],";",W.i.display.flex("column")," gap:",_.W0[20],";"+(true?"":0),true?"":0),placeholderImage:function t(e){var r=e.notFound;return(0,n.iv)("max-width:100%;width:100%;height:",r?"189px":"312px;",";object-fit:cover;object-position:center;border-radius:",_.E0[6],";"+(true?"":0),true?"":0)},featureAndActionWrapper:(0,n.iv)(W.i.display.flex("column")," align-items:center;gap:",_.W0[12],";"+(true?"":0),true?"":0),actionsButton:(0,n.iv)(W.i.flexCenter()," margin-top:",_.W0[4],";"+(true?"":0),true?"":0)};const yt=r.p+"images/0807df3bda222a2334c674c106073bdf-prerequisites-2x.webp";const gt=r.p+"images/ec5640e219546af0a227e9787705bec1-prerequisites.webp";var bt=!!D.y.tutor_pro_url;var Zt=function t(){return(0,n.tZ)("div",{css:_t.emptyState},(0,n.tZ)("img",{css:_t.placeholderImage,src:gt,srcSet:"".concat(gt," 1x, ").concat(yt," 2x"),alt:(0,a.__)("Pro Placeholder","tutor")}),(0,n.tZ)("div",{css:_t.featureAndActionWrapper},(0,n.tZ)("div",{css:_t.featuresWithTitle},(0,n.tZ)("div",null,(0,a.__)("Guide Students with Course Prerequisites","tutor")),(0,n.tZ)(S.Z,{when:!bt},(0,n.tZ)("div",{css:_t.features},(0,n.tZ)("div",{css:_t.feature},(0,n.tZ)(y.Z,{name:"materialCheck",width:20,height:20,style:_t.checkIcon}),(0,n.tZ)("span",null,(0,a.__)("Easily set prerequisites to structure your courses and guide student progress.","tutor"))),(0,n.tZ)("div",{css:_t.feature},(0,n.tZ)(y.Z,{name:"materialCheck",width:20,height:20,style:_t.checkIcon}),(0,n.tZ)("span",null,(0,a.__)("Offer customized learning journeys by setting multiple prerequisites for any course.","tutor"))))))))};const wt=Zt;var _t={emptyState:(0,n.iv)("padding:",_.W0[12]," ",_.W0[12]," ",_.W0[24]," ",_.W0[12],";",W.i.display.flex("column")," gap:",_.W0[20],";border:1px solid ",_.Jv.stroke.divider,";border-radius:",_.E0.card,";background-color:",_.Jv.background.white,";"+(true?"":0),true?"":0),placeholderImage:(0,n.iv)("max-width:100%;width:100%;height:112px;object-fit:cover;object-position:center;border-radius:",_.E0[6],";"+(true?"":0),true?"":0),featureAndActionWrapper:(0,n.iv)(W.i.display.flex("column")," align-items:center;gap:",_.W0[12],";padding-inline:",_.W0[4],";"+(true?"":0),true?"":0),featuresWithTitle:(0,n.iv)(W.i.display.flex("column")," gap:",_.W0[8],";",x.c.caption("medium"),";"+(true?"":0),true?"":0),features:(0,n.iv)(W.i.display.flex("column")," gap:",_.W0[8],";"+(true?"":0),true?"":0),feature:(0,n.iv)(x.c.small(),";",W.i.display.flex()," gap:",_.W0[12],";color:",_.Jv.text.title,";text-wrap:pretty;"+(true?"":0),true?"":0),checkIcon:(0,n.iv)("flex-shrink:0;color:",_.Jv.text.success,";"+(true?"":0),true?"":0),actionsButton:(0,n.iv)(W.i.flexCenter()," margin-top:",_.W0[4],";"+(true?"":0),true?"":0)};var xt=r(9612);var kt=r(3366);var St=r(9546);var Wt=r(1933);var Ct=r(6873);var Et=r(932);var At=r(7363);function Ot(t){"@babel/helpers - typeof";return Ot="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ot(t)}function Tt(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Tt=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function t(e,r,n){return e[r]=n}}function s(t,e,r,i){var o=e&&e.prototype instanceof f?e:f,a=Object.create(o.prototype),u=new S(i||[]);return n(a,"_invoke",{value:w(t,r,u)}),a}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=s;var d={};function f(){}function p(){}function v(){}var h={};c(h,o,(function(){return this}));var m=Object.getPrototypeOf,y=m&&m(m(W([])));y&&y!==e&&r.call(y,o)&&(h=y);var g=v.prototype=f.prototype=Object.create(h);function b(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function Z(t,e){function i(n,o,a,u){var c=l(t[n],t,o);if("throw"!==c.type){var s=c.arg,d=s.value;return d&&"object"==Ot(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){i("next",t,a,u)}),(function(t){i("throw",t,a,u)})):e.resolve(d).then((function(t){s.value=t,a(s)}),(function(t){return i("throw",t,a,u)}))}u(c.arg)}var o;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){i(r,n,t,e)}))}return o=o?o.then(a,a):a()}})}function w(t,e,r){var n="suspendedStart";return function(i,o){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===i)throw o;return C()}for(r.method=i,r.arg=o;;){var a=r.delegate;if(a){var u=_(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var c=l(t,e,r);if("normal"===c.type){if(n=r.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(n="completed",r.method="throw",r.arg=c.arg)}}}function _(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,_(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var i=l(n,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,d;var o=i.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function x(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function k(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(x,this),this.reset(!0)}function W(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return i.next=i}}return{next:C}}function C(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=c(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,c(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},b(Z.prototype),c(Z.prototype,a,(function(){return this})),t.AsyncIterator=Z,t.async=function(e,r,n,i,o){void 0===o&&(o=Promise);var a=new Z(s(e,r,n,i),o);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},b(g),c(g,u,"Generator"),c(g,o,(function(){return this})),c(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=W,S.prototype={constructor:S,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(k),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function i(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],u=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),s=r.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function t(e,n){for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),k(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var i=n.completion;if("throw"===i.type){var o=i.arg;k(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:W(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function Lt(t,e,r,n,i,o,a){try{var u=t[o](a);var c=u.value}catch(t){r(t);return}if(u.done){e(c)}else{Promise.resolve(c).then(n,i)}}function It(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var o=t.apply(e,r);function a(t){Lt(o,n,i,a,u,"next",t)}function u(t){Lt(o,n,i,a,u,"throw",t)}a(undefined)}))}}function jt(t,e){return Nt(t)||Jt(t,e)||Rt(t,e)||Pt()}function Pt(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Rt(t,e){if(!t)return;if(typeof t==="string")return Mt(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Mt(t,e)}function Mt(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Jt(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],c=!0,s=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){s=!0,i=t}finally{try{if(!c&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(s)throw i}}return u}}function Nt(t){if(Array.isArray(t))return t}var Bt=(0,M.zs)();var Ut=function t(e){var r=e.data,i=e.topicId;var o=(0,u.useState)(false),c=jt(o,2),s=c[0],l=c[1];var d=(0,u.useState)(false),f=jt(d,2),p=f[0],v=f[1];var h=(0,Ct.qN)(String(Bt),{"post-id":r.ID,"event-id":r.meeting_data.id});var m=(0,u.useRef)(null);var g=(0,u.useRef)(null);var b=r.meeting_data,w=r.post_title;var x=function(){var t=It(Tt().mark((function t(){var e;return Tt().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:r.next=2;return h.mutateAsync();case 2:e=r.sent;if(e.status_code===200){v(false)}case 4:case"end":return r.stop()}}),t)})));return function e(){return t.apply(this,arguments)}}();var S=(0,St["default"])(new Date(b.start_datetime),k.E_.day);var C=(0,St["default"])(new Date(b.start_datetime),k.E_.month);var E=(0,St["default"])(new Date(b.start_datetime),k.E_.year);var A=(0,St["default"])(new Date(b.start_datetime),k.E_.hoursMinutes).split(" "),O=jt(A,2),T=O[0],L=O[1],I=L===void 0?"":L;return(0,n.tZ)(At.Fragment,null,(0,n.tZ)("div",{css:Gt.card({isPopoverOpen:p||s})},(0,n.tZ)("div",{css:Gt.cardTitle},w),(0,n.tZ)("div",{css:Gt.cardContent},(0,n.tZ)("span",{css:Gt.inlineContent},(0,a.__)("Start time","tutor"),(0,n.tZ)("div",{css:Gt.hyphen}),(0,n.tZ)("div",{css:Gt.meetingDateTime,className:"date-time"},(0,n.tZ)("span",{css:(0,n.iv)({fontWeight:_.Ue.semiBold},true?"":0,true?"":0)},"".concat(S," ")),(0,n.tZ)("span",null,"".concat(C," ")),(0,n.tZ)("span",{css:(0,n.iv)({fontWeight:_.Ue.semiBold},true?"":0,true?"":0)},"".concat(E,", ")),(0,n.tZ)("span",{css:(0,n.iv)({fontWeight:_.Ue.semiBold},true?"":0,true?"":0)},"".concat(T," ")),(0,n.tZ)("span",null,"".concat(I," ")))),(0,n.tZ)("div",{css:Gt.buttonWrapper},(0,n.tZ)(Z.Z,{variant:"secondary",size:"small",type:"button",onClick:function t(){window.open(b.meet_link,"_blank","noopener")}},(0,a.__)("Start Meeting","tutor")),(0,n.tZ)("div",{css:Gt.actions},(0,n.tZ)("button",{ref:m,type:"button",css:W.i.actionButton,"data-visually-hidden":true,onClick:function t(){return l(true)}},(0,n.tZ)(y.Z,{name:"edit",width:24,height:24})),(0,n.tZ)("button",{type:"button",css:W.i.actionButton,"data-visually-hidden":true,onClick:function t(){v(true)},ref:g},(0,n.tZ)(y.Z,{name:"delete",width:24,height:24})))))),(0,n.tZ)(xt.Z,{isOpen:s,triggerRef:m,closePopover:function t(){return l(false)},maxWidth:"306px"},(0,n.tZ)(Et.Z,{data:r,topicId:i,onCancel:function t(){l(false)}})),(0,n.tZ)(Wt.Z,{isOpen:p,triggerRef:g,closePopover:F.ZT,maxWidth:"258px",title:(0,a.sprintf)((0,a.__)('Delete "%s"',"tutor"),w),message:(0,a.__)("Are you sure you want to delete this meeting? This cannot be undone.","tutor"),animationType:kt.ru.slideUp,arrow:"auto",hideArrow:true,isLoading:h.isPending,confirmButton:{text:(0,a.__)("Delete","tutor"),variant:"text",isDelete:true},cancelButton:{text:(0,a.__)("Cancel","tutor"),variant:"text"},onConfirmation:It(Tt().mark((function t(){return Tt().wrap((function t(e){while(1)switch(e.prev=e.next){case 0:e.next=2;return x();case 2:case"end":return e.stop()}}),t)}))),onCancel:function t(){v(false)}}))};const Dt=Ut;var Gt={card:function t(e){var r=e.isPopoverOpen,i=r===void 0?false:r;return(0,n.iv)(W.i.display.flex("column")," padding:",_.W0[8]," ",_.W0[12]," ",_.W0[12]," ",_.W0[12],";gap:",_.W0[8],";border-radius:",_.E0[6],";transition:background 0.3s ease;[data-visually-hidden]{opacity:0;transition:opacity 0.3s ease-in-out;}",i&&(0,n.iv)("background-color:",_.Jv.background.hover,";[data-visually-hidden]{opacity:1;}.date-time{background:none;}"+(true?"":0),true?"":0)," &:hover,&:focus-within{background-color:",_.Jv.background.hover,";[data-visually-hidden]{opacity:1;}.date-time{background:none;}}",_.Uo.smallTablet,"{[data-visually-hidden]{opacity:1;}}"+(true?"":0),true?"":0)},cardTitle:(0,n.iv)(x.c.caption("medium")," color:",_.Jv.text.title,";"+(true?"":0),true?"":0),cardContent:(0,n.iv)(W.i.display.flex("column")," gap:",_.W0[8],";"+(true?"":0),true?"":0),hyphen:(0,n.iv)("width:5px;height:2px;background:",_.Jv.stroke["default"],";"+(true?"":0),true?"":0),inlineContent:(0,n.iv)(x.c.small("regular")," ",W.i.display.flex()," align-items:center;gap:",_.W0[6],";"+(true?"":0),true?"":0),meetingDateTime:(0,n.iv)("padding:",_.W0[4]," ",_.W0[6],";border-radius:",_.E0[4],";background:",_.Jv.background.status.processing,";transition:background 0.3s ease-in-out;"+(true?"":0),true?"":0),buttonWrapper:(0,n.iv)(W.i.display.flex(),";margin-top:",_.W0[8],";justify-content:space-between;"+(true?"":0),true?"":0),actions:(0,n.iv)(W.i.display.flex(),";align-items:center;gap:",_.W0[8],";"+(true?"":0),true?"":0)};var Ft=r(7100);var zt=r(7363);function qt(t){"@babel/helpers - typeof";return qt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},qt(t)}function Qt(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Qt=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function t(e,r,n){return e[r]=n}}function s(t,e,r,i){var o=e&&e.prototype instanceof f?e:f,a=Object.create(o.prototype),u=new S(i||[]);return n(a,"_invoke",{value:w(t,r,u)}),a}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=s;var d={};function f(){}function p(){}function v(){}var h={};c(h,o,(function(){return this}));var m=Object.getPrototypeOf,y=m&&m(m(W([])));y&&y!==e&&r.call(y,o)&&(h=y);var g=v.prototype=f.prototype=Object.create(h);function b(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function Z(t,e){function i(n,o,a,u){var c=l(t[n],t,o);if("throw"!==c.type){var s=c.arg,d=s.value;return d&&"object"==qt(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){i("next",t,a,u)}),(function(t){i("throw",t,a,u)})):e.resolve(d).then((function(t){s.value=t,a(s)}),(function(t){return i("throw",t,a,u)}))}u(c.arg)}var o;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){i(r,n,t,e)}))}return o=o?o.then(a,a):a()}})}function w(t,e,r){var n="suspendedStart";return function(i,o){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===i)throw o;return C()}for(r.method=i,r.arg=o;;){var a=r.delegate;if(a){var u=_(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var c=l(t,e,r);if("normal"===c.type){if(n=r.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(n="completed",r.method="throw",r.arg=c.arg)}}}function _(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,_(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var i=l(n,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,d;var o=i.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function x(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function k(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(x,this),this.reset(!0)}function W(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return i.next=i}}return{next:C}}function C(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=c(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,c(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},b(Z.prototype),c(Z.prototype,a,(function(){return this})),t.AsyncIterator=Z,t.async=function(e,r,n,i,o){void 0===o&&(o=Promise);var a=new Z(s(e,r,n,i),o);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},b(g),c(g,u,"Generator"),c(g,o,(function(){return this})),c(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=W,S.prototype={constructor:S,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(k),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function i(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],u=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),s=r.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function t(e,n){for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),k(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var i=n.completion;if("throw"===i.type){var o=i.arg;k(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:W(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function Yt(t,e,r,n,i,o,a){try{var u=t[o](a);var c=u.value}catch(t){r(t);return}if(u.done){e(c)}else{Promise.resolve(c).then(n,i)}}function $t(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var o=t.apply(e,r);function a(t){Yt(o,n,i,a,u,"next",t)}function u(t){Yt(o,n,i,a,u,"throw",t)}a(undefined)}))}}function Ht(t,e){return ee(t)||te(t,e)||Kt(t,e)||Vt()}function Vt(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Kt(t,e){if(!t)return;if(typeof t==="string")return Xt(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Xt(t,e)}function Xt(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function te(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],c=!0,s=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){s=!0,i=t}finally{try{if(!c&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(s)throw i}}return u}}function ee(t){if(Array.isArray(t))return t}var re=(0,M.zs)();var ne=function t(e){var r=e.data,i=e.meetingHost,o=e.topicId;var c=(0,u.useState)(false),s=Ht(c,2),l=s[0],d=s[1];var f=(0,u.useState)(false),p=Ht(f,2),v=p[0],h=p[1];var m=(0,Ct.m8)(String(re));var g=(0,u.useRef)(null);var b=(0,u.useRef)(null);var w=r.ID,x=r.meeting_data,C=r.post_title,E=r.meeting_starts_at;var A=function(){var t=$t(Qt().mark((function t(){var e;return Qt().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:r.next=2;return m.mutateAsync(w);case 2:e=r.sent;if(e.success){h(false)}case 4:case"end":return r.stop()}}),t)})));return function e(){return t.apply(this,arguments)}}();var O=(0,St["default"])(new Date(E),k.E_.day);var T=(0,St["default"])(new Date(E),k.E_.month);var L=(0,St["default"])(new Date(E),k.E_.year);var I=(0,St["default"])(new Date(E),k.E_.hoursMinutes).split(" "),j=Ht(I,2),P=j[0],R=j[1],M=R===void 0?"":R;return(0,n.tZ)(zt.Fragment,null,(0,n.tZ)("div",{css:oe.card({isPopoverOpen:v||l})},(0,n.tZ)("div",{css:oe.cardTitle},C),(0,n.tZ)("div",{css:oe.cardContent},(0,n.tZ)("span",{css:oe.inlineContent},(0,a.__)("Start time","tutor"),(0,n.tZ)("div",{css:oe.hyphen}),(0,n.tZ)("div",{css:oe.meetingDateTime,className:"date-time"},(0,n.tZ)("span",{css:(0,n.iv)({fontWeight:_.Ue.semiBold},true?"":0,true?"":0)},"".concat(O," ")),(0,n.tZ)("span",null,"".concat(T," ")),(0,n.tZ)("span",{css:(0,n.iv)({fontWeight:_.Ue.semiBold},true?"":0,true?"":0)},"".concat(L,", ")),(0,n.tZ)("span",{css:(0,n.iv)({fontWeight:_.Ue.semiBold},true?"":0,true?"":0)},"".concat(P," ")),(0,n.tZ)("span",null,"".concat(M," ")))),(0,n.tZ)(S.Z,{when:x.id},(0,n.tZ)("div",{css:oe.inlineContent},(0,a.__)("Meeting Token","tutor"),(0,n.tZ)("div",{css:oe.hyphen}),(0,n.tZ)("div",null,x.id))),(0,n.tZ)(S.Z,{when:x.password},(0,n.tZ)("div",{css:oe.inlineContent},(0,a.__)("Password","tutor"),(0,n.tZ)("div",{css:oe.hyphen}),(0,n.tZ)("div",null,x.password))),(0,n.tZ)("div",{css:oe.buttonWrapper},(0,n.tZ)(Z.Z,{variant:"secondary",size:"small",type:"button",onClick:function t(){window.open(x.start_url,"_blank","noopener")}},(0,a.__)("Start Meeting","tutor")),(0,n.tZ)("div",{css:oe.actions},(0,n.tZ)("button",{ref:g,type:"button",css:W.i.actionButton,"data-visually-hidden":true,onClick:function t(){d(true)}},(0,n.tZ)(y.Z,{name:"edit",width:24,height:24})),(0,n.tZ)("button",{type:"button",css:W.i.actionButton,"data-visually-hidden":true,onClick:function t(){return h(true)},ref:b},(0,n.tZ)(y.Z,{name:"delete",width:24,height:24})))))),(0,n.tZ)(xt.Z,{isOpen:l,triggerRef:g,closePopover:function t(){return d(false)},maxWidth:"306px"},(0,n.tZ)(Ft.Z,{data:r,meetingHost:i,topicId:o,onCancel:function t(){d(false)}})),(0,n.tZ)(Wt.Z,{isOpen:v,triggerRef:b,closePopover:F.ZT,maxWidth:"258px",title:(0,a.sprintf)((0,a.__)('Delete "%s"',"tutor"),C),message:(0,a.__)("Are you sure you want to delete this meeting? This cannot be undone.","tutor"),animationType:kt.ru.slideUp,arrow:"auto",hideArrow:true,isLoading:m.isPending,confirmButton:{text:(0,a.__)("Delete","tutor"),variant:"text",isDelete:true},cancelButton:{text:(0,a.__)("Cancel","tutor"),variant:"text"},onConfirmation:$t(Qt().mark((function t(){return Qt().wrap((function t(e){while(1)switch(e.prev=e.next){case 0:e.next=2;return A();case 2:case"end":return e.stop()}}),t)}))),onCancel:function t(){h(false)}}))};const ie=ne;var oe={card:function t(e){var r=e.isPopoverOpen,i=r===void 0?false:r;return(0,n.iv)(W.i.display.flex("column")," padding:",_.W0[8]," ",_.W0[12]," ",_.W0[12]," ",_.W0[12],";gap:",_.W0[8],";border-radius:",_.E0[6],";transition:background 0.3s ease;[data-visually-hidden]{opacity:0;transition:opacity 0.3s ease-in-out;}",i&&(0,n.iv)("background-color:",_.Jv.background.hover,";[data-visually-hidden]{opacity:1;}.date-time{background:none;}"+(true?"":0),true?"":0)," &:hover,&:focus-within{background-color:",_.Jv.background.hover,";[data-visually-hidden]{opacity:1;}.date-time{background:none;}}",_.Uo.smallTablet,"{[data-visually-hidden]{opacity:1;}}"+(true?"":0),true?"":0)},cardTitle:(0,n.iv)(x.c.caption("medium")," color:",_.Jv.text.title,";"+(true?"":0),true?"":0),cardContent:(0,n.iv)(W.i.display.flex("column")," gap:",_.W0[8],";"+(true?"":0),true?"":0),hyphen:(0,n.iv)("width:5px;height:2px;background:",_.Jv.stroke["default"],";"+(true?"":0),true?"":0),inlineContent:(0,n.iv)(x.c.small("regular")," ",W.i.display.flex()," align-items:center;gap:",_.W0[6],";"+(true?"":0),true?"":0),meetingDateTime:(0,n.iv)("padding:",_.W0[4]," ",_.W0[6],";border-radius:",_.E0[4],";background:",_.Jv.background.status.processing,";transition:background 0.3s ease-in-out;"+(true?"":0),true?"":0),buttonWrapper:(0,n.iv)(W.i.display.flex(),";margin-top:",_.W0[8],";justify-content:space-between;"+(true?"":0),true?"":0),actions:(0,n.iv)(W.i.display.flex(),";align-items:center;gap:",_.W0[8],";"+(true?"":0),true?"":0)};const ae=r.p+"images/ce30a118f93885425aa8ace20559a99e-live-class-2x.webp";const ue=r.p+"images/b3a93f8abedeeefea14556d1f4bac6b1-live-class.webp";function ce(t,e){return pe(t)||fe(t,e)||le(t,e)||se()}function se(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function le(t,e){if(!t)return;if(typeof t==="string")return de(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return de(t,e)}function de(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function fe(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],c=!0,s=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){s=!0,i=t}finally{try{if(!c&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(s)throw i}}return u}}function pe(t){if(Array.isArray(t))return t}function ve(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var he=!!D.y.tutor_pro_url;var me=(0,F.ro)(k.AO.TUTOR_ZOOM_INTEGRATION);var ye=(0,F.ro)(k.AO.TUTOR_GOOGLE_MEET_INTEGRATION);var ge=(0,M.zs)();var be=true?{name:"1d3w5wq",styles:"width:100%"}:0;var Ze=true?{name:"1d3w5wq",styles:"width:100%"}:0;var we=function t(){var e,r,o;var c=(0,i.NL)();var s=c.getQueryData(["CourseDetails",ge]);var l=(e=s===null||s===void 0?void 0:s.zoom_meetings)!==null&&e!==void 0?e:[];var p=(r=s===null||s===void 0?void 0:s.zoom_users)!==null&&r!==void 0?r:{};var v=(o=s===null||s===void 0?void 0:s.google_meet_meetings)!==null&&o!==void 0?o:[];var h=(0,u.useState)(null),m=ce(h,2),g=m[0],b=m[1];var w=(0,u.useRef)(null);var _=(0,u.useRef)(null);if(he&&!me&&!ye){return null}return(0,n.tZ)("div",{css:xe.liveClass},(0,n.tZ)("span",{css:xe.label},(0,a.__)("Schedule Live Class","tutor"),!he&&(0,n.tZ)(d.Z,{content:(0,a.__)("Pro","tutor")})),(0,n.tZ)(S.Z,{when:he,fallback:(0,n.tZ)(f.Z,{size:"small",removeBorder:false,emptyStateImage:ue,emptyStateImage2x:ae,imageAltText:(0,a.__)("Tutor LMS PRO","tutor"),title:(0,a.__)("Bring your courses to life and engage students with interactive live classes.","tutor"),actions:(0,n.tZ)(Z.Z,{size:"small",icon:(0,n.tZ)(y.Z,{name:"crown",width:24,height:24}),onClick:function t(){window.open(D.Z.TUTOR_PRICING_PAGE,"_blank","noopener")}},(0,a.__)("Get Tutor LMS Pro","tutor"))})},(0,n.tZ)(S.Z,{when:me||ye},(0,n.tZ)(S.Z,{when:me},(0,n.tZ)("div",{css:xe.meetingsWrapper({hasMeeting:l.length>0})},(0,n.tZ)(G.Z,{each:l},(function(t){return(0,n.tZ)("div",{key:t.ID,css:xe.meeting({hasMeeting:l.length>0})},(0,n.tZ)(ie,{data:t,meetingHost:p}))})),(0,n.tZ)("div",{css:xe.meetingsFooter({hasMeeting:l.length>0})},(0,n.tZ)(Z.Z,{variant:"secondary",icon:(0,n.tZ)(y.Z,{name:"zoomColorize",width:24,height:24}),buttonCss:Ze,onClick:function t(){return b("zoom")},ref:w},(0,a.__)("Create a Zoom Meeting","tutor"))))),(0,n.tZ)(S.Z,{when:ye},(0,n.tZ)("div",{css:xe.meetingsWrapper({hasMeeting:v.length>0})},(0,n.tZ)(G.Z,{each:v},(function(t){return(0,n.tZ)("div",{key:t.ID,css:xe.meeting({hasMeeting:v.length>0})},(0,n.tZ)(Dt,{data:t}))})),(0,n.tZ)("div",{css:xe.meetingsFooter({hasMeeting:v.length>0})},(0,n.tZ)(Z.Z,{variant:"secondary",icon:(0,n.tZ)(y.Z,{name:"googleMeetColorize",width:24,height:24}),buttonCss:be,onClick:function t(){return b("google_meet")},ref:_},(0,a.__)("Create a Google Meet Link","tutor"))))))),(0,n.tZ)(xt.Z,{triggerRef:w,isOpen:g==="zoom",closePopover:F.ZT,animationType:kt.ru.slideUp,closeOnEscape:false,arrow:k.iM.isAboveMobile?"auto":"absoluteCenter",hideArrow:true},(0,n.tZ)(Ft.Z,{data:null,meetingHost:p,onCancel:function t(){b(null)}})),(0,n.tZ)(xt.Z,{triggerRef:_,isOpen:g==="google_meet",closePopover:F.ZT,animationType:kt.ru.slideUp,closeOnEscape:false,arrow:k.iM.isAboveMobile?"auto":"absoluteCenter",hideArrow:true},(0,n.tZ)(Et.Z,{data:null,onCancel:function t(){b(null)}})))};const _e=we;var xe={label:(0,n.iv)(W.i.display.inlineFlex()," align-items:center;gap:",_.W0[4],";",x.c.body()," color:",_.Jv.text.title,";"+(true?"":0),true?"":0),liveClass:(0,n.iv)(W.i.display.flex("column")," gap:",_.W0[8],";"+(true?"":0),true?"":0),meetingsWrapper:function t(e){var r=e.hasMeeting;return(0,n.iv)(W.i.display.flex("column")," background-color:",_.Jv.background.white,";border-radius:",_.E0.card,";",r&&(0,n.iv)("border:1px solid ",_.Jv.stroke["default"],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},meeting:function t(e){var r=e.hasMeeting;return(0,n.iv)("padding:",_.W0[8]," ",_.W0[8]," ",_.W0[12]," ",_.W0[8],";",r&&(0,n.iv)("border-bottom:1px solid ",_.Jv.stroke.divider,";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},meetingsFooter:function t(e){var r=e.hasMeeting;return(0,n.iv)("width:100%;",r&&(0,n.iv)("padding:",_.W0[12]," ",_.W0[8],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)}};var ke=r(2322);var Se=r(6051);var We=r(1343);var Ce=r(551);const Ee=r.p+"images/a980852605189e7bcfedc3c12a7844d0-attachments-2x.webp";const Ae=r.p+"images/8f57a0f5b77c41a0b937b821ca4b4e29-attachments.webp";var Oe=r(5033);var Te=r(8488);function Le(){Le=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Le.apply(this,arguments)}function Ie(t){return Me(t)||Re(t)||Pe(t)||je()}function je(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Pe(t,e){if(!t)return;if(typeof t==="string")return Je(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Je(t,e)}function Re(t){if(typeof Symbol!=="undefined"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function Me(t){if(Array.isArray(t))return Je(t)}function Je(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Ne=!!D.y.tutor_pro_url;var Be=(0,M.zs)();var Ue=(0,F.ro)(k.AO.TUTOR_PREREQUISITES);var De=(0,F.ro)(k.AO.TUTOR_COURSE_ATTACHMENTS);var Ge=(0,F.ro)(k.AO.TUTOR_CERTIFICATE);var Fe=function t(){var e;var r=(0,s.s0)();(0,u.useEffect)((function(){if(!Be){r(Ce.L.Home.buildLink(),{replace:true})}}),[r]);var y=(0,c.Gc)();var g=(0,i.NL)();var b=(0,o.y)({queryKey:["CourseDetails",Be]});var Z=g.getQueryData(["CourseDetails",Be]);var w=(Z===null||Z===void 0?void 0:(e=Z.course_prerequisites)===null||e===void 0?void 0:e.map((function(t){return String(t.id)})))||[];var _=(0,Te.C)({params:{excludedIds:[String(Be)].concat(Ie(w)),limit:-1},isEnabled:!!Ue&&!b});if(!Be){return null}if(!Be){return null}var x=!Ne||[k.AO.TUTOR_PREREQUISITES,k.AO.TUTOR_COURSE_ATTACHMENTS,k.AO.TUTOR_ZOOM_INTEGRATION,k.AO.TUTOR_GOOGLE_MEET_INTEGRATION].some(F.ro);return(0,n.tZ)("div",{css:qe.wrapper({showSidebar:x})},(0,n.tZ)("div",{css:qe.leftSide},(0,n.tZ)(ke.Z,{title:(0,a.__)("Additional","tutor"),backUrl:"/curriculum"}),(0,n.tZ)("div",{css:qe.formWrapper},(0,n.tZ)(l.xu,{bordered:true},(0,n.tZ)("div",{css:qe.titleAndSub},(0,n.tZ)(l.$K,null,(0,a.__)("Overview","tutor")),(0,n.tZ)(l.Jg,null,(0,a.__)("Provide essential course information to attract and inform potential students","tutor"))),(0,n.tZ)("div",{css:qe.fieldsWrapper},(0,n.tZ)(c.Qr,{name:"course_benefits",control:y.control,render:function t(e){return(0,n.tZ)(m.Z,Le({},e,{label:(0,a.__)("What Will I Learn?","tutor"),placeholder:(0,a.__)("Define the key takeaways from this course (list one benefit per line)","tutor"),rows:2,enableResize:true,loading:!!b&&!e.field.value}))}}),(0,n.tZ)(c.Qr,{name:"course_target_audience",control:y.control,render:function t(e){return(0,n.tZ)(m.Z,Le({},e,{label:(0,a.__)("Target Audience","tutor"),placeholder:(0,a.__)("Specify the target audience that will benefit the most from the course. (One Line Per target audience)","tutor"),rows:2,enableResize:true,loading:!!b&&!e.field.value}))}}),(0,n.tZ)("div",{css:qe.totalCourseDuration},(0,n.tZ)(c.Qr,{name:"course_duration_hours",control:y.control,render:function t(e){return(0,n.tZ)(h.Z,Le({},e,{type:"number",label:(0,a.__)("Total Course Duration","tutor"),placeholder:"0",contentPosition:"right",content:(0,a.__)("hour(s)","tutor"),loading:!!b&&!e.field.value}))}}),(0,n.tZ)(c.Qr,{name:"course_duration_minutes",control:y.control,render:function t(e){return(0,n.tZ)(h.Z,Le({},e,{type:"number",placeholder:"0",contentPosition:"right",content:(0,a.__)("min(s)","tutor"),loading:!!b&&!e.field.value}))}})),(0,n.tZ)(c.Qr,{name:"course_material_includes",control:y.control,render:function t(e){return(0,n.tZ)(m.Z,Le({},e,{label:(0,a.__)("Materials Included","tutor"),placeholder:(0,a.__)("A list of assets you will be providing for the students in this course (One Per Line)","tutor"),rows:4,enableResize:true,loading:!!b&&!e.field.value}))}}),(0,n.tZ)(c.Qr,{name:"course_requirements",control:y.control,render:function t(e){return(0,n.tZ)(m.Z,Le({},e,{label:(0,a.__)("Requirements/Instructions","tutor"),placeholder:(0,a.__)("Additional requirements or special instructions for the students (One Per Line)","tutor"),rows:2,enableResize:true,loading:!!b&&!e.field.value}))}}))),(0,n.tZ)(S.Z,{when:!Ne||Ge},(0,n.tZ)(l.xu,{bordered:true},(0,n.tZ)("div",{css:qe.titleAndSub},(0,n.tZ)(l.$K,{css:qe.titleWithBadge},(0,a.__)("Certificate","tutor"),(0,n.tZ)(S.Z,{when:!Ne},(0,n.tZ)(d.Z,{content:(0,a.__)("Pro","tutor")}))),(0,n.tZ)(S.Z,{when:Ne&&(0,F.ro)(k.AO.TUTOR_CERTIFICATE)},(0,n.tZ)(l.Jg,null,(0,a.__)("Select a certificate to award your learners.","tutor")))),(0,n.tZ)(S.Z,{when:!b,fallback:(0,n.tZ)(Oe.g4,null)},(0,n.tZ)(vt,{isSidebarVisible:x})))),(0,n.tZ)(We.Z,{section:"Additional.after_certificates",form:y})),(0,n.tZ)(S.Z,{when:k.iM.isAboveTablet},(0,n.tZ)(Se.Z,null))),(0,n.tZ)(S.Z,{when:x},(0,n.tZ)("div",{css:qe.sidebar},(0,n.tZ)(S.Z,{when:!Ne||Ue},(0,n.tZ)("div",null,(0,n.tZ)("div",{css:qe.label},(0,a.__)("Course Prerequisites","tutor"),!Ne&&(0,n.tZ)(d.Z,{content:(0,a.__)("Pro","tutor")})),(0,n.tZ)(S.Z,{when:Ne&&Ue,fallback:(0,n.tZ)(wt,null)},(0,n.tZ)(c.Qr,{name:"course_prerequisites",control:y.control,render:function t(e){var r;return(0,n.tZ)(p.Z,Le({},e,{placeholder:(0,a.__)("Search courses for prerequisites","tutor"),options:((r=_.data)===null||r===void 0?void 0:r.results)||[],isSearchable:true,loading:_.isLoading||!!b&&!e.field.value}))}})))),(0,n.tZ)(S.Z,{when:!Ne||De},(0,n.tZ)("div",null,(0,n.tZ)("div",{css:qe.label},(0,a.__)("Attachments","tutor"),!Ne&&(0,n.tZ)(d.Z,{content:(0,a.__)("Pro","tutor")})),(0,n.tZ)(S.Z,{when:Ne&&De,fallback:(0,n.tZ)(S.Z,{when:!Ne},(0,n.tZ)(f.Z,{size:"small",removeBorder:false,emptyStateImage:Ae,emptyStateImage2x:Ee,title:(0,a.__)((0,a.__)("Provide additional resources like downloadable files and reference materials.","tutor"))}))},(0,n.tZ)(c.Qr,{name:"course_attachments",control:y.control,render:function t(e){return(0,n.tZ)(v.Z,Le({},e,{buttonText:(0,a.__)("Upload Attachment","tutor"),selectMultiple:true}))}})))),(0,n.tZ)(_e,null),(0,n.tZ)(We.Z,{section:"Additional.bottom_of_sidebar",form:y}))),(0,n.tZ)(S.Z,{when:!k.iM.isAboveTablet},(0,n.tZ)(Se.Z,null)))};const ze=Fe;var qe={wrapper:function t(e){var r=e.showSidebar;return(0,n.iv)("display:grid;grid-template-columns:",r?"1fr 338px":"1fr",";width:100%;",_.Uo.smallTablet,"{grid-template-columns:1fr;gap:",_.W0[24],";}"+(true?"":0),true?"":0)},leftSide:(0,n.iv)("padding:",_.W0[32]," ",_.W0[32]," ",_.W0[32]," 0;",W.i.display.flex("column")," gap:",_.W0[32],";",_.Uo.smallTablet,"{padding:0;padding-top:",_.W0[16],";gap:",_.W0[16],";}"+(true?"":0),true?"":0),formWrapper:(0,n.iv)(W.i.display.flex("column")," gap:",_.W0[24],";"+(true?"":0),true?"":0),titleAndSub:(0,n.iv)(W.i.display.flex("column")," gap:",_.W0[4],";margin-bottom:",_.W0[20],";"+(true?"":0),true?"":0),titleWithBadge:(0,n.iv)("span{",W.i.display.flex(),";align-items:center;gap:",_.W0[4],";}"+(true?"":0),true?"":0),fieldsWrapper:(0,n.iv)(W.i.display.flex("column")," gap:",_.W0[24],";"+(true?"":0),true?"":0),totalCourseDuration:(0,n.iv)(W.i.display.flex()," align-items:end;gap:",_.W0[8],";&>div{flex:1;}"+(true?"":0),true?"":0),sidebar:(0,n.iv)(W.i.display.flex("column")," padding:",_.W0[32]," 0 ",_.W0[32]," ",_.W0[32],";border-left:1px solid ",_.Jv.stroke.divider,";min-height:calc(100vh - (",_.J9,"px + ",_.xQ,"px));gap:",_.W0[16],";",_.Uo.smallTablet,"{padding:0;padding-top:",_.W0[24],";border-left:none;border-top:1px solid ",_.Jv.stroke.divider,";}"+(true?"":0),true?"":0),label:(0,n.iv)(W.i.display.inlineFlex()," align-items:center;gap:",_.W0[4],";",x.c.body("medium")," color:",_.Jv.text.title,";margin-bottom:",_.W0[8],";"+(true?"":0),true?"":0)}},2141:(t,e,r)=>{r.d(e,{y:()=>a});var n=r(7363);var i=r(7037);var o=r(202);"use client";function a(t,e){const r=(0,o.NL)(e);const a=r.getQueryCache();return n.useSyncExternalStore(n.useCallback((t=>a.subscribe(i.V.batchCalls(t))),[a]),(()=>r.isFetching(t)),(()=>r.isFetching(t)))}}}]);