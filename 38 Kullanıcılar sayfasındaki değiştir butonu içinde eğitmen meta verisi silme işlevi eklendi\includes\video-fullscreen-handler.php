<?php
/**
 * Video Tam Ekran Yöneticisi
 *
 * Mobil cihazlarda ekran döndürüldüğünde veya tam ekran ikonuna tıklandığında
 * videoyu otomatik olarak tam ekrana geçiren JavaScript'i ekler.
 *
 * @package Tutor
 * @subpackage VideoPlayer
 * <AUTHOR> <<EMAIL>>
 * @since 1.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Video Tam Ekran Yöneticisi sınıfı
 *
 * @since 1.0.0
 */
class Tutor_Video_Fullscreen_Handler {

    /**
     * Sınıf yapıcısı
     *
     * @since 1.0.0
     */
    public function __construct() {
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
    }

    /**
     * JavaScript dosyasını frontend'e ekler
     *
     * @since 1.0.0
     * @return void
     */
    public function enqueue_scripts() {
        // Sadece kurs sayfalarında yükle
        if (function_exists('is_singular') && (is_singular('courses') || is_singular('lesson') || is_singular('tutor_quiz') || is_singular('tutor_assignments'))) {
            wp_enqueue_script(
                'tutor-orientation-fullscreen',
                plugins_url('assets/js/tutor-orientation-fullscreen.js', dirname(__FILE__)),
                array('jquery'),
                TUTOR_VERSION,
                true
            );
        }
    }
}

// Sınıfı başlat
new Tutor_Video_Fullscreen_Handler();
