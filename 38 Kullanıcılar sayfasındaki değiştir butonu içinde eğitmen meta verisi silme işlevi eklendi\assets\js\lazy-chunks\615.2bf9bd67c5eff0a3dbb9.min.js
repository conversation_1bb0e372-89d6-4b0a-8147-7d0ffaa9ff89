"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[615],{7584:(e,t,n)=>{n.d(t,{Z:()=>s});var r=n(3645);var o=n.n(r);var a=o()((function(e){return e[1]}));a.push([e.id,'/* Variables declaration */\n/* prettier-ignore */\n.rdp-root {\n  --rdp-accent-color: blue; /* The accent color used for selected days and UI elements. */\n  --rdp-accent-background-color: #f0f0ff; /* The accent background color used for selected days and UI elements. */\n\n  --rdp-day-height: 44px; /* The height of the day cells. */\n  --rdp-day-width: 44px; /* The width of the day cells. */\n  \n  --rdp-day_button-border-radius: 100%; /* The border radius of the day cells. */\n  --rdp-day_button-border: 2px solid transparent; /* The border of the day cells. */\n  --rdp-day_button-height: 42px; /* The height of the day cells. */\n  --rdp-day_button-width: 42px; /* The width of the day cells. */\n  \n  --rdp-selected-border: 2px solid var(--rdp-accent-color); /* The border of the selected days. */\n  --rdp-disabled-opacity: 0.5; /* The opacity of the disabled days. */\n  --rdp-outside-opacity: 0.75; /* The opacity of the days outside the current month. */\n  --rdp-today-color: var(--rdp-accent-color); /* The color of the today\'s date. */\n  \n  --rdp-dropdown-gap: 0.5rem;/* The gap between the dropdowns used in the month captons. */\n  \n  --rdp-months-gap: 2rem; /* The gap between the months in the multi-month view. */\n  \n  --rdp-nav_button-disabled-opacity: 0.5; /* The opacity of the disabled navigation buttons. */\n  --rdp-nav_button-height: 2.25rem; /* The height of the navigation buttons. */\n  --rdp-nav_button-width: 2.25rem; /* The width of the navigation buttons. */\n  --rdp-nav-height: 2.75rem; /* The height of the navigation bar. */\n  \n  --rdp-range_middle-background-color: var(--rdp-accent-background-color); /* The color of the background for days in the middle of a range. */\n  --rdp-range_middle-color: inherit;/* The color of the range text. */\n  \n  --rdp-range_start-color: white; /* The color of the range text. */\n  --rdp-range_start-background: linear-gradient(var(--rdp-gradient-direction), transparent 50%, var(--rdp-range_middle-background-color) 50%); /* Used for the background of the start of the selected range. */\n  --rdp-range_start-date-background-color: var(--rdp-accent-color); /* The background color of the date when at the start of the selected range. */\n  \n  --rdp-range_end-background: linear-gradient(var(--rdp-gradient-direction), var(--rdp-range_middle-background-color) 50%, transparent 50%); /* Used for the background of the end of the selected range. */\n  --rdp-range_end-color: white;/* The color of the range text. */\n  --rdp-range_end-date-background-color: var(--rdp-accent-color); /* The background color of the date when at the end of the selected range. */\n  \n  --rdp-week_number-border-radius: 100%; /* The border radius of the week number. */\n  --rdp-week_number-border: 2px solid transparent; /* The border of the week number. */\n  \n  --rdp-week_number-height: var(--rdp-day-height); /* The height of the week number cells. */\n  --rdp-week_number-opacity: 0.75; /* The opacity of the week number. */\n  --rdp-week_number-width: var(--rdp-day-width); /* The width of the week number cells. */\n  --rdp-weeknumber-text-align: center; /* The text alignment of the weekday cells. */\n\n  --rdp-weekday-opacity: 0.75; /* The opacity of the weekday. */\n  --rdp-weekday-padding: 0.5rem 0rem; /* The padding of the weekday. */\n  --rdp-weekday-text-align: center; /* The text alignment of the weekday cells. */\n\n  --rdp-gradient-direction: 90deg;\n}\n\n.rdp-root[dir="rtl"] {\n  --rdp-gradient-direction: -90deg;\n}\n\n.rdp-root[data-broadcast-calendar="true"] {\n  --rdp-outside-opacity: unset;\n}\n\n/* Root of the component. */\n.rdp-root {\n  position: relative; /* Required to position the navigation toolbar. */\n  box-sizing: border-box;\n}\n\n.rdp-root * {\n  box-sizing: border-box;\n}\n\n.rdp-day {\n  width: var(--rdp-day-width);\n  height: var(--rdp-day-height);\n  text-align: center;\n}\n\n.rdp-day_button {\n  background: none;\n  padding: 0;\n  margin: 0;\n  cursor: pointer;\n  font: inherit;\n  color: inherit;\n  justify-content: center;\n  align-items: center;\n  display: flex;\n\n  width: var(--rdp-day_button-width);\n  height: var(--rdp-day_button-height);\n  border: var(--rdp-day_button-border);\n  border-radius: var(--rdp-day_button-border-radius);\n}\n\n.rdp-day_button:disabled {\n  cursor: revert;\n}\n\n.rdp-caption_label {\n  z-index: 1;\n\n  position: relative;\n  display: inline-flex;\n  align-items: center;\n\n  white-space: nowrap;\n  border: 0;\n}\n\n.rdp-dropdown:focus-visible ~ .rdp-caption_label {\n  outline: 5px auto Highlight;\n  outline: 5px auto -webkit-focus-ring-color;\n}\n\n.rdp-button_next,\n.rdp-button_previous {\n  border: none;\n  background: none;\n  padding: 0;\n  margin: 0;\n  cursor: pointer;\n  font: inherit;\n  color: inherit;\n  -moz-appearance: none;\n  -webkit-appearance: none;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  appearance: none;\n\n  width: var(--rdp-nav_button-width);\n  height: var(--rdp-nav_button-height);\n}\n\n.rdp-button_next:disabled,\n.rdp-button_previous:disabled {\n  cursor: revert;\n\n  opacity: var(--rdp-nav_button-disabled-opacity);\n}\n\n.rdp-chevron {\n  display: inline-block;\n  fill: var(--rdp-accent-color);\n}\n\n.rdp-root[dir="rtl"] .rdp-nav .rdp-chevron {\n  transform: rotate(180deg);\n}\n\n.rdp-root[dir="rtl"] .rdp-nav .rdp-chevron {\n  transform: rotate(180deg);\n  transform-origin: 50%;\n}\n\n.rdp-dropdowns {\n  position: relative;\n  display: inline-flex;\n  align-items: center;\n  gap: var(--rdp-dropdown-gap);\n}\n.rdp-dropdown {\n  z-index: 2;\n\n  /* Reset */\n  opacity: 0;\n  appearance: none;\n  position: absolute;\n  inset-block-start: 0;\n  inset-block-end: 0;\n  inset-inline-start: 0;\n  width: 100%;\n  margin: 0;\n  padding: 0;\n  cursor: inherit;\n  border: none;\n  line-height: inherit;\n}\n\n.rdp-dropdown_root {\n  position: relative;\n  display: inline-flex;\n  align-items: center;\n}\n\n.rdp-dropdown_root[data-disabled="true"] .rdp-chevron {\n  opacity: var(--rdp-disabled-opacity);\n}\n\n.rdp-month_caption {\n  display: flex;\n  align-content: center;\n  height: var(--rdp-nav-height);\n  font-weight: bold;\n  font-size: large;\n}\n\n.rdp-months {\n  position: relative;\n  display: flex;\n  flex-wrap: wrap;\n  gap: var(--rdp-months-gap);\n  max-width: fit-content;\n}\n\n.rdp-month_grid {\n  border-collapse: collapse;\n}\n\n.rdp-nav {\n  position: absolute;\n  inset-block-start: 0;\n  inset-inline-end: 0;\n\n  display: flex;\n  align-items: center;\n\n  height: var(--rdp-nav-height);\n}\n\n.rdp-weekday {\n  opacity: var(--rdp-weekday-opacity);\n  padding: var(--rdp-weekday-padding);\n  font-weight: 500;\n  font-size: smaller;\n  text-align: var(--rdp-weekday-text-align);\n  text-transform: var(--rdp-weekday-text-transform);\n}\n\n.rdp-week_number {\n  opacity: var(--rdp-week_number-opacity);\n  font-weight: 400;\n  font-size: small;\n  height: var(--rdp-week_number-height);\n  width: var(--rdp-week_number-width);\n  border: var(--rdp-week_number-border);\n  border-radius: var(--rdp-week_number-border-radius);\n  text-align: var(--rdp-weeknumber-text-align);\n}\n\n/* DAY MODIFIERS */\n.rdp-today:not(.rdp-outside) {\n  color: var(--rdp-today-color);\n}\n\n.rdp-selected {\n  font-weight: bold;\n  font-size: large;\n}\n\n.rdp-selected .rdp-day_button {\n  border: var(--rdp-selected-border);\n}\n\n.rdp-outside {\n  opacity: var(--rdp-outside-opacity);\n}\n\n.rdp-disabled {\n  opacity: var(--rdp-disabled-opacity);\n}\n\n.rdp-hidden {\n  visibility: hidden;\n  color: var(--rdp-range_start-color);\n}\n\n.rdp-range_start {\n  background: var(--rdp-range_start-background);\n}\n\n.rdp-range_start .rdp-day_button {\n  background-color: var(--rdp-range_start-date-background-color);\n  color: var(--rdp-range_start-color);\n}\n\n.rdp-range_middle {\n  background-color: var(--rdp-range_middle-background-color);\n}\n\n.rdp-range_middle .rdp-day_button {\n  border-color: transparent;\n  border: unset;\n  border-radius: unset;\n  color: var(--rdp-range_middle-color);\n}\n\n.rdp-range_end {\n  background: var(--rdp-range_end-background);\n  color: var(--rdp-range_end-color);\n}\n\n.rdp-range_end .rdp-day_button {\n  color: var(--rdp-range_start-color);\n  background-color: var(--rdp-range_end-date-background-color);\n}\n\n.rdp-range_start.rdp-range_end {\n  background: revert;\n}\n\n.rdp-focusable {\n  cursor: pointer;\n}\n',""]);const s=a},3645:e=>{e.exports=function(e){var t=[];t.toString=function t(){return this.map((function(t){var n=e(t);if(t[2]){return"@media ".concat(t[2]," {").concat(n,"}")}return n})).join("")};t.i=function(e,n,r){if(typeof e==="string"){e=[[null,e,""]]}var o={};if(r){for(var a=0;a<this.length;a++){var s=this[a][0];if(s!=null){o[s]=true}}}for(var i=0;i<e.length;i++){var c=[].concat(e[i]);if(r&&o[c[0]]){continue}if(n){if(!c[2]){c[2]=n}else{c[2]="".concat(n," and ").concat(c[2])}}t.push(c)}};return t}},7041:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(8545);var o=n(9013);var a=n(8717);var s=n(3882);function i(e,t){var n;(0,s.Z)(1,arguments);var i=(0,a.Z)((0,o["default"])(e.start));var c=(0,o["default"])(e.end);var d=i.getTime();var u=c.getTime();if(d>=u){throw new RangeError("Invalid interval")}var l=[];var f=i;var h=Number((n=t===null||t===void 0?void 0:t.step)!==null&&n!==void 0?n:1);if(h<1||isNaN(h))throw new RangeError("`options.step` must be a number equal to or greater than 1");while(f.getTime()<=u){l.push((0,o["default"])(f));f=(0,r["default"])(f,h)}return l}},7042:(e,t,n)=>{n.d(t,{default:()=>s});var r=n(3946);var o=n(9013);var a=n(3882);function s(e,t){(0,a.Z)(2,arguments);var n=(0,o["default"])(e);var s=(0,r.Z)(t);n.setHours(s);return n}},4543:(e,t,n)=>{n.d(t,{default:()=>s});var r=n(3946);var o=n(9013);var a=n(3882);function s(e,t){(0,a.Z)(2,arguments);var n=(0,o["default"])(e);var s=(0,r.Z)(t);n.setMinutes(s);return n}},8717:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(9013);var o=n(3882);function a(e){(0,o.Z)(1,arguments);var t=(0,r["default"])(e);t.setSeconds(0,0);return t}},1313:(e,t,n)=>{var r=n(3379);var o=n.n(r);var a=n(7795);var s=n.n(a);var i=n(569);var c=n.n(i);var d=n(3565);var u=n.n(d);var l=n(9216);var f=n.n(l);var h=n(4589);var m=n.n(h);var p=n(7584);var g={};g.styleTagTransform=m();g.setAttributes=u();g.insert=c().bind(null,"head");g.domAPI=s();g.insertStyleElement=f();var b=o()(p.Z,g);var y=p.Z&&p.Z.locals?p.Z.locals:undefined},3379:e=>{var t=[];function n(e){var n=-1;for(var r=0;r<t.length;r++){if(t[r].identifier===e){n=r;break}}return n}function r(e,r){var a={};var s=[];for(var i=0;i<e.length;i++){var c=e[i];var d=r.base?c[0]+r.base:c[0];var u=a[d]||0;var l="".concat(d," ").concat(u);a[d]=u+1;var f=n(l);var h={css:c[1],media:c[2],sourceMap:c[3],supports:c[4],layer:c[5]};if(f!==-1){t[f].references++;t[f].updater(h)}else{var m=o(h,r);r.byIndex=i;t.splice(i,0,{identifier:l,updater:m,references:1})}s.push(l)}return s}function o(e,t){var n=t.domAPI(t);n.update(e);var r=function t(r){if(r){if(r.css===e.css&&r.media===e.media&&r.sourceMap===e.sourceMap&&r.supports===e.supports&&r.layer===e.layer){return}n.update(e=r)}else{n.remove()}};return r}e.exports=function(e,o){o=o||{};e=e||[];var a=r(e,o);return function e(s){s=s||[];for(var i=0;i<a.length;i++){var c=a[i];var d=n(c);t[d].references--}var u=r(s,o);for(var l=0;l<a.length;l++){var f=a[l];var h=n(f);if(t[h].references===0){t[h].updater();t.splice(h,1)}}a=u}}},569:e=>{var t={};function n(e){if(typeof t[e]==="undefined"){var n=document.querySelector(e);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement){try{n=n.contentDocument.head}catch(e){n=null}}t[e]=n}return t[e]}function r(e,t){var r=n(e);if(!r){throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.")}r.appendChild(t)}e.exports=r},9216:e=>{function t(e){var t=document.createElement("style");e.setAttributes(t,e.attributes);e.insert(t,e.options);return t}e.exports=t},3565:(e,t,n)=>{function r(e){var t=true?n.nc:0;if(t){e.setAttribute("nonce",t)}}e.exports=r},7795:e=>{function t(e,t,n){var r="";if(n.supports){r+="@supports (".concat(n.supports,") {")}if(n.media){r+="@media ".concat(n.media," {")}var o=typeof n.layer!=="undefined";if(o){r+="@layer".concat(n.layer.length>0?" ".concat(n.layer):""," {")}r+=n.css;if(o){r+="}"}if(n.media){r+="}"}if(n.supports){r+="}"}var a=n.sourceMap;if(a&&typeof btoa!=="undefined"){r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(a))))," */")}t.styleTagTransform(r,e,t.options)}function n(e){if(e.parentNode===null){return false}e.parentNode.removeChild(e)}function r(e){if(typeof document==="undefined"){return{update:function e(){},remove:function e(){}}}var r=e.insertStyleElement(e);return{update:function n(o){t(r,e,o)},remove:function e(){n(r)}}}e.exports=r},4589:e=>{function t(e,t){if(t.styleSheet){t.styleSheet.cssText=e}else{while(t.firstChild){t.removeChild(t.firstChild)}t.appendChild(document.createTextNode(e))}}e.exports=t},2809:(e,t,n)=>{n.d(t,{_W:()=>Yo});var r={};n.r(r);n.d(r,{Button:()=>qn,CaptionLabel:()=>jn,Chevron:()=>$n,Day:()=>Gn,DayButton:()=>Xn,Dropdown:()=>Qn,DropdownNav:()=>Rn,Footer:()=>Jn,Month:()=>Kn,MonthCaption:()=>Vn,MonthGrid:()=>er,Months:()=>tr,MonthsDropdown:()=>or,Nav:()=>ar,NextMonthButton:()=>sr,Option:()=>ir,PreviousMonthButton:()=>cr,Root:()=>dr,Select:()=>ur,Week:()=>lr,WeekNumber:()=>mr,WeekNumberHeader:()=>pr,Weekday:()=>fr,Weekdays:()=>hr,Weeks:()=>gr,YearsDropdown:()=>br});var o={};n.r(o);n.d(o,{formatCaption:()=>kr,formatDay:()=>Dr,formatMonthCaption:()=>Mr,formatMonthDropdown:()=>Or,formatWeekNumber:()=>Cr,formatWeekNumberHeader:()=>Nr,formatWeekdayName:()=>Tr,formatYearCaption:()=>Sr,formatYearDropdown:()=>Wr});var a={};n.r(a);n.d(a,{labelCaption:()=>Fr,labelDay:()=>Hr,labelDayButton:()=>Lr,labelGrid:()=>Br,labelGridcell:()=>Ir,labelMonthDropdown:()=>Ur,labelNav:()=>zr,labelNext:()=>Ar,labelPrevious:()=>Zr,labelWeekNumber:()=>jr,labelWeekNumberHeader:()=>$r,labelWeekday:()=>qr,labelYearDropdown:()=>Gr});var s=n(7363);var i;(function(e){e["Root"]="root";e["Chevron"]="chevron";e["Day"]="day";e["DayButton"]="day_button";e["CaptionLabel"]="caption_label";e["Dropdowns"]="dropdowns";e["Dropdown"]="dropdown";e["DropdownRoot"]="dropdown_root";e["Footer"]="footer";e["MonthGrid"]="month_grid";e["MonthCaption"]="month_caption";e["MonthsDropdown"]="months_dropdown";e["Month"]="month";e["Months"]="months";e["Nav"]="nav";e["NextMonthButton"]="button_next";e["PreviousMonthButton"]="button_previous";e["Week"]="week";e["Weeks"]="weeks";e["Weekday"]="weekday";e["Weekdays"]="weekdays";e["WeekNumber"]="week_number";e["WeekNumberHeader"]="week_number_header";e["YearsDropdown"]="years_dropdown"})(i||(i={}));var c;(function(e){e["disabled"]="disabled";e["hidden"]="hidden";e["outside"]="outside";e["focused"]="focused";e["today"]="today"})(c||(c={}));var d;(function(e){e["range_end"]="range_end";e["range_middle"]="range_middle";e["range_start"]="range_start";e["selected"]="selected"})(d||(d={}));const u=Symbol.for("constructDateFrom");const l={};const f={};function h(e,t){try{const n=l[e]||=new Intl.DateTimeFormat("en-GB",{timeZone:e,hour:"numeric",timeZoneName:"longOffset"}).format;const r=n(t).split("GMT")[1]||"";if(r in f)return f[r];return p(r,r.split(":"))}catch{if(e in f)return f[e];const t=e?.match(m);if(t)return p(e,t.slice(1));return NaN}}const m=/([+-]\d\d):?(\d\d)?/;function p(e,t){const n=+t[0];const r=+(t[1]||0);return f[e]=n>0?n*60+r:n*60-r}class g extends Date{constructor(...e){super();if(e.length>1&&typeof e[e.length-1]==="string"){this.timeZone=e.pop()}this.internal=new Date;if(isNaN(h(this.timeZone,this))){this.setTime(NaN)}else{if(!e.length){this.setTime(Date.now())}else if(typeof e[0]==="number"&&(e.length===1||e.length===2&&typeof e[1]!=="number")){this.setTime(e[0])}else if(typeof e[0]==="string"){this.setTime(+new Date(e[0]))}else if(e[0]instanceof Date){this.setTime(+e[0])}else{this.setTime(+new Date(...e));v(this,NaN);y(this)}}}static tz(e,...t){return t.length?new g(...t,e):new g(Date.now(),e)}withTimeZone(e){return new g(+this,e)}getTimezoneOffset(){return-h(this.timeZone,this)}setTime(e){Date.prototype.setTime.apply(this,arguments);y(this);return+this}[Symbol.for("constructDateFrom")](e){return new g(+new Date(e),this.timeZone)}}const b=/^(get|set)(?!UTC)/;Object.getOwnPropertyNames(Date.prototype).forEach((e=>{if(!b.test(e))return;const t=e.replace(b,"$1UTC");if(!g.prototype[t])return;if(e.startsWith("get")){g.prototype[e]=function(){return this.internal[t]()}}else{g.prototype[e]=function(){Date.prototype[t].apply(this.internal,arguments);w(this);return+this};g.prototype[t]=function(){Date.prototype[t].apply(this,arguments);y(this);return+this}}}));function y(e){e.internal.setTime(+e);e.internal.setUTCMinutes(e.internal.getUTCMinutes()-e.getTimezoneOffset())}function w(e){Date.prototype.setFullYear.call(e,e.internal.getUTCFullYear(),e.internal.getUTCMonth(),e.internal.getUTCDate());Date.prototype.setHours.call(e,e.internal.getUTCHours(),e.internal.getUTCMinutes(),e.internal.getUTCSeconds(),e.internal.getUTCMilliseconds());v(e)}function v(e){const t=h(e.timeZone,e);const n=new Date(+e);n.setUTCHours(n.getUTCHours()-1);const r=-new Date(+e).getTimezoneOffset();const o=-new Date(+n).getTimezoneOffset();const a=r-o;const s=Date.prototype.getHours.apply(e)!==e.internal.getUTCHours();if(a&&s)e.internal.setUTCMinutes(e.internal.getUTCMinutes()+a);const i=r-t;if(i)Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+i);const c=h(e.timeZone,e);const d=-new Date(+e).getTimezoneOffset();const u=d-c;const l=c!==t;const f=u-i;if(l&&f){Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+f);const t=h(e.timeZone,e);const n=c-t;if(n){e.internal.setUTCMinutes(e.internal.getUTCMinutes()+n);Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+n)}}}class k extends g{static tz(e,...t){return t.length?new k(...t,e):new k(Date.now(),e)}toISOString(){const[e,t,n]=this.tzComponents();const r=`${e}${t}:${n}`;return this.internal.toISOString().slice(0,-1)+r}toString(){return`${this.toDateString()} ${this.toTimeString()}`}toDateString(){const[e,t,n,r]=this.internal.toUTCString().split(" ");return`${e?.slice(0,-1)} ${n} ${t} ${r}`}toTimeString(){const e=this.internal.toUTCString().split(" ")[4];const[t,n,r]=this.tzComponents();return`${e} GMT${t}${n}${r} (${M(this.timeZone,this)})`}toLocaleString(e,t){return Date.prototype.toLocaleString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}toLocaleDateString(e,t){return Date.prototype.toLocaleDateString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}toLocaleTimeString(e,t){return Date.prototype.toLocaleTimeString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}tzComponents(){const e=this.getTimezoneOffset();const t=e>0?"-":"+";const n=String(Math.floor(Math.abs(e)/60)).padStart(2,"0");const r=String(Math.abs(e)%60).padStart(2,"0");return[t,n,r]}withTimeZone(e){return new k(+this,e)}[Symbol.for("constructDateFrom")](e){return new k(+new Date(e),this.timeZone)}}function M(e,t){return new Intl.DateTimeFormat("en-GB",{timeZone:e,timeZoneName:"long"}).format(t).slice(12)}const D=e=>t=>TZDate.tz(e,+new Date(t));function O(e,t){const n=[];const r=new Date(t.start);r.setUTCSeconds(0,0);const o=new Date(t.end);o.setUTCSeconds(0,0);const a=+o;let s=tzOffset(e,r);while(+r<a){r.setUTCMonth(r.getUTCMonth()+1);const t=tzOffset(e,r);if(t!=s){const t=new Date(r);t.setUTCMonth(t.getUTCMonth()-1);const o=+r;s=tzOffset(e,t);while(+t<o){t.setUTCDate(t.getUTCDate()+1);const r=tzOffset(e,t);if(r!=s){const r=new Date(t);r.setUTCDate(r.getUTCDate()-1);const o=+t;s=tzOffset(e,r);while(+r<o){r.setUTCHours(r.getUTCHours()+1);const t=tzOffset(e,r);if(t!==s){n.push({date:new Date(r),change:t-s,offset:t})}s=t}}s=r}}s=t}return n}const C=7;const N=365.2425;const T=Math.pow(10,8)*24*60*60*1e3;const W=-T;const S=6048e5;const x=864e5;const E=6e4;const Y=36e5;const P=1e3;const _=525600;const B=43200;const F=1440;const I=60;const L=3;const H=12;const z=4;const U=3600;const A=60;const Z=U*24;const q=Z*7;const j=Z*N;const $=j/12;const G=$*3;const X=Symbol.for("constructDateFrom");function Q(e,t){if(typeof e==="function")return e(t);if(e&&typeof e==="object"&&X in e)return e[X](t);if(e instanceof Date)return new e.constructor(t);return new Date(t)}const R=null&&Q;function J(e,t){return Q(t||e,e)}const K=null&&J;function V(e,t,n){const r=J(e,n?.in);if(isNaN(t))return Q(n?.in||e,NaN);if(!t)return r;r.setDate(r.getDate()+t);return r}const ee=null&&V;function te(e,t,n){const r=J(e,n?.in);if(isNaN(t))return Q(n?.in||e,NaN);if(!t){return r}const o=r.getDate();const a=Q(n?.in||e,r.getTime());a.setMonth(r.getMonth()+t+1,0);const s=a.getDate();if(o>=s){return a}else{r.setFullYear(a.getFullYear(),a.getMonth(),o);return r}}const ne=null&&te;function re(e,t,n){return V(e,t*7,n)}const oe=null&&re;function ae(e,t,n){return te(e,t*12,n)}const se=null&&ae;function ie(e){const t=J(e);const n=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));n.setUTCFullYear(t.getFullYear());return+e-+n}function ce(e,...t){const n=Q.bind(null,e||t.find((e=>typeof e==="object")));return t.map(n)}function de(e,t){const n=J(e,t?.in);n.setHours(0,0,0,0);return n}const ue=null&&de;function le(e,t,n){const[r,o]=ce(n?.in,e,t);const a=de(r);const s=de(o);const i=+a-ie(a);const c=+s-ie(s);return Math.round((i-c)/x)}const fe=null&&le;function he(e,t,n){const[r,o]=ce(n?.in,e,t);const a=r.getFullYear()-o.getFullYear();const s=r.getMonth()-o.getMonth();return a*12+s}const me=null&&he;function pe(e,t){const[n,r]=ce(e,t.start,t.end);return{start:n,end:r}}function ge(e,t){const{start:n,end:r}=pe(t?.in,e);let o=+n>+r;const a=o?+n:+r;const s=o?r:n;s.setHours(0,0,0,0);s.setDate(1);let i=t?.step??1;if(!i)return[];if(i<0){i=-i;o=!o}const c=[];while(+s<=a){c.push(Q(n,s));s.setMonth(s.getMonth()+i)}return o?c.reverse():c}const be=null&&ge;let ye={};function we(){return ye}function ve(e){ye=e}function ke(e,t){const n=we();const r=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??n.weekStartsOn??n.locale?.options?.weekStartsOn??0;const o=J(e,t?.in);const a=o.getDay();const s=(a<r?-7:0)+6-(a-r);o.setDate(o.getDate()+s);o.setHours(23,59,59,999);return o}const Me=null&&ke;function De(e,t){return ke(e,{...t,weekStartsOn:1})}const Oe=null&&De;function Ce(e,t){const n=J(e,t?.in);const r=n.getMonth();n.setFullYear(n.getFullYear(),r+1,0);n.setHours(23,59,59,999);return n}const Ne=null&&Ce;function Te(e,t){const n=J(e,t?.in);const r=n.getFullYear();n.setFullYear(r+1,0,0);n.setHours(23,59,59,999);return n}const We=null&&Te;const Se={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};const xe=(e,t,n)=>{let r;const o=Se[e];if(typeof o==="string"){r=o}else if(t===1){r=o.one}else{r=o.other.replace("{{count}}",t.toString())}if(n?.addSuffix){if(n.comparison&&n.comparison>0){return"in "+r}else{return r+" ago"}}return r};function Ee(e){return(t={})=>{const n=t.width?String(t.width):e.defaultWidth;const r=e.formats[n]||e.formats[e.defaultWidth];return r}}const Ye={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"};const Pe={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"};const _e={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"};const Be={date:Ee({formats:Ye,defaultWidth:"full"}),time:Ee({formats:Pe,defaultWidth:"full"}),dateTime:Ee({formats:_e,defaultWidth:"full"})};const Fe={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};const Ie=(e,t,n,r)=>Fe[e];function Le(e){return(t,n)=>{const r=n?.context?String(n.context):"standalone";let o;if(r==="formatting"&&e.formattingValues){const t=e.defaultFormattingWidth||e.defaultWidth;const r=n?.width?String(n.width):t;o=e.formattingValues[r]||e.formattingValues[t]}else{const t=e.defaultWidth;const r=n?.width?String(n.width):e.defaultWidth;o=e.values[r]||e.values[t]}const a=e.argumentCallback?e.argumentCallback(t):t;return o[a]}}const He={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]};const ze={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]};const Ue={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]};const Ae={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]};const Ze={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}};const qe={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}};const je=(e,t)=>{const n=Number(e);const r=n%100;if(r>20||r<10){switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}}return n+"th"};const $e={ordinalNumber:je,era:Le({values:He,defaultWidth:"wide"}),quarter:Le({values:ze,defaultWidth:"wide",argumentCallback:e=>e-1}),month:Le({values:Ue,defaultWidth:"wide"}),day:Le({values:Ae,defaultWidth:"wide"}),dayPeriod:Le({values:Ze,defaultWidth:"wide",formattingValues:qe,defaultFormattingWidth:"wide"})};function Ge(e){return(t,n={})=>{const r=n.width;const o=r&&e.matchPatterns[r]||e.matchPatterns[e.defaultMatchWidth];const a=t.match(o);if(!a){return null}const s=a[0];const i=r&&e.parsePatterns[r]||e.parsePatterns[e.defaultParseWidth];const c=Array.isArray(i)?Qe(i,(e=>e.test(s))):Xe(i,(e=>e.test(s)));let d;d=e.valueCallback?e.valueCallback(c):c;d=n.valueCallback?n.valueCallback(d):d;const u=t.slice(s.length);return{value:d,rest:u}}}function Xe(e,t){for(const n in e){if(Object.prototype.hasOwnProperty.call(e,n)&&t(e[n])){return n}}return undefined}function Qe(e,t){for(let n=0;n<e.length;n++){if(t(e[n])){return n}}return undefined}function Re(e){return(t,n={})=>{const r=t.match(e.matchPattern);if(!r)return null;const o=r[0];const a=t.match(e.parsePattern);if(!a)return null;let s=e.valueCallback?e.valueCallback(a[0]):a[0];s=n.valueCallback?n.valueCallback(s):s;const i=t.slice(o.length);return{value:s,rest:i}}}const Je=/^(\d+)(th|st|nd|rd)?/i;const Ke=/\d+/i;const Ve={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i};const et={any:[/^b/i,/^(a|c)/i]};const tt={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i};const nt={any:[/1/i,/2/i,/3/i,/4/i]};const rt={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i};const ot={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]};const at={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i};const st={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]};const it={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i};const ct={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}};const dt={ordinalNumber:Re({matchPattern:Je,parsePattern:Ke,valueCallback:e=>parseInt(e,10)}),era:Ge({matchPatterns:Ve,defaultMatchWidth:"wide",parsePatterns:et,defaultParseWidth:"any"}),quarter:Ge({matchPatterns:tt,defaultMatchWidth:"wide",parsePatterns:nt,defaultParseWidth:"any",valueCallback:e=>e+1}),month:Ge({matchPatterns:rt,defaultMatchWidth:"wide",parsePatterns:ot,defaultParseWidth:"any"}),day:Ge({matchPatterns:at,defaultMatchWidth:"wide",parsePatterns:st,defaultParseWidth:"any"}),dayPeriod:Ge({matchPatterns:it,defaultMatchWidth:"any",parsePatterns:ct,defaultParseWidth:"any"})};const ut={code:"en-US",formatDistance:xe,formatLong:Be,formatRelative:Ie,localize:$e,match:dt,options:{weekStartsOn:0,firstWeekContainsDate:1}};const lt=null&&ut;function ft(e,t){const n=J(e,t?.in);n.setFullYear(n.getFullYear(),0,1);n.setHours(0,0,0,0);return n}const ht=null&&ft;function mt(e,t){const n=J(e,t?.in);const r=le(n,ft(n));const o=r+1;return o}const pt=null&&mt;function gt(e,t){const n=we();const r=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??n.weekStartsOn??n.locale?.options?.weekStartsOn??0;const o=J(e,t?.in);const a=o.getDay();const s=(a<r?7:0)+a-r;o.setDate(o.getDate()-s);o.setHours(0,0,0,0);return o}const bt=null&&gt;function yt(e,t){return gt(e,{...t,weekStartsOn:1})}const wt=null&&yt;function vt(e,t){const n=J(e,t?.in);const r=n.getFullYear();const o=Q(n,0);o.setFullYear(r+1,0,4);o.setHours(0,0,0,0);const a=yt(o);const s=Q(n,0);s.setFullYear(r,0,4);s.setHours(0,0,0,0);const i=yt(s);if(n.getTime()>=a.getTime()){return r+1}else if(n.getTime()>=i.getTime()){return r}else{return r-1}}const kt=null&&vt;function Mt(e,t){const n=vt(e,t);const r=Q(t?.in||e,0);r.setFullYear(n,0,4);r.setHours(0,0,0,0);return yt(r)}const Dt=null&&Mt;function Ot(e,t){const n=J(e,t?.in);const r=+yt(n)-+Mt(n);return Math.round(r/S)+1}const Ct=null&&Ot;function Nt(e,t){const n=J(e,t?.in);const r=n.getFullYear();const o=we();const a=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??o.firstWeekContainsDate??o.locale?.options?.firstWeekContainsDate??1;const s=Q(t?.in||e,0);s.setFullYear(r+1,0,a);s.setHours(0,0,0,0);const i=gt(s,t);const c=Q(t?.in||e,0);c.setFullYear(r,0,a);c.setHours(0,0,0,0);const d=gt(c,t);if(+n>=+i){return r+1}else if(+n>=+d){return r}else{return r-1}}const Tt=null&&Nt;function Wt(e,t){const n=we();const r=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??n.firstWeekContainsDate??n.locale?.options?.firstWeekContainsDate??1;const o=Nt(e,t);const a=Q(t?.in||e,0);a.setFullYear(o,0,r);a.setHours(0,0,0,0);const s=gt(a,t);return s}const St=null&&Wt;function xt(e,t){const n=J(e,t?.in);const r=+gt(n,t)-+Wt(n,t);return Math.round(r/S)+1}const Et=null&&xt;function Yt(e,t){const n=e<0?"-":"";const r=Math.abs(e).toString().padStart(t,"0");return n+r}const Pt={y(e,t){const n=e.getFullYear();const r=n>0?n:1-n;return Yt(t==="yy"?r%100:r,t.length)},M(e,t){const n=e.getMonth();return t==="M"?String(n+1):Yt(n+1,2)},d(e,t){return Yt(e.getDate(),t.length)},a(e,t){const n=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];case"aaaa":default:return n==="am"?"a.m.":"p.m."}},h(e,t){return Yt(e.getHours()%12||12,t.length)},H(e,t){return Yt(e.getHours(),t.length)},m(e,t){return Yt(e.getMinutes(),t.length)},s(e,t){return Yt(e.getSeconds(),t.length)},S(e,t){const n=t.length;const r=e.getMilliseconds();const o=Math.trunc(r*Math.pow(10,n-3));return Yt(o,t.length)}};const _t={am:"am",pm:"pm",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"};const Bt={G:function(e,t,n){const r=e.getFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return n.era(r,{width:"abbreviated"});case"GGGGG":return n.era(r,{width:"narrow"});case"GGGG":default:return n.era(r,{width:"wide"})}},y:function(e,t,n){if(t==="yo"){const t=e.getFullYear();const r=t>0?t:1-t;return n.ordinalNumber(r,{unit:"year"})}return Pt.y(e,t)},Y:function(e,t,n,r){const o=Nt(e,r);const a=o>0?o:1-o;if(t==="YY"){const e=a%100;return Yt(e,2)}if(t==="Yo"){return n.ordinalNumber(a,{unit:"year"})}return Yt(a,t.length)},R:function(e,t){const n=vt(e);return Yt(n,t.length)},u:function(e,t){const n=e.getFullYear();return Yt(n,t.length)},Q:function(e,t,n){const r=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(r);case"QQ":return Yt(r,2);case"Qo":return n.ordinalNumber(r,{unit:"quarter"});case"QQQ":return n.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(r,{width:"narrow",context:"formatting"});case"QQQQ":default:return n.quarter(r,{width:"wide",context:"formatting"})}},q:function(e,t,n){const r=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(r);case"qq":return Yt(r,2);case"qo":return n.ordinalNumber(r,{unit:"quarter"});case"qqq":return n.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(r,{width:"narrow",context:"standalone"});case"qqqq":default:return n.quarter(r,{width:"wide",context:"standalone"})}},M:function(e,t,n){const r=e.getMonth();switch(t){case"M":case"MM":return Pt.M(e,t);case"Mo":return n.ordinalNumber(r+1,{unit:"month"});case"MMM":return n.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(r,{width:"narrow",context:"formatting"});case"MMMM":default:return n.month(r,{width:"wide",context:"formatting"})}},L:function(e,t,n){const r=e.getMonth();switch(t){case"L":return String(r+1);case"LL":return Yt(r+1,2);case"Lo":return n.ordinalNumber(r+1,{unit:"month"});case"LLL":return n.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(r,{width:"narrow",context:"standalone"});case"LLLL":default:return n.month(r,{width:"wide",context:"standalone"})}},w:function(e,t,n,r){const o=xt(e,r);if(t==="wo"){return n.ordinalNumber(o,{unit:"week"})}return Yt(o,t.length)},I:function(e,t,n){const r=Ot(e);if(t==="Io"){return n.ordinalNumber(r,{unit:"week"})}return Yt(r,t.length)},d:function(e,t,n){if(t==="do"){return n.ordinalNumber(e.getDate(),{unit:"date"})}return Pt.d(e,t)},D:function(e,t,n){const r=mt(e);if(t==="Do"){return n.ordinalNumber(r,{unit:"dayOfYear"})}return Yt(r,t.length)},E:function(e,t,n){const r=e.getDay();switch(t){case"E":case"EE":case"EEE":return n.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(r,{width:"short",context:"formatting"});case"EEEE":default:return n.day(r,{width:"wide",context:"formatting"})}},e:function(e,t,n,r){const o=e.getDay();const a=(o-r.weekStartsOn+8)%7||7;switch(t){case"e":return String(a);case"ee":return Yt(a,2);case"eo":return n.ordinalNumber(a,{unit:"day"});case"eee":return n.day(o,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(o,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(o,{width:"short",context:"formatting"});case"eeee":default:return n.day(o,{width:"wide",context:"formatting"})}},c:function(e,t,n,r){const o=e.getDay();const a=(o-r.weekStartsOn+8)%7||7;switch(t){case"c":return String(a);case"cc":return Yt(a,t.length);case"co":return n.ordinalNumber(a,{unit:"day"});case"ccc":return n.day(o,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(o,{width:"narrow",context:"standalone"});case"cccccc":return n.day(o,{width:"short",context:"standalone"});case"cccc":default:return n.day(o,{width:"wide",context:"standalone"})}},i:function(e,t,n){const r=e.getDay();const o=r===0?7:r;switch(t){case"i":return String(o);case"ii":return Yt(o,t.length);case"io":return n.ordinalNumber(o,{unit:"day"});case"iii":return n.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(r,{width:"short",context:"formatting"});case"iiii":default:return n.day(r,{width:"wide",context:"formatting"})}},a:function(e,t,n){const r=e.getHours();const o=r/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.dayPeriod(o,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(o,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(o,{width:"narrow",context:"formatting"});case"aaaa":default:return n.dayPeriod(o,{width:"wide",context:"formatting"})}},b:function(e,t,n){const r=e.getHours();let o;if(r===12){o=_t.noon}else if(r===0){o=_t.midnight}else{o=r/12>=1?"pm":"am"}switch(t){case"b":case"bb":return n.dayPeriod(o,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(o,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(o,{width:"narrow",context:"formatting"});case"bbbb":default:return n.dayPeriod(o,{width:"wide",context:"formatting"})}},B:function(e,t,n){const r=e.getHours();let o;if(r>=17){o=_t.evening}else if(r>=12){o=_t.afternoon}else if(r>=4){o=_t.morning}else{o=_t.night}switch(t){case"B":case"BB":case"BBB":return n.dayPeriod(o,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(o,{width:"narrow",context:"formatting"});case"BBBB":default:return n.dayPeriod(o,{width:"wide",context:"formatting"})}},h:function(e,t,n){if(t==="ho"){let t=e.getHours()%12;if(t===0)t=12;return n.ordinalNumber(t,{unit:"hour"})}return Pt.h(e,t)},H:function(e,t,n){if(t==="Ho"){return n.ordinalNumber(e.getHours(),{unit:"hour"})}return Pt.H(e,t)},K:function(e,t,n){const r=e.getHours()%12;if(t==="Ko"){return n.ordinalNumber(r,{unit:"hour"})}return Yt(r,t.length)},k:function(e,t,n){let r=e.getHours();if(r===0)r=24;if(t==="ko"){return n.ordinalNumber(r,{unit:"hour"})}return Yt(r,t.length)},m:function(e,t,n){if(t==="mo"){return n.ordinalNumber(e.getMinutes(),{unit:"minute"})}return Pt.m(e,t)},s:function(e,t,n){if(t==="so"){return n.ordinalNumber(e.getSeconds(),{unit:"second"})}return Pt.s(e,t)},S:function(e,t){return Pt.S(e,t)},X:function(e,t,n){const r=e.getTimezoneOffset();if(r===0){return"Z"}switch(t){case"X":return It(r);case"XXXX":case"XX":return Lt(r);case"XXXXX":case"XXX":default:return Lt(r,":")}},x:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"x":return It(r);case"xxxx":case"xx":return Lt(r);case"xxxxx":case"xxx":default:return Lt(r,":")}},O:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+Ft(r,":");case"OOOO":default:return"GMT"+Lt(r,":")}},z:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+Ft(r,":");case"zzzz":default:return"GMT"+Lt(r,":")}},t:function(e,t,n){const r=Math.trunc(+e/1e3);return Yt(r,t.length)},T:function(e,t,n){return Yt(+e,t.length)}};function Ft(e,t=""){const n=e>0?"-":"+";const r=Math.abs(e);const o=Math.trunc(r/60);const a=r%60;if(a===0){return n+String(o)}return n+String(o)+t+Yt(a,2)}function It(e,t){if(e%60===0){const t=e>0?"-":"+";return t+Yt(Math.abs(e)/60,2)}return Lt(e,t)}function Lt(e,t=""){const n=e>0?"-":"+";const r=Math.abs(e);const o=Yt(Math.trunc(r/60),2);const a=Yt(r%60,2);return n+o+t+a}const Ht=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});case"PPPP":default:return t.date({width:"full"})}};const zt=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});case"pppp":default:return t.time({width:"full"})}};const Ut=(e,t)=>{const n=e.match(/(P+)(p+)?/)||[];const r=n[1];const o=n[2];if(!o){return Ht(e,t)}let a;switch(r){case"P":a=t.dateTime({width:"short"});break;case"PP":a=t.dateTime({width:"medium"});break;case"PPP":a=t.dateTime({width:"long"});break;case"PPPP":default:a=t.dateTime({width:"full"});break}return a.replace("{{date}}",Ht(r,t)).replace("{{time}}",zt(o,t))};const At={p:zt,P:Ut};const Zt=/^D+$/;const qt=/^Y+$/;const jt=["D","DD","YY","YYYY"];function $t(e){return Zt.test(e)}function Gt(e){return qt.test(e)}function Xt(e,t,n){const r=Qt(e,t,n);console.warn(r);if(jt.includes(e))throw new RangeError(r)}function Qt(e,t,n){const r=e[0]==="Y"?"years":"days of the month";return`Use \`${e.toLowerCase()}\` instead of \`${e}\` (in \`${t}\`) for formatting ${r} to the input \`${n}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}function Rt(e){return e instanceof Date||typeof e==="object"&&Object.prototype.toString.call(e)==="[object Date]"}const Jt=null&&Rt;function Kt(e){return!(!Rt(e)&&typeof e!=="number"||isNaN(+J(e)))}const Vt=null&&Kt;const en=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g;const tn=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g;const nn=/^'([^]*?)'?$/;const rn=/''/g;const on=/[a-zA-Z]/;function an(e,t,n){const r=we();const o=n?.locale??r.locale??ut;const a=n?.firstWeekContainsDate??n?.locale?.options?.firstWeekContainsDate??r.firstWeekContainsDate??r.locale?.options?.firstWeekContainsDate??1;const s=n?.weekStartsOn??n?.locale?.options?.weekStartsOn??r.weekStartsOn??r.locale?.options?.weekStartsOn??0;const i=J(e,n?.in);if(!Kt(i)){throw new RangeError("Invalid time value")}let c=t.match(tn).map((e=>{const t=e[0];if(t==="p"||t==="P"){const n=At[t];return n(e,o.formatLong)}return e})).join("").match(en).map((e=>{if(e==="''"){return{isToken:false,value:"'"}}const t=e[0];if(t==="'"){return{isToken:false,value:sn(e)}}if(Bt[t]){return{isToken:true,value:e}}if(t.match(on)){throw new RangeError("Format string contains an unescaped latin alphabet character `"+t+"`")}return{isToken:false,value:e}}));if(o.localize.preprocessor){c=o.localize.preprocessor(i,c)}const d={firstWeekContainsDate:a,weekStartsOn:s,locale:o};return c.map((r=>{if(!r.isToken)return r.value;const a=r.value;if(!n?.useAdditionalWeekYearTokens&&Gt(a)||!n?.useAdditionalDayOfYearTokens&&$t(a)){Xt(a,t,String(e))}const s=Bt[a[0]];return s(i,a,o.localize,d)})).join("")}function sn(e){const t=e.match(nn);if(!t){return e}return t[1].replace(rn,"'")}const cn=null&&an;function dn(e,t){return J(e,t?.in).getMonth()}const un=null&&dn;function ln(e,t){return J(e,t?.in).getFullYear()}const fn=null&&ln;function hn(e,t){return+J(e)>+J(t)}const mn=null&&hn;function pn(e,t){return+J(e)<+J(t)}const gn=null&&pn;function bn(e,t,n){const[r,o]=ce(n?.in,e,t);return+de(r)===+de(o)}const yn=null&&bn;function wn(e,t,n){const[r,o]=ce(n?.in,e,t);return r.getFullYear()===o.getFullYear()&&r.getMonth()===o.getMonth()}const vn=null&&wn;function kn(e,t,n){const[r,o]=ce(n?.in,e,t);return r.getFullYear()===o.getFullYear()}const Mn=null&&kn;function Dn(e,t){let n;let r=t?.in;e.forEach((e=>{if(!r&&typeof e==="object")r=Q.bind(null,e);const t=J(e,r);if(!n||n<t||isNaN(+t))n=t}));return Q(r,n||NaN)}const On=null&&Dn;function Cn(e,t){let n;let r=t?.in;e.forEach((e=>{if(!r&&typeof e==="object")r=Q.bind(null,e);const t=J(e,r);if(!n||n>t||isNaN(+t))n=t}));return Q(r,n||NaN)}const Nn=null&&Cn;function Tn(e,t){const n=J(e,t?.in);const r=n.getFullYear();const o=n.getMonth();const a=Q(n,0);a.setFullYear(r,o+1,0);a.setHours(0,0,0,0);return a.getDate()}const Wn=null&&Tn;function Sn(e,t,n){const r=J(e,n?.in);const o=r.getFullYear();const a=r.getDate();const s=Q(n?.in||e,0);s.setFullYear(o,t,15);s.setHours(0,0,0,0);const i=Tn(s);r.setMonth(t,Math.min(a,i));return r}const xn=null&&Sn;function En(e,t,n){const r=J(e,n?.in);if(isNaN(+r))return Q(n?.in||e,NaN);r.setFullYear(t);return r}const Yn=null&&En;function Pn(e,t){const n=J(e,t?.in);n.setDate(1);n.setHours(0,0,0,0);return n}const _n=null&&Pn;const Bn=5;const Fn=4;function In(e,t){const n=t.startOfMonth(e);const r=n.getDay()>0?n.getDay():7;const o=t.addDays(e,-r+1);const a=t.addDays(o,Bn*7-1);const s=t.getMonth(e)===t.getMonth(a)?Bn:Fn;return s}function Ln(e,t){const n=t.startOfMonth(e);const r=n.getDay();if(r===1){return n}else if(r===0){return t.addDays(n,-1*6)}else{return t.addDays(n,-1*(r-1))}}function Hn(e,t){const n=Ln(e,t);const r=In(e,t);const o=t.addDays(n,r*7-1);return o}class zn{constructor(e,t){this.Date=Date;this.today=()=>{if(this.overrides?.today){return this.overrides.today()}if(this.options.timeZone){return k.tz(this.options.timeZone)}return new this.Date};this.newDate=(e,t,n)=>{if(this.overrides?.newDate){return this.overrides.newDate(e,t,n)}if(this.options.timeZone){return new k(e,t,n,this.options.timeZone)}return new Date(e,t,n)};this.addDays=(e,t)=>this.overrides?.addDays?.(e,t)??V(e,t);this.addMonths=(e,t)=>this.overrides?.addMonths?.(e,t)??te(e,t);this.addWeeks=(e,t)=>this.overrides?.addWeeks?.(e,t)??re(e,t);this.addYears=(e,t)=>this.overrides?.addYears?.(e,t)??ae(e,t);this.differenceInCalendarDays=(e,t)=>this.overrides?.differenceInCalendarDays?.(e,t)??le(e,t);this.differenceInCalendarMonths=(e,t)=>this.overrides?.differenceInCalendarMonths?.(e,t)??he(e,t);this.eachMonthOfInterval=e=>this.overrides?.eachMonthOfInterval?.(e)??ge(e);this.endOfBroadcastWeek=(e,t)=>this.overrides?.endOfBroadcastWeek?.(e,t)??Hn(e,this);this.endOfISOWeek=e=>this.overrides?.endOfISOWeek?.(e)??De(e);this.endOfMonth=e=>this.overrides?.endOfMonth?.(e)??Ce(e);this.endOfWeek=(e,t)=>this.overrides?.endOfWeek?.(e,t??this.options)??ke(e,t??this.options);this.endOfYear=e=>this.overrides?.endOfYear?.(e)??Te(e);this.format=(e,t,n)=>{const r=this.overrides?.format?.(e,t,n??this.options)??an(e,t,n??this.options);if(this.options.numerals&&this.options.numerals!=="latn"){return this.replaceDigits(r)}return r};this.getISOWeek=e=>this.overrides?.getISOWeek?.(e)??Ot(e);this.getMonth=e=>this.overrides?.getMonth?.(e)??dn(e);this.getYear=e=>this.overrides?.getYear?.(e)??ln(e);this.getWeek=(e,t)=>this.overrides?.getWeek?.(e,t??this.options)??xt(e,t??this.options);this.isAfter=(e,t)=>this.overrides?.isAfter?.(e,t)??hn(e,t);this.isBefore=(e,t)=>this.overrides?.isBefore?.(e,t)??pn(e,t);this.isDate=e=>this.overrides?.isDate?.(e)??Rt(e);this.isSameDay=(e,t)=>this.overrides?.isSameDay?.(e,t)??bn(e,t);this.isSameMonth=(e,t)=>this.overrides?.isSameMonth?.(e,t)??wn(e,t);this.isSameYear=(e,t)=>this.overrides?.isSameYear?.(e,t)??kn(e,t);this.max=e=>this.overrides?.max?.(e)??Dn(e);this.min=e=>this.overrides?.min?.(e)??Cn(e);this.setMonth=(e,t)=>this.overrides?.setMonth?.(e,t)??Sn(e,t);this.setYear=(e,t)=>this.overrides?.setYear?.(e,t)??En(e,t);this.startOfBroadcastWeek=(e,t)=>this.overrides?.startOfBroadcastWeek?.(e,t??this)??Ln(e,t??this);this.startOfDay=e=>this.overrides?.startOfDay?.(e)??de(e);this.startOfISOWeek=e=>this.overrides?.startOfISOWeek?.(e)??yt(e);this.startOfMonth=e=>this.overrides?.startOfMonth?.(e)??Pn(e);this.startOfWeek=e=>this.overrides?.startOfWeek?.(e)??gt(e,this.options);this.startOfYear=e=>this.overrides?.startOfYear?.(e)??ft(e);this.options={locale:ut,...e};this.overrides=t}getDigitMap(){const{numerals:e="latn"}=this.options;const t=new Intl.NumberFormat("en-US",{numberingSystem:e});const n={};for(let e=0;e<10;e++){n[e.toString()]=t.format(e)}return n}replaceDigits(e){const t=this.getDigitMap();return e.replace(/\d/g,(e=>t[e]||e))}formatNumber(e){return this.replaceDigits(e.toString())}}const Un=new zn;const An=null&&Un;function Zn(e,t,n={}){const r=Object.entries(e).filter((([,e])=>e===true)).reduce(((e,[r])=>{if(n[r]){e.push(n[r])}else if(t[c[r]]){e.push(t[c[r]])}else if(t[d[r]]){e.push(t[d[r]])}return e}),[t[i.Day]]);return r}function qn(e){return s.createElement("button",{...e})}function jn(e){return s.createElement("span",{...e})}function $n(e){const{size:t=24,orientation:n="left",className:r}=e;return s.createElement("svg",{className:r,width:t,height:t,viewBox:"0 0 24 24"},n==="up"&&s.createElement("polygon",{points:"6.77 17 12.5 11.43 18.24 17 20 15.28 12.5 8 5 15.28"}),n==="down"&&s.createElement("polygon",{points:"6.77 8 12.5 13.57 18.24 8 20 9.72 12.5 17 5 9.72"}),n==="left"&&s.createElement("polygon",{points:"16 18.112 9.81111111 12 16 5.87733333 14.0888889 4 6 12 14.0888889 20"}),n==="right"&&s.createElement("polygon",{points:"8 18.612 14.1888889 12.5 8 6.37733333 9.91111111 4.5 18 12.5 9.91111111 20.5"}))}function Gn(e){const{day:t,modifiers:n,...r}=e;return s.createElement("td",{...r})}function Xn(e){const{day:t,modifiers:n,...r}=e;const o=s.useRef(null);s.useEffect((()=>{if(n.focused)o.current?.focus()}),[n.focused]);return s.createElement("button",{ref:o,...r})}function Qn(e){const{options:t,className:n,components:r,classNames:o,...a}=e;const c=[o[i.Dropdown],n].join(" ");const d=t?.find((({value:e})=>e===a.value));return s.createElement("span",{"data-disabled":a.disabled,className:o[i.DropdownRoot]},s.createElement(r.Select,{className:c,...a},t?.map((({value:e,label:t,disabled:n})=>s.createElement(r.Option,{key:e,value:e,disabled:n},t)))),s.createElement("span",{className:o[i.CaptionLabel],"aria-hidden":true},d?.label,s.createElement(r.Chevron,{orientation:"down",size:18,className:o[i.Chevron]})))}function Rn(e){return s.createElement("div",{...e})}function Jn(e){return s.createElement("div",{...e})}function Kn(e){const{calendarMonth:t,displayIndex:n,...r}=e;return s.createElement("div",{...r},e.children)}function Vn(e){const{calendarMonth:t,displayIndex:n,...r}=e;return s.createElement("div",{...r})}function er(e){return s.createElement("table",{...e})}function tr(e){return s.createElement("div",{...e})}const nr=(0,s.createContext)(undefined);function rr(){const e=(0,s.useContext)(nr);if(e===undefined){throw new Error("useDayPicker() must be used within a custom component.")}return e}function or(e){const{components:t}=rr();return s.createElement(t.Dropdown,{...e})}function ar(e){const{onPreviousClick:t,onNextClick:n,previousMonth:r,nextMonth:o,...a}=e;const{components:c,classNames:d,labels:{labelPrevious:u,labelNext:l}}=rr();return s.createElement("nav",{...a},s.createElement(c.PreviousMonthButton,{type:"button",className:d[i.PreviousMonthButton],tabIndex:r?undefined:-1,disabled:r?undefined:true,"aria-label":u(r),onClick:e.onPreviousClick},s.createElement(c.Chevron,{disabled:r?undefined:true,className:d[i.Chevron],orientation:"left"})),s.createElement(c.NextMonthButton,{type:"button",className:d[i.NextMonthButton],tabIndex:o?undefined:-1,disabled:o?undefined:true,"aria-label":l(o),onClick:e.onNextClick},s.createElement(c.Chevron,{disabled:o?undefined:true,orientation:"right",className:d[i.Chevron]})))}function sr(e){const{components:t}=rr();return s.createElement(t.Button,{...e})}function ir(e){return s.createElement("option",{...e})}function cr(e){const{components:t}=rr();return s.createElement(t.Button,{...e})}function dr(e){return s.createElement("div",{...e})}function ur(e){return s.createElement("select",{...e})}function lr(e){const{week:t,...n}=e;return s.createElement("tr",{...n})}function fr(e){return s.createElement("th",{...e})}function hr(e){return s.createElement("thead",{"aria-hidden":true},s.createElement("tr",{...e}))}function mr(e){const{week:t,...n}=e;return s.createElement("th",{...n})}function pr(e){return s.createElement("th",{...e})}function gr(e){return s.createElement("tbody",{...e})}function br(e){const{components:t}=rr();return s.createElement(t.Dropdown,{...e})}function yr(e){return{...r,...e}}function wr(e){const t={"data-mode":e.mode??undefined,"data-required":"required"in e?e.required:undefined,"data-multiple-months":e.numberOfMonths&&e.numberOfMonths>1||undefined,"data-week-numbers":e.showWeekNumber||undefined,"data-broadcast-calendar":e.broadcastCalendar||undefined};Object.entries(e).forEach((([e,n])=>{if(e.startsWith("data-")){t[e]=n}}));return t}function vr(){const e={};for(const t in i){e[i[t]]=`rdp-${i[t]}`}for(const t in c){e[c[t]]=`rdp-${c[t]}`}for(const t in d){e[d[t]]=`rdp-${d[t]}`}return e}function kr(e,t,n){return(n??new zn(t)).format(e,"LLLL y")}const Mr=kr;function Dr(e,t,n){return(n??new zn(t)).format(e,"d")}function Or(e,t=Un){return t.format(e,"LLLL")}function Cr(e){if(e<10){return`0${e.toLocaleString()}`}return`${e.toLocaleString()}`}function Nr(){return``}function Tr(e,t,n){return(n??new zn(t)).format(e,"cccccc")}function Wr(e,t=Un){return t.format(e,"yyyy")}const Sr=Wr;function xr(e){if(e?.formatMonthCaption&&!e.formatCaption){e.formatCaption=e.formatMonthCaption}if(e?.formatYearCaption&&!e.formatYearDropdown){e.formatYearDropdown=e.formatYearCaption}return{...o,...e}}function Er(e,t,n,r,o){const{startOfMonth:a,startOfYear:s,endOfYear:i,eachMonthOfInterval:c,getMonth:d}=o;const u=c({start:s(e),end:i(e)});const l=u.map((e=>{const s=r.formatMonthDropdown(e,o);const i=d(e);const c=t&&e<a(t)||n&&e>a(n)||false;return{value:i,label:s,disabled:c}}));return l}function Yr(e,t={},n={}){let r={...t?.[i.Day]};Object.entries(e).filter((([,e])=>e===true)).forEach((([e])=>{r={...r,...n?.[e]}}));return r}function Pr(e,t,n){const r=e.today();const o=n?e.startOfBroadcastWeek(r,e):t?e.startOfISOWeek(r):e.startOfWeek(r);const a=[];for(let t=0;t<7;t++){const n=e.addDays(o,t);a.push(n)}return a}function _r(e,t,n,r){if(!e)return undefined;if(!t)return undefined;const{startOfYear:o,endOfYear:a,addYears:s,getYear:i,isBefore:c,isSameYear:d}=r;const u=o(e);const l=a(t);const f=[];let h=u;while(c(h,l)||d(h,l)){f.push(h);h=s(h,1)}return f.map((e=>{const t=n.formatYearDropdown(e,r);return{value:i(e),label:t,disabled:false}}))}function Br(e,t,n){return(n??new zn(t)).format(e,"LLLL y")}const Fr=Br;function Ir(e,t,n,r){let o=(r??new zn(n)).format(e,"PPPP");if(t?.today){o=`Today, ${o}`}return o}function Lr(e,t,n,r){let o=(r??new zn(n)).format(e,"PPPP");if(t.today)o=`Today, ${o}`;if(t.selected)o=`${o}, selected`;return o}const Hr=Lr;function zr(){return""}function Ur(e){return"Choose the Month"}function Ar(e){return"Go to the Next Month"}function Zr(e){return"Go to the Previous Month"}function qr(e,t,n){return(n??new zn(t)).format(e,"cccc")}function jr(e,t){return`Week ${e}`}function $r(e){return"Week Number"}function Gr(e){return"Choose the Year"}function Xr(e,t,n,r){const o=e[0];const a=e[e.length-1];const{ISOWeek:s,fixedWeeks:i,broadcastCalendar:c}=n??{};const{addDays:d,differenceInCalendarDays:u,differenceInCalendarMonths:l,endOfBroadcastWeek:f,endOfISOWeek:h,endOfMonth:m,endOfWeek:p,isAfter:g,startOfBroadcastWeek:b,startOfISOWeek:y,startOfWeek:w}=r;const v=c?b(o,r):s?y(o):w(o);const k=c?f(a,r):s?h(m(a)):p(m(a));const M=u(k,v);const D=l(a,o)+1;const O=[];for(let e=0;e<=M;e++){const n=d(v,e);if(t&&g(n,t)){break}O.push(n)}const C=c?35:42;const N=C*D;if(i&&O.length<N){const e=N-O.length;for(let t=0;t<e;t++){const e=d(O[O.length-1],1);O.push(e)}}return O}function Qr(e){const t=[];return e.reduce(((e,t)=>{const n=[];const r=t.weeks.reduce(((e,t)=>[...e,...t.days]),n);return[...e,...r]}),t)}function Rr(e,t,n,r){const{numberOfMonths:o=1}=n;const a=[];for(let n=0;n<o;n++){const o=r.addMonths(e,n);if(t&&o>t){break}a.push(o)}return a}function Jr(e,t){const{month:n,defaultMonth:r,today:o=t.today(),numberOfMonths:a=1,endMonth:s,startMonth:i}=e;let c=n||r||o;const{differenceInCalendarMonths:d,addMonths:u,startOfMonth:l}=t;if(s&&d(s,c)<0){const e=-1*(a-1);c=u(s,e)}if(i&&d(c,i)<0){c=i}return l(c)}class Kr{constructor(e,t,n=Un){this.date=e;this.displayMonth=t;this.outside=Boolean(t&&!n.isSameMonth(e,t));this.dateLib=n}isEqualTo(e){return this.dateLib.isSameDay(e.date,this.date)&&this.dateLib.isSameMonth(e.displayMonth,this.displayMonth)}}class Vr{constructor(e,t){this.date=e;this.weeks=t}}class eo{constructor(e,t){this.days=t;this.weekNumber=e}}function to(e,t,n,r){const{addDays:o,endOfBroadcastWeek:a,endOfISOWeek:s,endOfMonth:i,endOfWeek:c,getISOWeek:d,getWeek:u,startOfBroadcastWeek:l,startOfISOWeek:f,startOfWeek:h}=r;const m=e.reduce(((e,m)=>{const p=n.broadcastCalendar?l(m,r):n.ISOWeek?f(m):h(m);const g=n.broadcastCalendar?a(m,r):n.ISOWeek?s(i(m)):c(i(m));const b=t.filter((e=>e>=p&&e<=g));const y=n.broadcastCalendar?35:42;if(n.fixedWeeks&&b.length<y){const e=t.filter((e=>{const t=y-b.length;return e>g&&e<=o(g,t)}));b.push(...e)}const w=b.reduce(((e,t)=>{const o=n.ISOWeek?d(t):u(t);const a=e.find((e=>e.weekNumber===o));const s=new Kr(t,m,r);if(!a){e.push(new eo(o,[s]))}else{a.days.push(s)}return e}),[]);const v=new Vr(m,w);e.push(v);return e}),[]);if(!n.reverseMonths){return m}else{return m.reverse()}}function no(e,t){let{startMonth:n,endMonth:r}=e;const{startOfYear:o,startOfDay:a,startOfMonth:s,endOfMonth:i,addYears:c,endOfYear:d,newDate:u,today:l}=t;const{fromYear:f,toYear:h,fromMonth:m,toMonth:p}=e;if(!n&&m){n=m}if(!n&&f){n=t.newDate(f,0,1)}if(!r&&p){r=p}if(!r&&h){r=u(h,11,31)}const g=e.captionLayout==="dropdown"||e.captionLayout==="dropdown-years";if(n){n=s(n)}else if(f){n=u(f,0,1)}else if(!n&&g){n=o(c(e.today??l(),-100))}if(r){r=i(r)}else if(h){r=u(h,11,31)}else if(!r&&g){r=d(e.today??l())}return[n?a(n):n,r?a(r):r]}function ro(e,t,n,r){if(n.disableNavigation){return undefined}const{pagedNavigation:o,numberOfMonths:a=1}=n;const{startOfMonth:s,addMonths:i,differenceInCalendarMonths:c}=r;const d=o?a:1;const u=s(e);if(!t){return i(u,d)}const l=c(t,e);if(l<a){return undefined}return i(u,d)}function oo(e,t,n,r){if(n.disableNavigation){return undefined}const{pagedNavigation:o,numberOfMonths:a}=n;const{startOfMonth:s,addMonths:i,differenceInCalendarMonths:c}=r;const d=o?a??1:1;const u=s(e);if(!t){return i(u,-d)}const l=c(u,t);if(l<=0){return undefined}return i(u,-d)}function ao(e){const t=[];return e.reduce(((e,t)=>[...e,...t.weeks]),t)}function so(e,t){const[n,r]=(0,s.useState)(e);const o=t===undefined?n:t;return[o,r]}function io(e,t){const[n,r]=no(e,t);const{startOfMonth:o,endOfMonth:a}=t;const i=Jr(e,t);const[c,d]=so(i,e.month?i:undefined);(0,s.useEffect)((()=>{const n=Jr(e,t);d(n)}),[e.timeZone]);const u=Rr(c,r,e,t);const l=Xr(u,e.endMonth?a(e.endMonth):undefined,e,t);const f=to(u,l,e,t);const h=ao(f);const m=Qr(f);const p=oo(c,n,e,t);const g=ro(c,r,e,t);const{disableNavigation:b,onMonthChange:y}=e;const w=e=>h.some((t=>t.days.some((t=>t.isEqualTo(e)))));const v=e=>{if(b){return}let t=o(e);if(n&&t<o(n)){t=o(n)}if(r&&t>o(r)){t=o(r)}d(t);y?.(t)};const k=e=>{if(w(e)){return}v(e.date)};const M={months:f,weeks:h,days:m,navStart:n,navEnd:r,previousMonth:p,nextMonth:g,goToMonth:v,goToDay:k};return M}function co(e,t,n,r){let o;let a=0;let s=false;while(a<e.length&&!s){const i=e[a];const d=t(i);if(!d[c.disabled]&&!d[c.hidden]&&!d[c.outside]){if(d[c.focused]){o=i;s=true}else if(r?.isEqualTo(i)){o=i;s=true}else if(n(i.date)){o=i;s=true}else if(d[c.today]){o=i;s=true}}a++}if(!o){o=e.find((e=>{const n=t(e);return!n[c.disabled]&&!n[c.hidden]&&!n[c.outside]}))}return o}function uo(e,t,n=false,r=Un){let{from:o,to:a}=e;const{differenceInCalendarDays:s,isSameDay:i}=r;if(o&&a){const e=s(a,o)<0;if(e){[o,a]=[a,o]}const r=s(t,o)>=(n?1:0)&&s(a,t)>=(n?1:0);return r}if(!n&&a){return i(a,t)}if(!n&&o){return i(o,t)}return false}const lo=(e,t)=>uo(e,t,false,defaultDateLib);function fo(e){return Boolean(e&&typeof e==="object"&&"before"in e&&"after"in e)}function ho(e){return Boolean(e&&typeof e==="object"&&"from"in e)}function mo(e){return Boolean(e&&typeof e==="object"&&"after"in e)}function po(e){return Boolean(e&&typeof e==="object"&&"before"in e)}function go(e){return Boolean(e&&typeof e==="object"&&"dayOfWeek"in e)}function bo(e,t){return Array.isArray(e)&&e.every(t.isDate)}function yo(e,t,n=Un){const r=!Array.isArray(t)?[t]:t;const{isSameDay:o,differenceInCalendarDays:a,isAfter:s}=n;return r.some((t=>{if(typeof t==="boolean"){return t}if(n.isDate(t)){return o(e,t)}if(bo(t,n)){return t.includes(e)}if(ho(t)){return uo(t,e,false,n)}if(go(t)){if(!Array.isArray(t.dayOfWeek)){return t.dayOfWeek===e.getDay()}return t.dayOfWeek.includes(e.getDay())}if(fo(t)){const n=a(t.before,e);const r=a(t.after,e);const o=n>0;const i=r<0;const c=s(t.before,t.after);if(c){return i&&o}else{return o||i}}if(mo(t)){return a(e,t.after)>0}if(po(t)){return a(t.before,e)>0}if(typeof t==="function"){return t(e)}return false}))}const wo=null&&yo;function vo(e,t,n,r,o,a,s){const{ISOWeek:i,broadcastCalendar:c}=a;const{addDays:d,addMonths:u,addWeeks:l,addYears:f,endOfBroadcastWeek:h,endOfISOWeek:m,endOfWeek:p,max:g,min:b,startOfBroadcastWeek:y,startOfISOWeek:w,startOfWeek:v}=s;const k={day:d,week:l,month:u,year:f,startOfWeek:e=>c?y(e,s):i?w(e):v(e),endOfWeek:e=>c?h(e,s):i?m(e):p(e)};let M=k[e](n,t==="after"?1:-1);if(t==="before"&&r){M=g([r,M])}else if(t==="after"&&o){M=b([o,M])}return M}function ko(e,t,n,r,o,a,s,i=0){if(i>365){return undefined}const c=vo(e,t,n.date,r,o,a,s);const d=Boolean(a.disabled&&yo(c,a.disabled,s));const u=Boolean(a.hidden&&yo(c,a.hidden,s));const l=c;const f=new Kr(c,l,s);if(!d&&!u){return f}return ko(e,t,f,r,o,a,s,i+1)}function Mo(e,t,n,r,o){const{autoFocus:a}=e;const[i,c]=(0,s.useState)();const d=co(t.days,n,r||(()=>false),i);const[u,l]=(0,s.useState)(a?d:undefined);const f=()=>{c(u);l(undefined)};const h=(n,r)=>{if(!u)return;const a=ko(n,r,u,t.navStart,t.navEnd,e,o);if(!a)return;t.goToDay(a);l(a)};const m=e=>Boolean(d?.isEqualTo(e));const p={isFocusTarget:m,setFocused:l,focused:u,blur:f,moveFocus:h};return p}function Do(e,t,n){const{disabled:r,hidden:o,modifiers:a,showOutsideDays:s,broadcastCalendar:i,today:d}=t;const{isSameDay:u,isSameMonth:l,startOfMonth:f,isBefore:h,endOfMonth:m,isAfter:p}=n;const g=t.startMonth&&f(t.startMonth);const b=t.endMonth&&m(t.endMonth);const y={[c.focused]:[],[c.outside]:[],[c.disabled]:[],[c.hidden]:[],[c.today]:[]};const w={};for(const t of e){const{date:e,displayMonth:c}=t;const f=Boolean(c&&!l(e,c));const m=Boolean(g&&h(e,g));const v=Boolean(b&&p(e,b));const k=Boolean(r&&yo(e,r,n));const M=Boolean(o&&yo(e,o,n))||m||v||!i&&!s&&f||i&&s===false&&f;const D=u(e,d??n.today());if(f)y.outside.push(t);if(k)y.disabled.push(t);if(M)y.hidden.push(t);if(D)y.today.push(t);if(a){Object.keys(a).forEach((r=>{const o=a?.[r];const s=o?yo(e,o,n):false;if(!s)return;if(w[r]){w[r].push(t)}else{w[r]=[t]}}))}}return e=>{const t={[c.focused]:false,[c.disabled]:false,[c.hidden]:false,[c.outside]:false,[c.today]:false};const n={};for(const n in y){const r=y[n];t[n]=r.some((t=>t===e))}for(const t in w){n[t]=w[t].some((t=>t===e))}return{...t,...n}}}function Oo(e,t){const{selected:n,required:r,onSelect:o}=e;const[a,s]=so(n,o?n:undefined);const i=!o?a:n;const{isSameDay:c}=t;const d=e=>i?.some((t=>c(t,e)))??false;const{min:u,max:l}=e;const f=(e,t,n)=>{let a=[...i??[]];if(d(e)){if(i?.length===u){return}if(r&&i?.length===1){return}a=i?.filter((t=>!c(t,e)))}else{if(i?.length===l){a=[e]}else{a=[...a,e]}}if(!o){s(a)}o?.(a,e,t,n);return a};return{selected:i,select:f,isSelected:d}}function Co(e,t,n=0,r=0,o=false,a=Un){const{from:s,to:i}=t||{};const{isSameDay:c,isAfter:d,isBefore:u}=a;let l;if(!s&&!i){l={from:e,to:n>0?undefined:e}}else if(s&&!i){if(c(s,e)){if(o){l={from:s,to:undefined}}else{l=undefined}}else if(u(e,s)){l={from:e,to:s}}else{l={from:s,to:e}}}else if(s&&i){if(c(s,e)&&c(i,e)){if(o){l={from:s,to:i}}else{l=undefined}}else if(c(s,e)){l={from:s,to:n>0?undefined:e}}else if(c(i,e)){l={from:e,to:n>0?undefined:e}}else if(u(e,s)){l={from:e,to:i}}else if(d(e,s)){l={from:s,to:e}}else if(d(e,i)){l={from:s,to:e}}else{throw new Error("Invalid range")}}if(l?.from&&l?.to){const t=a.differenceInCalendarDays(l.to,l.from);if(r>0&&t>r){l={from:e,to:undefined}}else if(n>1&&t<n){l={from:e,to:undefined}}}return l}function No(e,t,n=Un){const r=!Array.isArray(t)?[t]:t;let o=e.from;const a=n.differenceInCalendarDays(e.to,e.from);const s=Math.min(a,6);for(let e=0;e<=s;e++){if(r.includes(o.getDay())){return true}o=n.addDays(o,1)}return false}function To(e,t,n=Un){return uo(e,t.from,false,n)||uo(e,t.to,false,n)||uo(t,e.from,false,n)||uo(t,e.to,false,n)}function Wo(e,t,n=Un){const r=Array.isArray(t)?t:[t];const o=r.filter((e=>typeof e!=="function"));const a=o.some((t=>{if(typeof t==="boolean")return t;if(n.isDate(t)){return uo(e,t,false,n)}if(bo(t,n)){return t.some((t=>uo(e,t,false,n)))}if(ho(t)){if(t.from&&t.to){return To(e,{from:t.from,to:t.to},n)}return false}if(go(t)){return No(e,t.dayOfWeek,n)}if(fo(t)){const r=n.isAfter(t.before,t.after);if(r){return To(e,{from:n.addDays(t.after,1),to:n.addDays(t.before,-1)},n)}return yo(e.from,t,n)||yo(e.to,t,n)}if(mo(t)||po(t)){return yo(e.from,t,n)||yo(e.to,t,n)}return false}));if(a){return true}const s=r.filter((e=>typeof e==="function"));if(s.length){let t=e.from;const r=n.differenceInCalendarDays(e.to,e.from);for(let e=0;e<=r;e++){if(s.some((e=>e(t)))){return true}t=n.addDays(t,1)}}return false}function So(e,t){const{disabled:n,excludeDisabled:r,selected:o,required:a,onSelect:s}=e;const[i,c]=so(o,s?o:undefined);const d=!s?i:o;const u=e=>d&&uo(d,e,false,t);const l=(o,i,u)=>{const{min:l,max:f}=e;const h=o?Co(o,d,l,f,a,t):undefined;if(r&&n&&h?.from&&h.to){if(Wo({from:h.from,to:h.to},n,t)){h.from=o;h.to=undefined}}if(!s){c(h)}s?.(h,o,i,u);return h};return{selected:d,select:l,isSelected:u}}function xo(e,t){const{selected:n,required:r,onSelect:o}=e;const[a,s]=so(n,o?n:undefined);const i=!o?a:n;const{isSameDay:c}=t;const d=e=>i?c(i,e):false;const u=(e,t,n)=>{let a=e;if(!r&&i&&i&&c(e,i)){a=undefined}if(!o){s(a)}if(r){o?.(a,e,t,n)}else{o?.(a,e,t,n)}return a};return{selected:i,select:u,isSelected:d}}function Eo(e,t){const n=xo(e,t);const r=Oo(e,t);const o=So(e,t);switch(e.mode){case"single":return n;case"multiple":return r;case"range":return o;default:return undefined}}function Yo(e){const{components:t,formatters:n,labels:r,dateLib:o,locale:u,classNames:l}=(0,s.useMemo)((()=>{const t={...ut,...e.locale};const n=new zn({locale:t,weekStartsOn:e.broadcastCalendar?1:e.weekStartsOn,firstWeekContainsDate:e.firstWeekContainsDate,useAdditionalWeekYearTokens:e.useAdditionalWeekYearTokens,useAdditionalDayOfYearTokens:e.useAdditionalDayOfYearTokens,timeZone:e.timeZone,numerals:e.numerals},e.dateLib);return{dateLib:n,components:yr(e.components),formatters:xr(e.formatters),labels:{...a,...e.labels},locale:t,classNames:{...vr(),...e.classNames}}}),[e.locale,e.broadcastCalendar,e.weekStartsOn,e.firstWeekContainsDate,e.useAdditionalWeekYearTokens,e.useAdditionalDayOfYearTokens,e.timeZone,e.numerals,e.dateLib,e.components,e.formatters,e.labels,e.classNames]);const{captionLayout:f,mode:h,onDayBlur:m,onDayClick:p,onDayFocus:g,onDayKeyDown:b,onDayMouseEnter:y,onDayMouseLeave:w,onNextClick:v,onPrevClick:k,showWeekNumber:M,styles:D}=e;const{formatCaption:O,formatDay:C,formatMonthDropdown:N,formatWeekNumber:T,formatWeekNumberHeader:W,formatWeekdayName:S,formatYearDropdown:x}=n;const E=io(e,o);const{days:Y,months:P,navStart:_,navEnd:B,previousMonth:F,nextMonth:I,goToMonth:L}=E;const H=Do(Y,e,o);const{isSelected:z,select:U,selected:A}=Eo(e,o)??{};const{blur:Z,focused:q,isFocusTarget:j,moveFocus:$,setFocused:G}=Mo(e,E,H,z??(()=>false),o);const{labelDayButton:X,labelGridcell:Q,labelGrid:R,labelMonthDropdown:J,labelNav:K,labelWeekday:V,labelWeekNumber:ee,labelWeekNumberHeader:te,labelYearDropdown:ne}=r;const re=(0,s.useMemo)((()=>Pr(o,e.ISOWeek)),[o,e.ISOWeek]);const oe=h!==undefined||p!==undefined;const ae=(0,s.useCallback)((()=>{if(!F)return;L(F);k?.(F)}),[F,L,k]);const se=(0,s.useCallback)((()=>{if(!I)return;L(I);v?.(I)}),[L,I,v]);const ie=(0,s.useCallback)(((e,t)=>n=>{n.preventDefault();n.stopPropagation();G(e);U?.(e.date,t,n);p?.(e.date,t,n)}),[U,p,G]);const ce=(0,s.useCallback)(((e,t)=>n=>{G(e);g?.(e.date,t,n)}),[g,G]);const de=(0,s.useCallback)(((e,t)=>n=>{Z();m?.(e.date,t,n)}),[Z,m]);const ue=(0,s.useCallback)(((t,n)=>r=>{const o={ArrowLeft:["day",e.dir==="rtl"?"after":"before"],ArrowRight:["day",e.dir==="rtl"?"before":"after"],ArrowDown:["week","after"],ArrowUp:["week","before"],PageUp:[r.shiftKey?"year":"month","before"],PageDown:[r.shiftKey?"year":"month","after"],Home:["startOfWeek","before"],End:["endOfWeek","after"]};if(o[r.key]){r.preventDefault();r.stopPropagation();const[e,t]=o[r.key];$(e,t)}b?.(t.date,n,r)}),[$,b,e.dir]);const le=(0,s.useCallback)(((e,t)=>n=>{y?.(e.date,t,n)}),[y]);const fe=(0,s.useCallback)(((e,t)=>n=>{w?.(e.date,t,n)}),[w]);const he=(0,s.useCallback)((e=>t=>{const n=Number(t.target.value);const r=o.setMonth(o.startOfMonth(e),n);L(r)}),[o,L]);const me=(0,s.useCallback)((e=>t=>{const n=Number(t.target.value);const r=o.setYear(o.startOfMonth(e),n);L(r)}),[o,L]);const{className:pe,style:ge}=(0,s.useMemo)((()=>({className:[l[i.Root],e.className].filter(Boolean).join(" "),style:{...D?.[i.Root],...e.style}})),[l,e.className,e.style,D]);const be=wr(e);const ye={dayPickerProps:e,selected:A,select:U,isSelected:z,months:P,nextMonth:I,previousMonth:F,goToMonth:L,getModifiers:H,components:t,classNames:l,styles:D,labels:r,formatters:n};return s.createElement(nr.Provider,{value:ye},s.createElement(t.Root,{className:pe,style:ge,dir:e.dir,id:e.id,lang:e.lang,nonce:e.nonce,title:e.title,role:e.role,"aria-label":e["aria-label"],...be},s.createElement(t.Months,{className:l[i.Months],style:D?.[i.Months]},!e.hideNavigation&&s.createElement(t.Nav,{className:l[i.Nav],style:D?.[i.Nav],"aria-label":K(),onPreviousClick:ae,onNextClick:se,previousMonth:F,nextMonth:I}),P.map(((r,a)=>{const m=Er(r.date,_,B,n,o);const p=_r(_,B,n,o);return s.createElement(t.Month,{className:l[i.Month],style:D?.[i.Month],key:a,displayIndex:a,calendarMonth:r},s.createElement(t.MonthCaption,{className:l[i.MonthCaption],style:D?.[i.MonthCaption],calendarMonth:r,displayIndex:a},f?.startsWith("dropdown")?s.createElement(t.DropdownNav,{className:l[i.Dropdowns],style:D?.[i.Dropdowns]},f==="dropdown"||f==="dropdown-months"?s.createElement(t.MonthsDropdown,{className:l[i.MonthsDropdown],"aria-label":J(),classNames:l,components:t,disabled:Boolean(e.disableNavigation),onChange:he(r.date),options:m,style:D?.[i.Dropdown],value:o.getMonth(r.date)}):s.createElement("span",{role:"status","aria-live":"polite"},N(r.date,o)),f==="dropdown"||f==="dropdown-years"?s.createElement(t.YearsDropdown,{className:l[i.YearsDropdown],"aria-label":ne(o.options),classNames:l,components:t,disabled:Boolean(e.disableNavigation),onChange:me(r.date),options:p,style:D?.[i.Dropdown],value:o.getYear(r.date)}):s.createElement("span",{role:"status","aria-live":"polite"},x(r.date,o))):s.createElement(t.CaptionLabel,{className:l[i.CaptionLabel],role:"status","aria-live":"polite"},O(r.date,o.options,o))),s.createElement(t.MonthGrid,{role:"grid","aria-multiselectable":h==="multiple"||h==="range","aria-label":R(r.date,o.options,o)||undefined,className:l[i.MonthGrid],style:D?.[i.MonthGrid]},!e.hideWeekdays&&s.createElement(t.Weekdays,{className:l[i.Weekdays],style:D?.[i.Weekdays]},M&&s.createElement(t.WeekNumberHeader,{"aria-label":te(o.options),className:l[i.WeekNumberHeader],style:D?.[i.WeekNumberHeader],scope:"col"},W()),re.map(((e,n)=>s.createElement(t.Weekday,{"aria-label":V(e,o.options,o),className:l[i.Weekday],key:n,style:D?.[i.Weekday],scope:"col"},S(e,o.options,o))))),s.createElement(t.Weeks,{className:l[i.Weeks],style:D?.[i.Weeks]},r.weeks.map(((n,r)=>s.createElement(t.Week,{className:l[i.Week],key:n.weekNumber,style:D?.[i.Week],week:n},M&&s.createElement(t.WeekNumber,{week:n,style:D?.[i.WeekNumber],"aria-label":ee(n.weekNumber,{locale:u}),className:l[i.WeekNumber],scope:"row",role:"rowheader"},T(n.weekNumber)),n.days.map((n=>{const{date:r}=n;const a=H(n);a[c.focused]=!a.hidden&&Boolean(q?.isEqualTo(n));a[d.selected]=!a.disabled&&(z?.(r)||a.selected);if(ho(A)){const{from:e,to:t}=A;a[d.range_start]=Boolean(e&&t&&o.isSameDay(r,e));a[d.range_end]=Boolean(e&&t&&o.isSameDay(r,t));a[d.range_middle]=uo(A,r,true,o)}const u=Yr(a,D,e.modifiersStyles);const f=Zn(a,l,e.modifiersClassNames);const h=!oe&&!a.hidden?Q(r,a,o.options,o):undefined;return s.createElement(t.Day,{key:`${o.format(r,"yyyy-MM-dd")}_${o.format(n.displayMonth,"yyyy-MM")}`,day:n,modifiers:a,className:f.join(" "),style:u,role:"gridcell","aria-selected":a.selected||undefined,"aria-label":h,"data-day":o.format(r,"yyyy-MM-dd"),"data-month":n.outside?o.format(r,"yyyy-MM"):undefined,"data-selected":a.selected||undefined,"data-disabled":a.disabled||undefined,"data-hidden":a.hidden||undefined,"data-outside":n.outside||undefined,"data-focused":a.focused||undefined,"data-today":a.today||undefined},!a.hidden&&oe?s.createElement(t.DayButton,{className:l[i.DayButton],style:D?.[i.DayButton],type:"button",day:n,modifiers:a,disabled:a.disabled||undefined,tabIndex:j(n)?0:-1,"aria-label":X(r,a,o.options,o),onClick:ie(n,a),onBlur:de(n,a),onFocus:ce(n,a),onKeyDown:ue(n,a),onMouseEnter:le(n,a),onMouseLeave:fe(n,a)},C(r,o.options,o)):!a.hidden&&C(n.date,o.options,o))}))))))))}))),e.footer&&s.createElement(t.Footer,{className:l[i.Footer],style:D?.[i.Footer],role:"status","aria-live":"polite"},e.footer)))}const Po=null&&MonthCaption;const _o=null&&Week;const Bo=null&&useDayPicker}}]);