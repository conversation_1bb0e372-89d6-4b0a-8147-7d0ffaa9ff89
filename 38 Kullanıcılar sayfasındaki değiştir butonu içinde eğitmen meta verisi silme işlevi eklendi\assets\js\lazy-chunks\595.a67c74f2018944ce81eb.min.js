"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[595],{5409:(t,e,r)=>{r.r(e);r.d(e,{default:()=>ur});var n=r(917);var o=r(202);var i=r(2141);var a=r(8003);var l=r(7363);var u=r(7536);var c=r(9546);var s=r(6595);var d=r(3946);var f=r(1820);var p=r(3882);var v=36e5;function m(t,e){(0,p.Z)(2,arguments);var r=(0,d.Z)(e);return(0,f.Z)(t,r*v)}var h=r(2274);var b=r(313);var y=r(3855);var _=r(9013);function g(t){(0,p.Z)(1,arguments);var e=(0,_["default"])(t);e.setHours(0,0,0,0);return e}var Z=r(8717);function w(t,e){(0,p.Z)(2,arguments);var r=(0,Z.Z)(t);var n=(0,Z.Z)(e);return r.getTime()===n.getTime()}var S=r(74);var O=r(8507);var x=r(2798);var j=r(5519);var D=r(7941);var P=r(4857);var E=r(830);var W=r(9447);var A=r(8305);var C=r(6413);var T=r(1537);var k=r(5460);var I=r(4900);var L=r(125);var N=r(5219);var Q=r(9169);var F=r(7363);function q(){q=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return q.apply(this,arguments)}function M(t,e){return U(t)||z(t,e)||V(t,e)||G()}function G(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function V(t,e){if(!t)return;if(typeof t==="string")return B(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return B(t,e)}function B(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function z(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,l=[],u=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(l.push(n.value),l.length!==e);u=!0);}catch(t){c=!0,o=t}finally{try{if(!u&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return l}}function U(t){if(Array.isArray(t))return t}function J(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var R=!!A.y.tutor_pro_url;var Y=function t(){var e,r,o,i,d,f,p,v;var _=(0,u.Gc)();var Z=(0,u.qo)({name:"post_date"});var T=(e=(0,u.qo)({name:"schedule_date"}))!==null&&e!==void 0?e:"";var k=(r=(0,u.qo)({name:"schedule_time"}))!==null&&r!==void 0?r:(0,c["default"])(m(new Date,1),C.E_.hoursMinutes);var G=(o=(0,u.qo)({name:"isScheduleEnabled"}))!==null&&o!==void 0?o:false;var V=(i=(0,u.qo)({name:"showScheduleForm"}))!==null&&i!==void 0?i:false;var B=(d=(0,u.qo)({name:"enable_coming_soon"}))!==null&&d!==void 0?d:false;var z=(f=(0,u.qo)({name:"course_enrollment_period"}))!==null&&f!==void 0?f:false;var U=(p=(0,u.qo)({name:"enrollment_starts_date"}))!==null&&p!==void 0?p:"";var J=(v=(0,u.qo)({name:"enrollment_starts_time"}))!==null&&v!==void 0?v:"";var Y=(0,u.qo)({name:"coming_soon_thumbnail"});var $=(0,l.useState)(T&&k&&(0,h["default"])(new Date("".concat(T," ").concat(k)))?(0,c["default"])(new Date("".concat(T," ").concat(k)),C.E_.yearMonthDayHourMinuteSecond24H):""),K=M($,2),X=K[0],tt=K[1];var et=new Date("".concat(U," ").concat(J));var rt=function t(){_.setValue("schedule_date","",{shouldDirty:true});_.setValue("schedule_time","",{shouldDirty:true});_.setValue("showScheduleForm",true,{shouldDirty:true})};var nt=function t(){var e=(0,b["default"])(new Date(Z),new Date);_.setValue("schedule_date",e&&X?(0,c["default"])((0,y["default"])(X),C.E_.yearMonthDay):"",{shouldDirty:true});_.setValue("schedule_time",e&&X?(0,c["default"])((0,y["default"])(X),C.E_.hoursMinutes):"",{shouldDirty:true})};var ot=function t(){if(!T||!k){return}_.setValue("showScheduleForm",false,{shouldDirty:true});tt((0,c["default"])(new Date("".concat(T," ").concat(k)),C.E_.yearMonthDayHourMinuteSecond24H))};(0,l.useEffect)((function(){if(G&&V){_.setFocus("schedule_date")}}),[V,G]);return(0,n.tZ)("div",{css:H.scheduleOptions},(0,n.tZ)(u.Qr,{name:"isScheduleEnabled",control:_.control,render:function t(e){return(0,n.tZ)(E.Z,q({},e,{label:(0,a.__)("Schedule","tutor"),onChange:function t(e){if(!e&&T&&k){_.setValue("showScheduleForm",false,{shouldDirty:true})}}}))}}),G&&V&&(0,n.tZ)("div",{css:H.formWrapper},(0,n.tZ)("div",{css:L.i.dateAndTimeWrapper},(0,n.tZ)(u.Qr,{name:"schedule_date",control:_.control,rules:{required:(0,a.__)("Schedule date is required.","tutor"),validate:{invalidDateRule:Q.Ek,futureDate:function t(e){if((0,b["default"])(new Date("".concat(e)),g(new Date))){return(0,a.__)("Schedule date should be in the future.","tutor")}return true},isBeforeEnrollmentStartDate:function t(e){if(z&&(0,b["default"])(et,new Date("".concat(e," ").concat(k)))){return(0,a.__)("Schedule date should be before enrollment start date.","tutor")}return true}},deps:["enrollment_starts_date","enrollment_starts_time","schedule_time"]},render:function t(e){return(0,n.tZ)(D.Z,q({},e,{isClearable:false,placeholder:(0,a.__)("Select date","tutor"),disabledBefore:(0,c["default"])(new Date,C.E_.yearMonthDay),onChange:function t(){_.setFocus("schedule_time")},dateFormat:C.E_.monthDayYear}))}}),(0,n.tZ)(u.Qr,{name:"schedule_time",control:_.control,rules:{required:(0,a.__)("Schedule time is required.","tutor"),validate:{invalidTimeRule:Q.xB,futureDate:function t(e){if((0,b["default"])(new Date("".concat(T," ").concat(e)),new Date)){return(0,a.__)("Schedule time should be in the future.","tutor")}return true},isBeforeEnrollmentStartDate:function t(e){if(z&&(0,b["default"])(et,new Date("".concat(T," ").concat(e)))){return(0,a.__)("Schedule time should be before enrollment start date.","tutor")}return true}},deps:["schedule_date","enrollment_starts_date","enrollment_starts_time"]},render:function t(e){return(0,n.tZ)(W.Z,q({},e,{interval:60,isClearable:false,placeholder:"hh:mm A"}))}})),(0,n.tZ)(u.Qr,{name:"enable_coming_soon",control:_.control,render:function t(e){return(0,n.tZ)(j.Z,q({},e,{label:(0,n.tZ)(F.Fragment,null,(0,a.__)("Show coming soon in course list & details page","tutor"),(0,n.tZ)(I.Z,{when:!R},(0,n.tZ)("div",{"data-pro-badge":true},(0,n.tZ)(x.Z,{content:(0,a.__)("Pro","tutor"),size:"small"})))),disabled:!R,labelCss:H.checkboxStartAlign}))}}),(0,n.tZ)(I.Z,{when:R},(0,n.tZ)(I.Z,{when:B},(0,n.tZ)(u.Qr,{name:"coming_soon_thumbnail",control:_.control,render:function t(e){return(0,n.tZ)(P.Z,q({},e,{label:(0,a.__)("Coming Soon Thumbnail","tutor"),buttonText:(0,a.__)("Upload Thumbnail","tutor"),infoText:(0,a.sprintf)((0,a.__)("JPEG, PNG, GIF, and WebP formats, up to %s","tutor"),A.y.max_upload_size)}))}}),(0,n.tZ)(u.Qr,{name:"enable_curriculum_preview",control:_.control,render:function t(e){return(0,n.tZ)(j.Z,q({},e,{label:(0,a.__)("Preview Course Curriculum","tutor")}))}}))),(0,n.tZ)("div",{css:H.scheduleButtonsWrapper},(0,n.tZ)(S.Z,{variant:"tertiary",size:"small",onClick:nt,disabled:!T&&!k||(0,h["default"])(new Date("".concat(T," ").concat(k)))&&w(new Date("".concat(T," ").concat(k)),new Date(X))},(0,a.__)("Cancel","tutor")),(0,n.tZ)(S.Z,{variant:"secondary",size:"small",onClick:_.handleSubmit(ot),disabled:!T||!k},(0,a.__)("Ok","tutor")))),G&&!V&&(0,n.tZ)("div",{css:H.scheduleInfoWrapper},(0,n.tZ)("div",{css:H.scheduledFor},(0,n.tZ)("div",{css:H.scheduleLabel},!B?(0,a.__)("Scheduled for","tutor"):(0,a.__)("Scheduled with coming soon","tutor")),(0,n.tZ)("div",{css:H.scheduleInfoButtons},(0,n.tZ)("button",{type:"button",css:L.i.actionButton,onClick:rt},(0,n.tZ)(s.Z,{name:"delete",width:24,height:24})),(0,n.tZ)("button",{type:"button",css:L.i.actionButton,onClick:function t(){_.setValue("showScheduleForm",true,{shouldDirty:true})}},(0,n.tZ)(s.Z,{name:"edit",width:24,height:24})))),(0,n.tZ)(I.Z,{when:T&&k&&(0,h["default"])(new Date("".concat(T," ").concat(k)))},(0,n.tZ)("div",{css:H.scheduleInfo},(0,a.sprintf)((0,a.__)("%s at %s","tutor"),(0,c["default"])((0,y["default"])(T),C.E_.monthDayYear),k)),(0,n.tZ)(I.Z,{when:Y===null||Y===void 0?void 0:Y.url},(0,n.tZ)(O.Z,{value:Y,uploadHandler:N.ZT,clearHandler:N.ZT,disabled:true})))))};const $=Y;var H={scheduleOptions:(0,n.iv)("padding:",T.W0[12],";border:1px solid ",T.Jv.stroke["default"],";border-radius:",T.E0[8],";gap:",T.W0[8],";background-color:",T.Jv.bg.white,";"+(true?"":0),true?"":0),formWrapper:(0,n.iv)(L.i.display.flex("column"),";gap:",T.W0[8],";margin-top:",T.W0[16],";"+(true?"":0),true?"":0),scheduleButtonsWrapper:(0,n.iv)("display:flex;gap:",T.W0[12],";margin-top:",T.W0[8],";button{width:100%;span{justify-content:center;}}"+(true?"":0),true?"":0),scheduleInfoWrapper:(0,n.iv)("display:flex;flex-direction:column;gap:",T.W0[8],";margin-top:",T.W0[12],";"+(true?"":0),true?"":0),scheduledFor:true?{name:"bcffy2",styles:"display:flex;align-items:center;justify-content:space-between"}:0,scheduleLabel:(0,n.iv)(k.c.caption(),";color:",T.Jv.text.subdued,";"+(true?"":0),true?"":0),scheduleInfoButtons:(0,n.iv)("display:flex;align-items:center;gap:",T.W0[8],";"+(true?"":0),true?"":0),scheduleInfo:(0,n.iv)(k.c.caption(),";background-color:",T.Jv.background.status.processing,";padding:",T.W0[8],";border-radius:",T.E0[4],";text-align:center;"+(true?"":0),true?"":0),checkboxStartAlign:(0,n.iv)("span:first-of-type{gap:",T.W0[4],";align-self:flex-start;margin-top:",T.W0[4],";}[data-pro-badge]{display:inline-flex;vertical-align:middle;padding-left:",T.W0[4],";}"+(true?"":0),true?"":0)};var K=r(1487);var X=r(9768);var tt=r(8777);var et=r(6932);var rt=r(2739);var nt=r(4436);var ot=r(7034);var it=r(6375);var at=r(9250);var lt=r(1162);var ut=r(1961);var ct=r(551);var st=r(6873);var dt=r(2556);var ft=r(7151);var pt=r(7363);function vt(t){"@babel/helpers - typeof";return vt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},vt(t)}function mt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ht(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?mt(Object(r),!0).forEach((function(e){bt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):mt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function bt(t,e,r){e=yt(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function yt(t){var e=_t(t,"string");return vt(e)==="symbol"?e:String(e)}function _t(t,e){if(vt(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(vt(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function gt(){gt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return gt.apply(this,arguments)}function Zt(t){return xt(t)||Ot(t)||St(t)||wt()}function wt(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function St(t,e){if(!t)return;if(typeof t==="string")return jt(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return jt(t,e)}function Ot(t){if(typeof Symbol!=="undefined"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function xt(t){if(Array.isArray(t))return jt(t)}function jt(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Dt=(0,ot.zs)();var Pt=function t(){var e,r,c,s,d,f,p,v,m,h,b;var y=(0,u.Gc)();var _=(0,o.NL)();var g=(0,i.y)({queryKey:["CourseDetails",Dt]});var Z=(0,at.s0)();var w=(0,at.TH)(),S=w.state;var O=(0,u.qo)({control:y.control,name:"course_price_type"});var x=(0,u.qo)({control:y.control,name:"course_product_id"});var j=(0,u.qo)({control:y.control,name:"course_selling_option"});var D=_.getQueryData(["CourseDetails",Dt]);var P=A.y.tutor_currency;var E=!!A.y.tutor_pro_url;var W=((e=A.y.settings)===null||e===void 0?void 0:e.monetize_by)==="wc"||((r=A.y.settings)===null||r===void 0?void 0:r.monetize_by)==="tutor"||((c=A.y.settings)===null||c===void 0?void 0:c.monetize_by)==="edd"?[{label:(0,a.__)("Free","tutor"),value:"free"},{label:(0,a.__)("Paid","tutor"),value:"paid"}]:[{label:(0,a.__)("Free","tutor"),value:"free"}];var T=[{label:(0,a.__)("One-time purchase only","tutor"),value:"one_time"},{label:(0,a.__)("Subscription only","tutor"),value:"subscription"},{label:(0,a.__)("Subscription & one-time purchase","tutor"),value:"both"},{label:(0,a.__)("Membership only","tutor"),value:"membership"},{label:(0,a.__)("All","tutor"),value:"all"}];var k=(0,st.ni)((s=A.y.settings)===null||s===void 0?void 0:s.monetize_by,Dt?String(Dt):"");var F=(0,st.vG)(x,String(Dt),O,E?(d=A.y.settings)===null||d===void 0?void 0:d.monetize_by:undefined);var q=function t(e){var r;if(!e||!e.length){return[]}var n=D||{},o=n.course_pricing;var i=o!==null&&o!==void 0&&o.product_id&&o.product_id!=="0"&&o.product_name?{label:o.product_name||"",value:String(o.product_id)}:null;var a=(r=e.map((function(t){var e=t.post_title,r=t.ID;return{label:e,value:String(r)}})))!==null&&r!==void 0?r:[];var l=[i].concat(Zt(a)).filter(ft.$K);var u=Array.from(new Map(l.map((function(t){return[t.value,t]}))).values());return u};(0,l.useEffect)((function(){if(k.isSuccess&&k.data){var t;var e=D||{},r=e.course_pricing;if(((t=A.y.settings)===null||t===void 0?void 0:t.monetize_by)==="wc"&&r!==null&&r!==void 0&&r.product_id&&r.product_id!=="0"&&!q(k.data).find((function(t){var e=t.value;return String(e)===String(r.product_id)}))){y.setValue("course_product_id","",{shouldValidate:true})}}}),[k.data]);(0,l.useEffect)((function(){var t;if(!A.y.edd_products||!A.y.edd_products.length){return}var e=D||{},r=e.course_pricing;if(((t=A.y.settings)===null||t===void 0?void 0:t.monetize_by)==="edd"&&r!==null&&r!==void 0&&r.product_id&&r.product_id!=="0"&&!A.y.edd_products.find((function(t){var e=t.ID;return String(e)===String(r.product_id)}))){y.setValue("course_product_id","",{shouldValidate:true})}}),[A.y.edd_products]);(0,l.useEffect)((function(){var t;if(((t=A.y.settings)===null||t===void 0?void 0:t.monetize_by)!=="wc"){return}if(F.isSuccess&&F.data){if(S!==null&&S!==void 0&&S.isError){Z(ct.L.CourseBasics.buildLink(),{state:{isError:false}});return}y.setValue("course_price",F.data.regular_price||"0",{shouldValidate:true});y.setValue("course_sale_price",F.data.sale_price||"0",{shouldValidate:true});return}var e=y.formState.dirtyFields.course_price;var r=y.formState.dirtyFields.course_sale_price;if(!e){y.setValue("course_price","0")}if(!r){y.setValue("course_sale_price","0")}}),[F.data]);return(0,n.tZ)(pt.Fragment,null,(0,n.tZ)(u.Qr,{name:"course_price_type",control:y.control,render:function t(e){return(0,n.tZ)(ut.Z,gt({},e,{label:(0,a.__)("Pricing Model","tutor"),options:W,wrapperCss:Wt.priceRadioGroup}))}}),(0,n.tZ)(I.Z,{when:(0,N.ro)(C.AO.SUBSCRIPTION)&&((f=A.y.settings)===null||f===void 0?void 0:f.monetize_by)==="tutor"&&O==="paid"},(0,n.tZ)(u.Qr,{name:"course_selling_option",control:y.control,render:function t(e){return(0,n.tZ)(tt.Z,gt({},e,{label:(0,a.__)("Purchase Options","tutor"),options:T}))}})),(0,n.tZ)(I.Z,{when:O==="paid"&&((p=A.y.settings)===null||p===void 0?void 0:p.monetize_by)==="wc"},(0,n.tZ)(u.Qr,{name:"course_product_id",control:y.control,render:function t(e){return(0,n.tZ)(tt.Z,gt({},e,{label:(0,a.__)("Select product","tutor"),placeholder:(0,a.__)("Select a product","tutor"),options:[{label:(0,a.__)("Select a product","tutor"),value:"-1"}].concat(Zt(q(k.data))),helpText:(0,a.sprintf)((0,a.__)("You can select an existing WooCommerce product%s","tutor"),E?", alternatively, a new WooCommerce product will be created for you.":"."),isSearchable:true,loading:k.isLoading&&!e.field.value,isClearable:true}))}})),(0,n.tZ)(I.Z,{when:O==="paid"&&((v=A.y.settings)===null||v===void 0?void 0:v.monetize_by)==="edd"},(0,n.tZ)(u.Qr,{name:"course_product_id",control:y.control,rules:ht({},(0,Q.n0)()),render:function t(e){return(0,n.tZ)(tt.Z,gt({},e,{label:(0,a.__)("Select product","tutor"),placeholder:(0,a.__)("Select a product","tutor"),options:A.y.edd_products?A.y.edd_products.map((function(t){return{label:t.post_title,value:String(t.ID)}})):[],helpText:(0,a.__)("Sell your product, process by EDD","tutor"),isSearchable:true,loading:!!g&&!e.field.value}))}})),(0,n.tZ)(I.Z,{when:O==="paid"&&!["subscription","membership"].includes(j)&&(((m=A.y.settings)===null||m===void 0?void 0:m.monetize_by)==="tutor"||E&&((h=A.y.settings)===null||h===void 0?void 0:h.monetize_by)==="wc"&&x!=="-1")},(0,n.tZ)("div",{css:Wt.coursePriceWrapper},(0,n.tZ)(u.Qr,{name:"course_price",control:y.control,rules:ht(ht({},(0,Q.n0)()),{},{validate:function t(e){if(Number(e)<=0){return(0,a.__)("Price must be greater than 0","tutor")}return true}}),render:function t(e){return(0,n.tZ)(lt.Z,gt({},e,{label:(0,a.__)("Regular Price","tutor"),content:(P===null||P===void 0?void 0:P.symbol)||"$",placeholder:(0,a.__)("0","tutor"),type:"number",loading:!!g&&!e.field.value,selectOnFocus:true,contentCss:L.i.inputCurrencyStyle}))}}),(0,n.tZ)(u.Qr,{name:"course_sale_price",control:y.control,rules:{validate:function t(e){if(!e){return true}var r=y.getValues("course_price");if(Number(e)>=Number(r)){return(0,a.__)("Sale price must be less than regular price","tutor")}return true}},render:function t(e){return(0,n.tZ)(lt.Z,gt({},e,{label:(0,a.__)("Sale Price","tutor"),content:(P===null||P===void 0?void 0:P.symbol)||"$",placeholder:(0,a.__)("0","tutor"),type:"number",loading:!!g&&!e.field.value,selectOnFocus:true,contentCss:L.i.inputCurrencyStyle}))}}))),(0,n.tZ)(I.Z,{when:(0,N.ro)(C.AO.SUBSCRIPTION)&&((b=A.y.settings)===null||b===void 0?void 0:b.monetize_by)==="tutor"&&O==="paid"},(0,n.tZ)(I.Z,{when:!["one_time","membership"].includes(j)},(0,n.tZ)(dt.Z,{courseId:Dt}))))};const Et=Pt;var Wt={priceRadioGroup:(0,n.iv)("display:flex;align-items:center;gap:",T.W0[36],";"+(true?"":0),true?"":0),coursePriceWrapper:(0,n.iv)("display:flex;align-items:flex-start;gap:",T.W0[16],";"+(true?"":0),true?"":0)};function At(){At=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return At.apply(this,arguments)}function Ct(t){return It(t)||kt(t)||Qt(t)||Tt()}function Tt(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function kt(t){if(typeof Symbol!=="undefined"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function It(t){if(Array.isArray(t))return Ft(t)}function Lt(t,e){return Mt(t)||qt(t,e)||Qt(t,e)||Nt()}function Nt(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Qt(t,e){if(!t)return;if(typeof t==="string")return Ft(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ft(t,e)}function Ft(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function qt(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,l=[],u=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(l.push(n.value),l.length!==e);u=!0);}catch(t){c=!0,o=t}finally{try{if(!u&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return l}}function Mt(t){if(Array.isArray(t))return t}var Gt=(0,ot.zs)();var Vt=function t(){var e,r,d,f;var p=(0,u.Gc)();var v=(0,o.NL)();var m=(0,i.y)({queryKey:["CourseDetails",Gt]});var h=(0,l.useState)(""),b=Lt(h,2),y=b[0],_=b[1];var g=v.getQueryData(["CourseDetails",Gt]);var Z=A.y.current_user;var w=(0,N.ro)(C.AO.TUTOR_MULTI_INSTRUCTORS);var S=!!A.y.tutor_pro_url;var O=((e=A.y.settings)===null||e===void 0?void 0:e.chatgpt_enable)==="on";var x=((r=A.y.settings)===null||r===void 0?void 0:r.instructor_can_change_course_author)!=="off";var j=((d=A.y.settings)===null||d===void 0?void 0:d.instructor_can_manage_co_instructors)!=="off";var D=String(Z.data.id)===String((g===null||g===void 0?void 0:g.post_author.ID)||"");var E=Z.roles.includes(C.er.ADMINISTRATOR);var W=((g===null||g===void 0?void 0:g.course_instructors)||[]).find((function(t){return String(t.id)===String(Z.data.id)}));var T=(0,N.ro)(C.AO.SUBSCRIPTION)&&((f=A.y.settings)===null||f===void 0?void 0:f.membership_only_mode);var k=p.watch("post_author");var L=S&&w&&(E||W&&j);var Q=E||D&&x;var F=(0,u.qo)({control:p.control,name:"visibility"});var q=[{label:(0,a.__)("Public","tutor"),value:"publish"},{label:(0,a.__)("Password Protected","tutor"),value:"password_protected"},{label:(0,a.__)("Private","tutor"),value:"private"}];var M=(0,it.V)(y);var G=(0,it.r)(String(Gt),w);var V=((g===null||g===void 0?void 0:g.course_instructors)||[]).map((function(t){return{id:t.id,name:t.display_name,email:t.user_email,avatar_url:t.avatar_url}}));var B=[].concat(Ct(V),Ct(G.data||[])).filter((function(t){return String(t.id)!==String(k===null||k===void 0?void 0:k.id)}));return(0,n.tZ)("div",{css:zt.sidebar},(0,n.tZ)("div",{css:zt.statusAndDate},(0,n.tZ)(u.Qr,{name:"visibility",control:p.control,render:function t(e){return(0,n.tZ)(tt.Z,At({},e,{label:(0,a.__)("Visibility","tutor"),placeholder:(0,a.__)("Select visibility status","tutor"),options:q,leftIcon:(0,n.tZ)(s.Z,{name:"eye",width:32,height:32}),loading:!!m&&!e.field.value,onChange:function t(){p.setValue("post_password","")}}))}}),(0,n.tZ)(I.Z,{when:g===null||g===void 0?void 0:g.post_modified},(function(t){return(0,n.tZ)("div",{css:zt.updatedOn},(0,a.sprintf)((0,a.__)("Last updated on %s","tutor"),(0,c["default"])(new Date(t),C.E_.dayMonthYear)||""))}))),(0,n.tZ)(I.Z,{when:F==="password_protected"},(0,n.tZ)(u.Qr,{name:"post_password",control:p.control,rules:{required:(0,a.__)("Password is required","tutor")},render:function t(e){return(0,n.tZ)(X.Z,At({},e,{label:(0,a.__)("Password","tutor"),placeholder:(0,a.__)("Enter password","tutor"),type:"password",isPassword:true,selectOnFocus:true,loading:!!m&&!e.field.value}))}})),(0,n.tZ)($,null),(0,n.tZ)(u.Qr,{name:"thumbnail",control:p.control,render:function t(e){return(0,n.tZ)(P.Z,At({},e,{label:(0,a.__)("Featured Image","tutor"),buttonText:(0,a.__)("Upload Thumbnail","tutor"),infoText:(0,a.sprintf)((0,a.__)("JPEG, PNG, GIF, and WebP formats, up to %s","tutor"),A.y.max_upload_size),generateWithAi:!S||O,loading:!!m&&!e.field.value}))}}),(0,n.tZ)(u.Qr,{name:"video",control:p.control,render:function t(e){return(0,n.tZ)(nt.Z,At({},e,{label:(0,a.__)("Intro Video","tutor"),buttonText:(0,a.__)("Upload Video","tutor"),infoText:(0,a.sprintf)((0,a.__)("MP4, and WebM formats, up to %s","tutor"),A.y.max_upload_size),loading:!!m&&!e.field.value}))}}),(0,n.tZ)(I.Z,{when:!T},(0,n.tZ)(Et,null)),(0,n.tZ)(u.Qr,{name:"course_categories",control:p.control,defaultValue:[],render:function t(e){return(0,n.tZ)(K.Z,At({},e,{label:(0,a.__)("Categories","tutor")}))}}),(0,n.tZ)(u.Qr,{name:"course_tags",control:p.control,render:function t(e){return(0,n.tZ)(rt.Z,At({},e,{label:(0,a.__)("Tags","tutor"),placeholder:(0,a.__)("Add tags","tutor")}))}}),(0,n.tZ)(u.Qr,{name:"post_author",control:p.control,render:function t(e){var r,o;return(0,n.tZ)(et.Z,At({},e,{label:(0,a.__)("Author","tutor"),options:(r=(o=M.data)===null||o===void 0?void 0:o.map((function(t){return{id:t.id,name:t.name||"",email:t.email||"",avatar_url:t.avatar_url||""}})))!==null&&r!==void 0?r:[],placeholder:(0,a.__)("Search to add author","tutor"),isSearchable:true,disabled:!Q,loading:M.isLoading,onChange:function t(){var e=g===null||g===void 0?void 0:g.post_author;var r=p.getValues("course_instructors");var n=!!r.find((function(t){return String(t.id)===String(e===null||e===void 0?void 0:e.ID)}));var o={id:Number(e===null||e===void 0?void 0:e.ID),name:e===null||e===void 0?void 0:e.display_name,email:e.user_email,avatar_url:e===null||e===void 0?void 0:e.tutor_profile_photo_url,isRemoveAble:String(e===null||e===void 0?void 0:e.ID)!==String(Z.data.id)};var i=n?r:[].concat(Ct(r),[o]);p.setValue("course_instructors",i)},handleSearchOnChange:function t(e){_(e)}}))}}),(0,n.tZ)(I.Z,{when:L},(0,n.tZ)(u.Qr,{name:"course_instructors",control:p.control,render:function t(e){return(0,n.tZ)(et.Z,At({},e,{label:(0,a.__)("Instructors","tutor"),options:B,placeholder:(0,a.__)("Search to add instructor","tutor"),isSearchable:true,isMultiSelect:true,loading:G.isLoading&&!e.field.value,emptyStateText:(0,a.__)("No instructors added.","tutor"),isInstructorMode:true}))}})))};const Bt=Vt;var zt={sidebar:(0,n.iv)("border-left:1px solid ",T.Jv.stroke.divider,";min-height:calc(100vh - ",T.J9,"px);padding-left:",T.W0[32],";padding-block:",T.W0[24],";display:flex;flex-direction:column;gap:",T.W0[16],";",T.Uo.smallTablet,"{border-left:none;border-top:1px solid ",T.Jv.stroke.divider,";padding-block:",T.W0[16],";padding-left:0;}"+(true?"":0),true?"":0),statusAndDate:(0,n.iv)(L.i.display.flex("column"),";gap:",T.W0[4],";"+(true?"":0),true?"":0),updatedOn:(0,n.iv)(k.c.caption(),";color:",T.Jv.text.hints,";"+(true?"":0),true?"":0),priceRadioGroup:(0,n.iv)("display:flex;align-items:center;gap:",T.W0[36],";"+(true?"":0),true?"":0),coursePriceWrapper:(0,n.iv)("display:flex;align-items:flex-start;gap:",T.W0[16],";"+(true?"":0),true?"":0)};var Ut=r(6895);var Jt=r(6848);function Rt(){Rt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Rt.apply(this,arguments)}function Yt(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var $t=true?{name:"zmwa79",styles:"align-items:start;span{top:3px;}"}:0;var Ht=function t(){var e=(0,u.Gc)();var r=[{label:(0,a.__)("Schedule course content by date","tutor"),value:"unlock_by_date"},{label:(0,a.__)("Content available after X days from enrollment","tutor"),value:"specific_days"},{label:(0,a.__)("Course content available sequentially","tutor"),value:"unlock_sequentially"},{label:(0,a.__)("Course content unlocked after finishing prerequisites","tutor"),value:"after_finishing_prerequisites",labelCss:$t},{label:(0,a.__)("None","tutor"),value:""}];if(!A.y.tutor_pro_url){return(0,n.tZ)("div",{css:Xt.dripNoProWrapper},(0,n.tZ)(s.Z,{name:"crown",width:72,height:72}),(0,n.tZ)("h6",{css:k.c.body("medium")},(0,a.__)("Content Drip is a pro feature","tutor")),(0,n.tZ)("p",{css:Xt.dripNoProDescription},(0,a.__)("You can schedule your course content using  content drip options","tutor")),(0,n.tZ)(S.Z,{icon:(0,n.tZ)(s.Z,{name:"crown",width:24,height:24}),onClick:function t(){window.open(A.Z.TUTOR_PRICING_PAGE,"_blank","noopener")}},(0,a.__)("Get Tutor LMS Pro","tutor")))}if(!(0,N.ro)(C.AO.CONTENT_DRIP)){return(0,n.tZ)("div",{css:Xt.dripNoProWrapper},(0,n.tZ)(s.Z,{name:"contentDrip",width:72,height:72,style:Xt.dripIcon}),(0,n.tZ)("h6",{css:k.c.body("medium")},(0,a.__)("Activate the “Content Drip” addon to use this feature.","tutor")),(0,n.tZ)("p",{css:Xt.dripNoProDescription},(0,a.__)("Control when students can access lessons and quizzes using the Content Drip feature.","tutor")),(0,n.tZ)(S.Z,{variant:"secondary",icon:(0,n.tZ)(s.Z,{name:"linkExternal",width:24,height:24}),onClick:function t(){window.open(A.Z.TUTOR_ADDONS_PAGE,"_blank","noopener")}},(0,a.__)("Enable Content Drip Addon","tutor")))}return(0,n.tZ)("div",{css:Xt.dripWrapper},(0,n.tZ)("h6",{css:Xt.dripTitle},(0,a.__)("Content Drip Type","tutor")),(0,n.tZ)("p",{css:Xt.dripSubTitle},(0,a.__)("You can schedule your course content using one of the following Content Drip options","tutor")),(0,n.tZ)(u.Qr,{name:"contentDripType",control:e.control,render:function t(e){return(0,n.tZ)(ut.Z,Rt({},e,{options:r,wrapperCss:Xt.radioWrapper}))}}))};const Kt=Ht;var Xt={dripWrapper:(0,n.iv)("background-color:",T.Jv.background.white,";padding:",T.W0[16]," ",T.W0[24]," ",T.W0[32]," ",T.W0[32],";min-height:400px;",T.Uo.smallMobile,"{padding:",T.W0[16],";}"+(true?"":0),true?"":0),dripTitle:(0,n.iv)(k.c.body("medium"),";margin-bottom:",T.W0[4],";"+(true?"":0),true?"":0),dripSubTitle:(0,n.iv)(k.c.small(),";color:",T.Jv.text.hints,";margin-bottom:",T.W0[16],";"+(true?"":0),true?"":0),radioWrapper:(0,n.iv)("display:flex;flex-direction:column;gap:",T.W0[8],";"+(true?"":0),true?"":0),dripNoProWrapper:(0,n.iv)("min-height:400px;background:",T.Jv.background.white,";display:flex;flex-direction:column;align-items:center;justify-content:center;gap:",T.W0[4],";padding:",T.W0[24],";text-align:center;"+(true?"":0),true?"":0),dripNoProDescription:(0,n.iv)(k.c.caption(),";color:",T.Jv.text.subdued,";max-width:320px;margin:0 auto ",T.W0[12],";"+(true?"":0),true?"":0),dripIcon:(0,n.iv)("color:",T.Jv.icon.brand,";"+(true?"":0),true?"":0)};function te(t){"@babel/helpers - typeof";return te="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},te(t)}function ee(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function re(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ee(Object(r),!0).forEach((function(e){ne(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ee(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function ne(t,e,r){e=oe(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function oe(t){var e=ie(t,"string");return te(e)==="symbol"?e:String(e)}function ie(t,e){if(te(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(te(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function ae(){ae=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return ae.apply(this,arguments)}function le(t,e){return fe(t)||de(t,e)||ce(t,e)||ue()}function ue(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function ce(t,e){if(!t)return;if(typeof t==="string")return se(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return se(t,e)}function se(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function de(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,l=[],u=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(l.push(n.value),l.length!==e);u=!0);}catch(t){c=!0,o=t}finally{try{if(!u&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return l}}function fe(t){if(Array.isArray(t))return t}function pe(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var ve=(0,ot.zs)();var me=function t(){var e,r;var o=!!A.y.tutor_pro_url;var c=(0,u.Gc)();var s=(0,i.y)({queryKey:["CourseDetails",ve]});var d=(0,u.qo)({control:c.control,name:"course_enrollment_period"});var f=(0,u.qo)({control:c.control,name:"enrollment_starts_date"});var p=(0,u.qo)({control:c.control,name:"enrollment_starts_time"});var v=(0,u.qo)({control:c.control,name:"enrollment_ends_date"});var m=(0,u.qo)({control:c.control,name:"isScheduleEnabled"});var h=(0,u.qo)({control:c.control,name:"schedule_date"});var y=(0,u.qo)({control:c.control,name:"schedule_time"});var _=(0,l.useState)(false),Z=le(_,2),w=Z[0],O=Z[1];var x=(0,N.ro)(C.AO.SUBSCRIPTION)&&((e=A.y.settings)===null||e===void 0?void 0:e.membership_only_mode);var P=(0,N.ro)(C.AO.ENROLLMENT);var T=new Date("".concat(h," ").concat(y));return(0,n.tZ)("div",{css:be.wrapper},(0,n.tZ)(u.Qr,{name:"maximum_students",control:c.control,render:function t(e){return(0,n.tZ)(X.Z,ae({},e,{label:(0,a.__)("Maximum Student","tutor"),helpText:(0,a.__)("Number of students that can enrol in this course. Set 0 for no limits.","tutor"),placeholder:"0",type:"number",isClearable:true,selectOnFocus:true,loading:!!s&&!e.field.value}))}}),(0,n.tZ)(I.Z,{when:o&&P},(0,n.tZ)(I.Z,{when:!x&&((r=A.y.settings)===null||r===void 0?void 0:r.enrollment_expiry_enabled)==="on"},(0,n.tZ)(u.Qr,{name:"enrollment_expiry",control:c.control,render:function t(e){return(0,n.tZ)(X.Z,ae({},e,{label:(0,a.__)("Enrollment Expiration","tutor"),helpText:(0,a.__)("Student's enrollment will be removed after this number of days. Set 0 for lifetime enrollment.","tutor"),placeholder:"0",type:"number",isClearable:true,selectOnFocus:true,loading:!!s&&!e.field.value}))}})),(0,n.tZ)("div",{css:be.enrollmentPeriod({isEnabled:d})},(0,n.tZ)(u.Qr,{name:"course_enrollment_period",control:c.control,render:function t(e){return(0,n.tZ)(E.Z,ae({},e,{label:(0,a.__)("Course Enrollment Period","tutor"),loading:!!s&&!e.field.value}))}}),(0,n.tZ)(I.Z,{when:d},(0,n.tZ)("div",{css:be.enrollmentDateWrapper},(0,n.tZ)("div",{css:be.enrollmentDate},(0,n.tZ)("label",{htmlFor:"enrollment_starts_at"},(0,a.__)("Start Date","tutor")),(0,n.tZ)("div",{id:"enrollment_starts_at",css:L.i.dateAndTimeWrapper},(0,n.tZ)(u.Qr,{name:"enrollment_starts_date",control:c.control,rules:re(re({},Q.n0),{},{validate:{invalidDate:Q.Ek,isAfterScheduleDate:function t(e){if(m&&T&&(0,b["default"])(g(new Date(e)),g(new Date(h)))){return(0,a.__)("Start date should be after the schedule date","tutor")}}},deps:["schedule_date","schedule_time","enrollment_ends_date","enrollment_ends_time"]}),render:function t(e){return(0,n.tZ)(D.Z,ae({},e,{loading:!!s&&!e.field.value,placeholder:(0,a.__)("Start Date","tutor"),dateFormat:C.E_.monthDayYear}))}}),(0,n.tZ)(u.Qr,{name:"enrollment_starts_time",control:c.control,rules:re(re({},Q.n0),{},{validate:{invalidTime:Q.xB,isAfterScheduleTime:function t(e){if(m&&T&&(0,b["default"])(new Date("".concat(f," ").concat(e)),T)){return(0,a.__)("Start time should be after the schedule time","tutor")}}},deps:["schedule_date","schedule_time","enrollment_starts_date","enrollment_ends_date"]}),render:function t(e){return(0,n.tZ)(W.Z,ae({},e,{loading:!!s&&!e.field.value,placeholder:(0,a.__)("hh:mm a","tutor")}))}}))),(0,n.tZ)(I.Z,{when:w||v,fallback:(0,n.tZ)("div",null,(0,n.tZ)(S.Z,{variant:"secondary",size:"small",onClick:function t(){return O(true)},disabled:!!s||!f||!p},(0,a.__)("Add End Date","tutor")))},(0,n.tZ)("div",{css:be.enrollmentDate},(0,n.tZ)("label",{htmlFor:"enrollment_ends_at"},(0,n.tZ)("span",null,(0,a.__)("End Date","tutor")),(0,n.tZ)(S.Z,{variant:"text",size:"small",onClick:function t(){O(false);c.setValue("enrollment_ends_date","");c.setValue("enrollment_ends_time","")},css:be.removeButton},(0,a.__)("Remove","tutor"))),(0,n.tZ)("div",{id:"enrollment_ends_at",css:L.i.dateAndTimeWrapper},(0,n.tZ)(u.Qr,{name:"enrollment_ends_date",control:c.control,rules:re(re({},Q.n0),{},{validate:{invalidDate:Q.Ek,checkEndDate:function t(e){if((0,b["default"])(g(new Date(e)),g(new Date(f)))){return(0,a.__)("End date should be after the start date","tutor")}}},deps:["enrollment_starts_date","enrollment_starts_time"]}),render:function t(e){return(0,n.tZ)(D.Z,ae({},e,{loading:!!s&&!e.field.value,placeholder:(0,a.__)("End Date","tutor"),disabledBefore:f,dateFormat:C.E_.monthDayYear}))}}),(0,n.tZ)(u.Qr,{name:"enrollment_ends_time",control:c.control,rules:re(re({},Q.n0),{},{validate:{invalidTime:Q.xB,checkEndTime:function t(e){if(f&&v&&p&&!(0,b["default"])(new Date("".concat(f," ").concat(p)),new Date("".concat(v," ").concat(e)))){return(0,a.__)("End time should be after the start time","tutor")}}},deps:["enrollment_starts_date","enrollment_starts_time","enrollment_ends_date"]}),render:function t(e){return(0,n.tZ)(W.Z,ae({},e,{loading:!!s&&!e.field.value,placeholder:(0,a.__)("hh:mm a","tutor")}))}}))))))),(0,n.tZ)(u.Qr,{name:"pause_enrollment",control:c.control,render:function t(e){return(0,n.tZ)(j.Z,ae({},e,{label:(0,a.__)("Pause Enrollment","tutor"),description:(0,a.__)("If you pause enrolment, students will no longer be able to enroll in the course.","tutor")}))}})))};const he=me;var be={wrapper:(0,n.iv)(L.i.display.flex("column"),";gap:",T.W0[16],";background-color:",T.Jv.background.white,";padding:",T.W0[16]," ",T.W0[24]," ",T.W0[32]," ",T.W0[32],";min-height:400px;",T.Uo.smallMobile,"{padding:",T.W0[16],";}"+(true?"":0),true?"":0),enrollmentPeriod:function t(e){var r=e.isEnabled,o=r===void 0?false:r;return(0,n.iv)("padding:",T.W0[12],";border:1px solid ",T.Jv.stroke["default"],";border-radius:",T.E0[8],";background-color:",T.Jv.bg.white,";",o&&(0,n.iv)("padding-bottom:",T.W0[16],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},enrollmentDateWrapper:(0,n.iv)(L.i.display.flex("column"),";gap:",T.W0[8],";margin-top:",T.W0[16],";"+(true?"":0),true?"":0),enrollmentDate:(0,n.iv)(L.i.display.flex("column"),";gap:",T.W0[4],";label{",L.i.display.flex(),";align-items:center;justify-content:space-between;",k.c.caption(),";color:",T.Jv.text.title,";}"+(true?"":0),true?"":0),removeButton:true?{name:"14nlu69",styles:"margin-left:auto;padding:0"}:0};function ye(t){"@babel/helpers - typeof";return ye="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ye(t)}function _e(){_e=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return _e.apply(this,arguments)}function ge(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Ze(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ge(Object(r),!0).forEach((function(e){we(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ge(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function we(t,e,r){e=Se(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Se(t){var e=Oe(t,"string");return ye(e)==="symbol"?e:String(e)}function Oe(t,e){if(ye(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(ye(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function xe(t,e){return We(t)||Ee(t,e)||De(t,e)||je()}function je(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function De(t,e){if(!t)return;if(typeof t==="string")return Pe(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Pe(t,e)}function Pe(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Ee(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,l=[],u=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(l.push(n.value),l.length!==e);u=!0);}catch(t){c=!0,o=t}finally{try{if(!u&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return l}}function We(t){if(Array.isArray(t))return t}function Ae(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Ce=(0,ot.zs)();var Te=true?{name:"1vqsuxb",styles:"button{min-width:auto;}"}:0;var ke=function t(){var e;var r=(0,u.Gc)();var o=(0,l.useState)("general"),c=xe(o,2),d=c[0],f=c[1];var p=(0,i.y)({queryKey:["CourseDetails",Ce]});var v=r.watch("contentDripType");var m=r.watch("enable_tutor_bp");var h=[{label:(0,a.__)("General","tutor"),value:"general",icon:(0,n.tZ)(s.Z,{name:"settings",width:24,height:24})},{label:(0,a.__)("Content Drip","tutor"),value:"content_drip",icon:(0,n.tZ)(s.Z,{name:"contentDrip",width:24,height:24}),activeBadge:!!v},{label:(0,a.__)("Enrollment","tutor"),value:"enrollment",icon:(0,n.tZ)(s.Z,{name:"update",width:24,height:24})}];if((0,N.ro)(C.AO.BUDDYPRESS)){h.push({label:(0,a.__)("BuddyPress","tutor"),value:"buddyPress",icon:(0,n.tZ)(s.Z,{name:"buddyPress",width:24,height:24}),activeBadge:m})}var b=(A.y.difficulty_levels||[]).map((function(t){return{label:t.label,value:t.value}}));return(0,n.tZ)("div",null,(0,n.tZ)("label",{css:k.c.caption()},(0,a.__)("Options","tutor")),(0,n.tZ)("div",{css:Le.courseSettings},(0,n.tZ)(Ut.Z,{tabList:C.iM.isAboveSmallMobile?h:h.map((function(t){return Ze(Ze({},t),{},{label:d===t.value?t.label:""})})),activeTab:d,onChange:f,orientation:!C.iM.isAboveSmallMobile?"horizontal":"vertical",wrapperCss:Te}),(0,n.tZ)("div",{css:(0,n.iv)({borderLeft:"1px solid ".concat(T.Jv.stroke.divider)},true?"":0,true?"":0)},d==="general"&&(0,n.tZ)("div",{css:Le.settingsOptions},(0,n.tZ)(u.Qr,{name:"course_level",control:r.control,render:function t(e){return(0,n.tZ)(tt.Z,_e({},e,{label:(0,a.__)("Difficulty Level","tutor"),placeholder:(0,a.__)("Select Difficulty Level","tutor"),helpText:(0,a.__)("Course difficulty level","tutor"),options:b,isClearable:false,loading:!!p&&!e.field.value}))}}),(0,n.tZ)("div",{css:Le.courseAndQna},(0,n.tZ)(u.Qr,{name:"is_public_course",control:r.control,render:function t(e){return(0,n.tZ)(E.Z,_e({},e,{label:(0,a.__)("Public Course","tutor"),helpText:(0,a.__)("Make This Course Public. No Enrollment Required.","tutor"),loading:!!p&&!e.field.value}))}}),(0,n.tZ)(I.Z,{when:((e=A.y.settings)===null||e===void 0?void 0:e.enable_q_and_a_on_course)==="on"},(0,n.tZ)(u.Qr,{name:"enable_qna",control:r.control,render:function t(e){return(0,n.tZ)(E.Z,_e({},e,{label:(0,a.__)("Q&A","tutor"),helpText:(0,a.__)("Enable Q&A section for your course","tutor"),loading:!!p&&!e.field.value}))}})))),d==="content_drip"&&(0,n.tZ)(Kt,null),d==="enrollment"&&(0,n.tZ)(he,null),d==="buddyPress"&&(0,n.tZ)("div",{css:Le.settingsOptions},(0,n.tZ)(u.Qr,{name:"enable_tutor_bp",control:r.control,render:function t(e){return(0,n.tZ)(j.Z,_e({},e,{label:(0,a.__)("Enable BuddyPress group activity feeds","tutor")}))}}),(0,n.tZ)(u.Qr,{name:"bp_attached_group_ids",control:r.control,render:function t(e){return(0,n.tZ)(Jt.Z,_e({},e,{label:(0,a.__)("BuddyPress Groups","tutor"),helpText:(0,a.__)("Assign this course to BuddyPress Groups","tutor"),placeholder:(0,a.__)("Search BuddyPress Groups","tutor"),options:(A.y.bp_groups||[]).map((function(t){return{label:t.name,value:String(t.id)}})),loading:!!p&&!e.field.value}))}})))))};const Ie=ke;var Le={courseSettings:(0,n.iv)("display:grid;grid-template-columns:200px 1fr;margin-top:",T.W0[12],";border:1px solid ",T.Jv.stroke["default"],";border-radius:",T.E0[6],";background-color:",T.Jv.background["default"],";overflow:hidden;",T.Uo.smallMobile,"{grid-template-columns:1fr;}"+(true?"":0),true?"":0),settingsOptions:(0,n.iv)("min-height:400px;display:flex;flex-direction:column;gap:",T.W0[12],";padding:",T.W0[16]," ",T.W0[32]," ",T.W0[48]," ",T.W0[32],";background-color:",T.Jv.background.white,";",T.Uo.smallMobile,"{padding:",T.W0[16],";}"+(true?"":0),true?"":0),courseAndQna:(0,n.iv)("display:flex;flex-direction:column;gap:",T.W0[32],";margin-top:",T.W0[12],";"+(true?"":0),true?"":0)};var Ne=r(6051);var Qe=r(674);var Fe=r(9528);var qe=r(1343);var Me=r(81);var Ge=r(8488);function Ve(t){"@babel/helpers - typeof";return Ve="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ve(t)}function Be(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Be=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function t(e,r,n){return e[r]=n}}function c(t,e,r,o){var i=e&&e.prototype instanceof f?e:f,a=Object.create(i.prototype),l=new x(o||[]);return n(a,"_invoke",{value:Z(t,r,l)}),a}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var m={};u(m,i,(function(){return this}));var h=Object.getPrototypeOf,b=h&&h(h(j([])));b&&b!==e&&r.call(b,i)&&(m=b);var y=v.prototype=f.prototype=Object.create(m);function _(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function g(t,e){function o(n,i,a,l){var u=s(t[n],t,i);if("throw"!==u.type){var c=u.arg,d=c.value;return d&&"object"==Ve(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){o("next",t,a,l)}),(function(t){o("throw",t,a,l)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return o("throw",t,a,l)}))}l(u.arg)}var i;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){o(r,n,t,e)}))}return i=i?i.then(a,a):a()}})}function Z(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return D()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var l=w(a,r);if(l){if(l===d)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var u=s(t,e,r);if("normal"===u.type){if(n=r.done?"completed":"suspendedYield",u.arg===d)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n="completed",r.method="throw",r.arg=u.arg)}}}function w(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,w(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var o=s(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,d;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function S(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function x(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(S,this),this.reset(!0)}function j(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return o.next=o}}return{next:D}}function D(){return{value:undefined,done:!0}}return p.prototype=v,n(y,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=u(v,l,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,u(t,l,"GeneratorFunction")),t.prototype=Object.create(y),t},t.awrap=function(t){return{__await:t}},_(g.prototype),u(g.prototype,a,(function(){return this})),t.AsyncIterator=g,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new g(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},_(y),u(y,l,"Generator"),u(y,i,(function(){return this})),u(y,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=j,x.prototype={constructor:x,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function o(t,r){return l.type="throw",l.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],l=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(u&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function t(e,n){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var l=a?a.completion:{};return l.type=e,l.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(l)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:j(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function ze(t,e,r,n,o,i,a){try{var l=t[i](a);var u=l.value}catch(t){r(t);return}if(l.done){e(u)}else{Promise.resolve(u).then(n,o)}}function Ue(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){ze(i,n,o,a,l,"next",t)}function l(t){ze(i,n,o,a,l,"throw",t)}a(undefined)}))}}function Je(){Je=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Je.apply(this,arguments)}function Re(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Ye(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Re(Object(r),!0).forEach((function(e){$e(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Re(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function $e(t,e,r){e=He(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function He(t){var e=Ke(t,"string");return Ve(e)==="symbol"?e:String(e)}function Ke(t,e){if(Ve(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Ve(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Xe(t,e){return or(t)||nr(t,e)||er(t,e)||tr()}function tr(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function er(t,e){if(!t)return;if(typeof t==="string")return rr(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rr(t,e)}function rr(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function nr(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,l=[],u=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(l.push(n.value),l.length!==e);u=!0);}catch(t){c=!0,o=t}finally{try{if(!u&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return l}}function or(t){if(Array.isArray(t))return t}var ir=(0,ot.zs)();var ar=false;var lr=function t(){var e;var r=(0,Me.l)(),c=r.fields;var s=(0,u.Gc)();var d=(0,o.NL)();var f=(0,i.y)({queryKey:["CourseDetails",ir]});var p=(0,st.mG)();var v=(0,Ge.F)();var m=(0,l.useState)(false),h=Xe(m,2),b=h[0],y=h[1];var _=d.getQueryData(["CourseDetails",ir]);var g=!!A.y.tutor_pro_url;var Z=((e=A.y.settings)===null||e===void 0?void 0:e.chatgpt_enable)==="on";var w=s.watch("post_status");var S=s.watch("editor_used");return(0,n.tZ)("div",{css:cr.wrapper},(0,n.tZ)("div",{css:cr.mainForm({isWpEditorFullScreen:b})},(0,n.tZ)("div",{css:cr.fieldsWrapper},(0,n.tZ)("div",{css:cr.titleAndSlug},(0,n.tZ)(u.Qr,{name:"post_title",control:s.control,rules:Ye(Ye({},(0,Q.n0)()),(0,Q.T9)(255)),render:function t(e){return(0,n.tZ)(X.Z,Je({},e,{label:(0,a.__)("Title","tutor"),placeholder:(0,a.__)("ex. Learn Photoshop CS6 from scratch","tutor"),isClearable:true,selectOnFocus:true,generateWithAi:!g||Z,loading:!!f&&!e.field.value,onChange:function t(e){if(w==="draft"&&!ar){s.setValue("post_name",(0,N.k6)(String(e)),{shouldValidate:true,shouldDirty:true})}}}))}}),(0,n.tZ)(u.Qr,{name:"post_name",control:s.control,render:function t(e){var r;return(0,n.tZ)(Qe.Z,Je({},e,{label:(0,a.__)("Course URL","tutor"),baseURL:"".concat(A.y.home_url,"/").concat((r=A.y.settings)===null||r===void 0?void 0:r.course_permalink_base),onChange:function t(){ar=true}}))}})),(0,n.tZ)(u.Qr,{name:"post_content",control:s.control,render:function t(e){return(0,n.tZ)(Fe.Z,Je({},e,{label:(0,a.__)("Description","tutor"),loading:!!f&&!e.field.value,max_height:280,generateWithAi:!g||Z,hasCustomEditorSupport:true,editorUsed:S,editors:_===null||_===void 0?void 0:_.editors,onCustomEditorButtonClick:function t(){return s.handleSubmit((function(t){var e=(0,st.iC)(t,(0,N.hk)({fields:c.Basic},{fields:c.Additional}));return p.mutateAsync(Ye(Ye({course_id:ir},e),{},{post_status:(0,N.Xl)(s.getValues("post_status"),s.getValues("visibility"))}))}))()},onBackToWPEditorClick:function(){var t=Ue(Be().mark((function t(e){return Be().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:return r.abrupt("return",v.mutateAsync({courseId:ir,builder:e}).then((function(t){s.setValue("editor_used",{name:"classic",label:(0,a.__)("Classic Editor","tutor"),link:""});return t})));case 1:case"end":return r.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),onFullScreenChange:function t(e){y(e)}}))}}),(0,n.tZ)(qe.Z,{section:"Basic.after_description",form:s}),(0,n.tZ)(Ie,null),(0,n.tZ)(qe.Z,{section:"Basic.after_settings",form:s})),(0,n.tZ)(I.Z,{when:C.iM.isAboveTablet},(0,n.tZ)(Ne.Z,{styleModifier:cr.navigator}))),(0,n.tZ)(Bt,null),(0,n.tZ)(I.Z,{when:!C.iM.isAboveTablet},(0,n.tZ)(Ne.Z,{styleModifier:cr.navigator})))};const ur=lr;var cr={wrapper:(0,n.iv)("display:grid;grid-template-columns:1fr 338px;gap:",T.W0[32],";width:100%;",T.Uo.smallTablet,"{grid-template-columns:1fr;gap:0;}"+(true?"":0),true?"":0),mainForm:function t(e){var r=e.isWpEditorFullScreen;return(0,n.iv)("padding-block:",T.W0[32]," ",T.W0[24],";align-self:start;top:",T.J9,"px;position:sticky;",r&&(0,n.iv)("z-index:",T.W5.header+1,";"+(true?"":0),true?"":0)," ",T.Uo.smallTablet,"{padding-top:",T.W0[16],";position:unset;}"+(true?"":0),true?"":0)},fieldsWrapper:(0,n.iv)("display:flex;flex-direction:column;gap:",T.W0[24],";"+(true?"":0),true?"":0),titleAndSlug:(0,n.iv)("display:flex;flex-direction:column;gap:",T.W0[8],";"+(true?"":0),true?"":0),sidebar:(0,n.iv)("border-left:1px solid ",T.Jv.stroke.divider,";min-height:calc(100vh - ",T.J9,"px);padding-left:",T.W0[32],";padding-block:",T.W0[24],";display:flex;flex-direction:column;gap:",T.W0[16],";"+(true?"":0),true?"":0),priceRadioGroup:(0,n.iv)("display:flex;align-items:center;gap:",T.W0[36],";"+(true?"":0),true?"":0),coursePriceWrapper:(0,n.iv)("display:flex;align-items:flex-start;gap:",T.W0[16],";"+(true?"":0),true?"":0),navigator:(0,n.iv)("margin-top:",T.W0[40],";",T.Uo.smallTablet,"{margin-top:0;}"+(true?"":0),true?"":0),statusAndDate:(0,n.iv)(L.i.display.flex("column"),";gap:",T.W0[4],";"+(true?"":0),true?"":0),updatedOn:(0,n.iv)(k.c.caption(),";color:",T.Jv.text.hints,";"+(true?"":0),true?"":0)}},5036:(t,e,r)=>{r.d(e,{Z:()=>n});const n=r.p+"images/92fb1ef207f274b44e6d8c8d4c539329-profile-photo.png"},6614:(t,e,r)=>{r.d(e,{Z:()=>n});const n=r.p+"images/8883d834437ecd54063a38ba8ec0ef37-subscriptions-empty-state-2x.webp"},6022:(t,e,r)=>{r.d(e,{Z:()=>n});const n=r.p+"images/026952ce6dfdf3da34dc55d99f241520-subscriptions-empty-state.webp"},2141:(t,e,r)=>{r.d(e,{y:()=>a});var n=r(7363);var o=r(7037);var i=r(202);"use client";function a(t,e){const r=(0,i.NL)(e);const a=r.getQueryCache();return n.useSyncExternalStore(n.useCallback((t=>a.subscribe(o.V.batchCalls(t))),[a]),(()=>r.isFetching(t)),(()=>r.isFetching(t)))}}}]);