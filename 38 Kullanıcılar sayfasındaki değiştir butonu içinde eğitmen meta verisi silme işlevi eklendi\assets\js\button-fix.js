/**
 * <PERSON><PERSON> Butonu Düzeltme JavaScript
 * Bu script, "Kurs Oluştur" butonunun stilini JavaScript ile düzeltir.
 * <PERSON><PERSON> yüklendikten sonra çalışır ve butonu doğrudan hedefler.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Kurs Oluştur butonunu bul
    const createCourseButton = document.querySelector('.tutor-btn.tutor-btn-outline-primary.tutor-create-new-course');
    
    if (createCourseButton) {
        // Butonun stilini düzelt
        createCourseButton.style.border = '1px solid var(--tutor-color-primary)';
        createCourseButton.style.color = 'var(--tutor-color-primary)';
        createCourseButton.style.backgroundColor = 'transparent';
        createCourseButton.style.backgroundImage = 'none';
        createCourseButton.style.boxShadow = 'none';
        createCourseButton.style.transform = 'none';
        createCourseButton.style.padding = '8px 16px';
        createCourseButton.style.fontSize = '14px';
        createCourseButton.style.fontWeight = '500';
        createCourseButton.style.borderRadius = '6px';
        createCourseButton.style.textDecoration = 'none';
        createCourseButton.style.transition = 'all 0.3s ease';
        createCourseButton.style.textAlign = 'center';
        createCourseButton.style.display = 'inline-flex';
        createCourseButton.style.alignItems = 'center';
        
        // Hover olayını ekle
        createCourseButton.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'rgba(var(--tutor-primary-rgb), 0.1)';
            this.style.color = 'var(--tutor-color-primary)';
        });
        
        // Hover olayını kaldır
        createCourseButton.addEventListener('mouseleave', function() {
            this.style.backgroundColor = 'transparent';
            this.style.color = 'var(--tutor-color-primary)';
        });
        
        // Butonun içindeki ikonu bul ve stilini düzelt
        const icon = createCourseButton.querySelector('i');
        if (icon) {
            icon.style.color = 'var(--tutor-color-primary)';
            icon.style.marginRight = '8px';
        }
    }
});
