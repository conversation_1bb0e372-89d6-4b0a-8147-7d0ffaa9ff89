<?php
/**
 * Superole Tutor LMS Entegrasyonu Test Dosyası
 * 
 * Bu dosya superole kullanıcılarının Tutor instructor o<PERSON><PERSON> doğru şekilde
 * tanınıp tanınmadığını test eder ve gerekli düzeltmeleri yapar.
 */

// WordPress dışından erişimi engelle
if (!defined('ABSPATH')) {
    exit;
}

// Sadece admin kullanıcılar için
if (!current_user_can('manage_options')) {
    return;
}

/**
 * Superole Tutor entegrasyonu test fonksiyonu
 */
function test_superole_tutor_integration() {
    echo "<div style='background: #fff; padding: 20px; margin: 20px; border: 1px solid #ccc;'>";
    echo "<h2>🎓 Superole Tutor LMS Entegrasyonu Test Sonuçları</h2>";
    
    // Tutor LMS aktif mi kontrol et
    if (!function_exists('tutor')) {
        echo "<p style='color: red;'>❌ Tutor LMS eklentisi aktif değil!</p>";
        echo "</div>";
        return;
    }
    
    echo "<p style='color: green;'>✅ Tutor LMS eklentisi aktif</p>";
    
    // Instructor rolünü al
    $instructor_role = tutor()->instructor_role;
    echo "<p><strong>Instructor Rolü:</strong> {$instructor_role}</p>";
    
    // Superole kullanıcılarını al
    $superole_users = get_users(array(
        'role' => RoleCustom::SUPEROLE_ROLE,
        'number' => 50
    ));
    
    echo "<h3>📊 Superole Kullanıcıları Analizi</h3>";
    echo "<p><strong>Toplam Superole Kullanıcısı:</strong> " . count($superole_users) . "</p>";
    
    if (empty($superole_users)) {
        echo "<p style='color: orange;'>⚠️ Hiç superole kullanıcısı bulunamadı.</p>";
        echo "</div>";
        return;
    }
    
    $fixed_count = 0;
    $already_ok_count = 0;
    
    echo "<h4>🔍 Kullanıcı Detayları:</h4>";
    echo "<table style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th style='border: 1px solid #ddd; padding: 8px;'>Kullanıcı</th>";
    echo "<th style='border: 1px solid #ddd; padding: 8px;'>Instructor Meta</th>";
    echo "<th style='border: 1px solid #ddd; padding: 8px;'>Status</th>";
    echo "<th style='border: 1px solid #ddd; padding: 8px;'>Tutor Rolü</th>";
    echo "<th style='border: 1px solid #ddd; padding: 8px;'>İşlem</th>";
    echo "</tr>";
    
    foreach ($superole_users as $user) {
        $needs_fix = false;
        $actions = array();
        
        // Meta verileri kontrol et
        $is_instructor = get_user_meta($user->ID, '_is_tutor_instructor', true);
        $instructor_status = get_user_meta($user->ID, '_tutor_instructor_status', true);
        $instructor_approved = get_user_meta($user->ID, '_tutor_instructor_approved', true);
        
        // Tutor instructor rolü kontrol et
        $has_tutor_role = in_array($instructor_role, $user->roles);
        
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$user->display_name} (ID: {$user->ID})</td>";
        
        // Meta veriler
        $meta_status = "";
        if (!$is_instructor) {
            $meta_status .= "❌ _is_tutor_instructor eksik<br>";
            $needs_fix = true;
            $actions[] = "Instructor meta eklendi";
        } else {
            $meta_status .= "✅ _is_tutor_instructor: " . date('Y-m-d H:i:s', $is_instructor) . "<br>";
        }
        
        if (!$instructor_status || $instructor_status !== 'approved') {
            $meta_status .= "❌ Status: " . ($instructor_status ?: 'yok') . "<br>";
            $needs_fix = true;
            $actions[] = "Status 'approved' yapıldı";
        } else {
            $meta_status .= "✅ Status: approved<br>";
        }
        
        if (!$instructor_approved) {
            $meta_status .= "❌ Approved meta eksik";
            $needs_fix = true;
            $actions[] = "Approved meta eklendi";
        } else {
            $meta_status .= "✅ Approved: " . date('Y-m-d H:i:s', $instructor_approved);
        }
        
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$meta_status}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . ($instructor_status ?: 'yok') . "</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . ($has_tutor_role ? "✅ Var" : "❌ Yok") . "</td>";
        
        // Düzeltme işlemleri
        if ($needs_fix || !$has_tutor_role) {
            // Meta verileri düzelt
            if (!$is_instructor) {
                update_user_meta($user->ID, '_is_tutor_instructor', tutor_time());
            }
            
            if (!$instructor_status || $instructor_status !== 'approved') {
                update_user_meta($user->ID, '_tutor_instructor_status', 'approved');
            }
            
            if (!$instructor_approved) {
                update_user_meta($user->ID, '_tutor_instructor_approved', tutor_time());
            }
            
            // Tutor instructor rolünü ekle
            if (!$has_tutor_role) {
                $user_obj = new WP_User($user->ID);
                $user_obj->add_role($instructor_role);
                $actions[] = "Tutor instructor rolü eklendi";
            }
            
            echo "<td style='border: 1px solid #ddd; padding: 8px; color: green;'>🔧 " . implode('<br>', $actions) . "</td>";
            $fixed_count++;
        } else {
            echo "<td style='border: 1px solid #ddd; padding: 8px; color: green;'>✅ Zaten tamam</td>";
            $already_ok_count++;
        }
        
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "<h3>📈 Özet</h3>";
    echo "<ul>";
    echo "<li><strong>Düzeltilen kullanıcı sayısı:</strong> {$fixed_count}</li>";
    echo "<li><strong>Zaten tamam olan kullanıcı sayısı:</strong> {$already_ok_count}</li>";
    echo "<li><strong>Toplam işlenen kullanıcı:</strong> " . count($superole_users) . "</li>";
    echo "</ul>";
    
    // Tutor is_instructor fonksiyonu test et
    echo "<h3>🧪 Tutor is_instructor Fonksionu Testi</h3>";
    if (!empty($superole_users)) {
        $test_user = $superole_users[0];
        $is_instructor_result = tutor_utils()->is_instructor($test_user->ID);
        echo "<p><strong>Test Kullanıcısı:</strong> {$test_user->display_name} (ID: {$test_user->ID})</p>";
        echo "<p><strong>tutor_utils()->is_instructor() sonucu:</strong> " . ($is_instructor_result ? "✅ True" : "❌ False") . "</p>";
        
        // Filter test et
        $filter_result = apply_filters('tutor_is_instructor', false, $test_user->ID, $test_user);
        echo "<p><strong>tutor_is_instructor filter sonucu:</strong> " . ($filter_result ? "✅ True" : "❌ False") . "</p>";
    }
    
    echo "<p style='color: green; font-weight: bold;'>✅ Test tamamlandı!</p>";
    echo "</div>";
}

// Test fonksiyonunu çalıştır
if (isset($_GET['test_superole_tutor']) && $_GET['test_superole_tutor'] === '1') {
    add_action('admin_notices', 'test_superole_tutor_integration');
}

// Admin menüsüne test linki ekle
add_action('admin_menu', function() {
    add_submenu_page(
        'tools.php',
        'Superole Tutor Test',
        'Superole Tutor Test',
        'manage_options',
        'superole-tutor-test',
        function() {
            echo '<div class="wrap">';
            echo '<h1>Superole Tutor LMS Entegrasyonu Testi</h1>';
            echo '<p>Bu test superole kullanıcılarının Tutor instructor olarak doğru şekilde tanınıp tanınmadığını kontrol eder.</p>';
            echo '<a href="' . admin_url('tools.php?page=superole-tutor-test&test_superole_tutor=1') . '" class="button button-primary">Testi Çalıştır</a>';
            
            if (isset($_GET['test_superole_tutor']) && $_GET['test_superole_tutor'] === '1') {
                test_superole_tutor_integration();
            }
            
            echo '</div>';
        }
    );
});
