/**
 * Başlık Animasyonlarını Devre Dışı Bırakma
 * Bu JavaScript dosyası, başlık animasyonlarını devre dışı bırakan CSS dosyasını yükler.
 * NOT: Sayfa geçiş animasyonları korunur.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Dashboard sayfasında olup olmadığımızı kontrol et
    if (document.querySelector('.tutor-dashboard') || document.body.classList.contains('tutor-dashboard-page')) {
        // CSS dosyasının yolunu oluştur
        var cssPath = tutor_dashboard_spotlight_data.plugin_url + 'assets/css/disable-title-animations.css';

        // CSS dosyasını dinamik olarak ekle
        var link = document.createElement('link');
        link.rel = 'stylesheet';
        link.type = 'text/css';
        link.href = cssPath;
        link.id = 'tutor-disable-title-animations-css';
        document.head.appendChild(link);

        // Sayfa başlıklarını seç ve görünürlüğünü sağla
        var pageTitles = document.querySelectorAll('.tutor-fs-5, .tutor-dashboard-title, h1.tutor-fs-5, h2.tutor-fs-5, h3.tutor-fs-5, .header-title');

        pageTitles.forEach(function(title) {
            // Sadece görünürlük özelliklerini ayarla, diğer animasyonları koru
            title.style.opacity = '1';
            title.style.visibility = 'visible';
        });
    }
});
