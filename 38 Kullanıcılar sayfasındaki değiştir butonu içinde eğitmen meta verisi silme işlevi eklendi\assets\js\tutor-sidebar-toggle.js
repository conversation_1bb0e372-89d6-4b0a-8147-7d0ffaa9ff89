/**
 * Tutor Sidebar Toggle
 * Bu script, hamburger menü butonuna tıklandığında sidebar genişliğini ve içerik alanını değiştirir
 */

(function($) {
    'use strict';

    // <PERSON>ğişkenler
    let isExpanded = false;

    /**
     * Sidebar ve içerik alanının stilini değiştir
     */
    function toggleSidebarStyle() {
        // Sidebar ve içerik alanı elementlerini seç
        const body = $('body');
        const sidebar = $('.tutor-course-single-sidebar-wrapper');
        const content = $('.tutor-course-single-content-wrapper #tutor-single-entry-content');

        if (!isExpanded) {
            // Genişletilmiş görünüme geç (sidebar gizli, içerik tam genişlikte)
            body.addClass('tutor-sidebar-expanded');

            // Genişletilmiş durumu kaydet
            localStorage.setItem('tutorSidebarExpanded', 'true');
            isExpanded = true;
        } else {
            // Normal görünüme geri dön (sidebar görünür, içerik daraltılmış)
            body.removeClass('tutor-sidebar-expanded');

            // Normal durumu kaydet
            localStorage.setItem('tutorSidebarExpanded', 'false');
            isExpanded = false;
        }
    }

    /**
     * Sidebar toggle işlevselliğini başlat
     */
    function initSidebarToggle() {
        // Hamburger menü butonlarını seç
        const hamburgerButtons = $('[tutor-course-topics-sidebar-toggler], [tutor-course-topics-sidebar-offcanvas-toggler]');

        // Butonlara tıklama olayı ekle
        hamburgerButtons.on('click', function(e) {
            // Sadece masaüstü görünümünde geniş moda geçsin
            // Mobil ve tablet görünümünde sadece sidebar'i açıp kapatsın, geniş moda geçmesin
            if (window.matchMedia('(min-width: 1200px)').matches) {
                toggleSidebarStyle();
            }
            // Mobil ve tablet görünümünde herhangi bir şey yapma
            // Varsayılan davranış sadece sidebar'i açıp kapatmak olacak
            // Mobil/tablet için e.preventDefault() ekleyelim ki sayfa yeniden yüklenmesin
            else {
                e.preventDefault();
            }
        });
    }

    // DOM yüklendiğinde çalışacak
    $(document).ready(function() {
        // Önceki durumu kontrol et
        const savedState = localStorage.getItem('tutorSidebarExpanded');

        // Sayfa yüklendiğinde hemen görünümü uygula
        setTimeout(function() {
            if (savedState === 'true') {
                // Eğer sidebar kapalı olarak kaydedilmişse
                if (!isExpanded) {
                    // Sidebar'a kapalı sınıfını ekle
                    $('body').addClass('tutor-sidebar-expanded');
                    isExpanded = true;
                }
            } else if (savedState === 'false') {
                // Eğer sidebar açık olarak kaydedilmişse
                if (isExpanded) {
                    // Sidebar'dan kapalı sınıfını kaldır
                    $('body').removeClass('tutor-sidebar-expanded');
                    isExpanded = false;
                }
            }
        }, 500); // Sayfanın yüklenmesi için kısa bir gecikme

        // Önceki ve sonraki butonlarına tıklama olayı ekle
        $('.tutor-single-course-content-prev a, .tutor-single-course-content-next a').on('click', function() {
            // Sidebar durumunu URL'ye ekle
            const currentUrl = $(this).attr('href');
            const separator = currentUrl.includes('?') ? '&' : '?';
            $(this).attr('href', currentUrl + separator + 'sidebar_state=' + (isExpanded ? 'expanded' : 'normal'));
        });

        // URL'den sidebar durumunu kontrol et
        const urlParams = new URLSearchParams(window.location.search);
        const sidebarState = urlParams.get('sidebar_state');
        if (sidebarState === 'expanded' && !isExpanded) {
            // Sidebar'a kapalı sınıfını ekle
            $('body').addClass('tutor-sidebar-expanded');
            isExpanded = true;
            localStorage.setItem('tutorSidebarExpanded', 'true');
        } else if (sidebarState === 'normal' && isExpanded) {
            // Sidebar'dan kapalı sınıfını kaldır
            $('body').removeClass('tutor-sidebar-expanded');
            isExpanded = false;
            localStorage.setItem('tutorSidebarExpanded', 'false');
        }

        initSidebarToggle();
    });

})(jQuery);
