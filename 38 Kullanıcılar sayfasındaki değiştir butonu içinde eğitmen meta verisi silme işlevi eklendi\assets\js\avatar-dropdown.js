/**
 * Avatar Dropdown Menü İşlevselliği
 * Bu dosya, avatar üzerine tıklandığında açılan dropdown menünün işlevselliğini sağlar.
 */

(function($) {
    'use strict';

    $(document).ready(function() {
        initAvatarDropdown();
    });

    /**
     * Avatar dropdown menüsünü başlat
     */
    function initAvatarDropdown() {
        // Avatar elementini seç
        const avatarElement = $('.tutor-dashboard-header-avatar');

        // Eğer avatar elementi yoksa işlemi sonlandır
        if (!avatarElement.length) {
            return;
        }

        // Kullanıcı bilgilerini al
        const userInfo = $('.tutor-header-right-side .tutor-user-info');
        const userName = userInfo.find('.tutor-fs-4').text().trim();
        const userRole = userInfo.find('.tutor-fs-7').text().trim();
        const userAvatar = avatarElement.html();

        // Yıld<PERSON>z alanını al
        const starRatingElement = $('.tutor-dashboard-header-stats').clone();

        // Dropdown menü HTML'ini oluştur
        const dropdownHTML = `
            <div class="tutor-avatar-dropdown">
                <div class="tutor-avatar-dropdown-header">
                    ${userAvatar}
                    <div class="tutor-user-info">
                        <p class="tutor-user-name">${userName}</p>
                        <p class="tutor-user-role">${userRole || 'Öğrenci'}</p>
                    </div>
                </div>
                <div class="tutor-avatar-dropdown-stats">
                    ${starRatingElement.html() || ''}
                </div>
                <ul class="tutor-avatar-dropdown-menu">
                    <li>
                        <a href="http://localhost/wordpress/kontrol-paneli/my-profile/" class="tutor-dashboard-menu-item-link">
                            <i class="tutor-icon-user-bold tutor-dashboard-menu-item-icon"></i>
                            <span class="tutor-dashboard-menu-item-text tutor-ml-12">Profilim</span>
                        </a>
                    </li>
                    <li>
                        <a href="http://localhost/wordpress/kontrol-paneli/settings/" class="tutor-dashboard-menu-item-link">
                            <i class="tutor-icon-gear tutor-dashboard-menu-item-icon"></i>
                            <span class="tutor-dashboard-menu-item-text tutor-ml-12">Ayarlar</span>
                        </a>
                    </li>
                    <li class="tutor-avatar-dropdown-divider"></li>
                    <li>
                        <a href="http://localhost/wordpress/kontrol-paneli/logout/" class="tutor-dashboard-menu-item-link">
                            <i class="tutor-icon-signout tutor-dashboard-menu-item-icon"></i>
                            <span class="tutor-dashboard-menu-item-text tutor-ml-12">Çıkış Yap</span>
                        </a>
                    </li>
                </ul>
            </div>
        `;

        // Dropdown menüyü avatar elementine ekle
        avatarElement.append(dropdownHTML);

        // Avatar elementine tıklama olayı ekle
        avatarElement.on('click', function(e) {
            e.stopPropagation();

            // Dropdown menüyü aç/kapat (toggle)
            $(this).find('.tutor-avatar-dropdown').toggleClass('show');
        });

        // Dropdown menü içindeki elementlere tıklama olayı ekle
        $('.tutor-avatar-dropdown-header, .tutor-avatar-dropdown-menu').on('click', function(e) {
            // Dropdown menü açık değilse, tıklamayı engelle
            if (!$(this).closest('.tutor-avatar-dropdown').hasClass('show')) {
                e.stopPropagation();
                e.preventDefault();
                return false;
            }
        });

        // Sayfa dışına tıklandığında dropdown menüyü kapat
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.tutor-dashboard-header-avatar').length) {
                $('.tutor-avatar-dropdown').removeClass('show');
            }
        });
    }

})(jQuery);
