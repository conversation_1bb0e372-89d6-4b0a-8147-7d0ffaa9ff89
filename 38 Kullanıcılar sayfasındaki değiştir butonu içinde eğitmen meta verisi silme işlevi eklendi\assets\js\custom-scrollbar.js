/**
 * ÖZEL KAYDIRMA ÇUBUĞU KONTROLÜ
 * Bu script, kaydırma çubuğunun tam uzunlukta olup olmadığını kontrol eder
 * ve buna göre HTML elementine sınıf ekler/kaldırır.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Kaydırma çubuğunun durumunu kontrol et ve güncelle
    function updateScrollbarVisibility() {
        const html = document.documentElement;
        const body = document.body;

        // Sayfanın toplam yüksekliği
        const scrollHeight = Math.max(
            body.scrollHeight,
            body.offsetHeight,
            html.clientHeight,
            html.scrollHeight,
            html.offsetHeight
        );

        // Görünür alan yüksekliği
        const clientHeight = html.clientHeight;

        // İçerik tam olarak görüntü alanına sığıyor mu?
        // <PERSON>i kaydırma çubuğu tam uzunlukta mı?
        const isFullLength = scrollHeight <= clientHeight + 5; // 5px tolerans

        // HTML elementine sınıf ekle/kaldır
        if (isFullLength) {
            html.classList.add('scroll-at-max');
        } else {
            html.classList.remove('scroll-at-max');
        }
    }

    // Tüm kaydırılabilir elementleri kontrol et
    function checkAllScrollableElements() {
        const scrollableElements = document.querySelectorAll('div, section, article, aside, nav, main, header, footer');

        scrollableElements.forEach(function(element) {
            // İçerik tam olarak görüntü alanına sığıyor mu?
            // Yani kaydırma çubuğu tam uzunlukta mı?
            const isFullLength = element.scrollHeight <= element.clientHeight + 5; // 5px tolerans

            // Element'e sınıf ekle/kaldır
            if (isFullLength) {
                element.classList.add('scroll-at-max');
            } else {
                element.classList.remove('scroll-at-max');
            }

            // Element için boyut değişikliği olayı dinleyicisi ekle
            if (!element.hasResizeListener) {
                // ResizeObserver kullanarak element boyutu değiştiğinde kontrol et
                if (typeof ResizeObserver !== 'undefined') {
                    const resizeObserver = new ResizeObserver(function() {
                        const isFullLength = element.scrollHeight <= element.clientHeight + 5;
                        if (isFullLength) {
                            element.classList.add('scroll-at-max');
                        } else {
                            element.classList.remove('scroll-at-max');
                        }
                    });
                    resizeObserver.observe(element);
                    element.hasResizeListener = true;
                }
            }
        });
    }

    // Sayfa yüklendiğinde kontrol et
    updateScrollbarVisibility();
    checkAllScrollableElements();

    // Sayfa kaydırıldığında kontrol et
    window.addEventListener('scroll', updateScrollbarVisibility);

    // Sayfa boyutu değiştiğinde kontrol et
    window.addEventListener('resize', function() {
        updateScrollbarVisibility();
        checkAllScrollableElements();
    });

    // Sayfa içeriği değiştiğinde kontrol et (AJAX yüklemeleri için)
    const observer = new MutationObserver(function() {
        updateScrollbarVisibility();
        checkAllScrollableElements();
    });

    // Tüm DOM değişikliklerini izle
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
});
