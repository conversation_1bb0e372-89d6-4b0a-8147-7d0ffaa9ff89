/**
 * ÖZEL KAYDIRMA ÇUBUĞU STİLLERİ
 * <PERSON><PERSON>ya, kaydırma çubuğunun görü<PERSON>ümünü özelleştirmek için kullanılır.
 * Kay<PERSON><PERSON><PERSON> çub<PERSON>u her zaman görün<PERSON><PERSON> o<PERSON>k, ancak tam uzunlukta olduğunda (içerik tam olarak görüntü alanına sığdığında) opaklığı sıfır olacaktır.
 */

/* Tüm sayfada kaydırma çubuğu stilini ayarla */
html {
    /* Her zaman kaydırma çubuğunu göster */
    overflow-y: scroll !important;

    /* Firefox için kaydırma çubuğu stilini ayarla */
    scrollbar-width: thin !important;
    scrollbar-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 0.3) transparent !important;
}

/* Webkit (Chrome, Safari, Edge) için kaydırma çub<PERSON>ğ<PERSON> stilini ayarla */
::-webkit-scrollbar {
    width: 6px !important;
    height: 6px !important;
    background-color: transparent !important;
    display: block !important; /* Her zaman göster */
}

::-webkit-scrollbar-track {
    background-color: transparent !important;
    border-radius: var(--tutor-radius-full, 10px) !important;
}

::-webkit-scrollbar-thumb {
    background-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 0.3) !important;
    border-radius: var(--tutor-radius-full, 10px) !important;
    border: 1px solid transparent !important;
    background-clip: content-box !important;
    transition: background-color 0.3s ease, opacity 0.3s ease !important;
}

::-webkit-scrollbar-thumb:hover {
    background-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 0.5) !important;
}

/* Kaydırma çubuğu tam uzunlukta olduğunda opaklığı sıfır olsun */
html.scroll-at-max ::-webkit-scrollbar-thumb {
    opacity: 0 !important;
    background-color: transparent !important;
}

html.scroll-at-max {
    scrollbar-color: transparent transparent !important;
    transition: scrollbar-color 0.3s ease !important;
}

/* Kaydırma çubuğu tam uzunlukta değilse opaklığı %100 olsun */
html:not(.scroll-at-max) ::-webkit-scrollbar-thumb {
    opacity: 1 !important;
    background-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 0.3) !important;
}

/* Tüm kaydırılabilir elementler için stil */
div, section, article, aside, nav, main, header, footer {
    /* Webkit için kaydırma çubuğu stilini ayarla */
    scrollbar-width: thin !important;

    /* Firefox için kaydırma çubuğu stilini ayarla */
    scrollbar-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 0.3) transparent !important;
    transition: scrollbar-color 0.3s ease !important;
}

/* Kaydırma çubuğu tam uzunlukta olduğunda opaklığı sıfır olsun - tüm elementler için */
html.scroll-at-max div::-webkit-scrollbar-thumb,
html.scroll-at-max section::-webkit-scrollbar-thumb,
html.scroll-at-max article::-webkit-scrollbar-thumb,
html.scroll-at-max aside::-webkit-scrollbar-thumb,
html.scroll-at-max nav::-webkit-scrollbar-thumb,
html.scroll-at-max main::-webkit-scrollbar-thumb,
html.scroll-at-max header::-webkit-scrollbar-thumb,
html.scroll-at-max footer::-webkit-scrollbar-thumb,
div.scroll-at-max::-webkit-scrollbar-thumb,
section.scroll-at-max::-webkit-scrollbar-thumb,
article.scroll-at-max::-webkit-scrollbar-thumb,
aside.scroll-at-max::-webkit-scrollbar-thumb,
nav.scroll-at-max::-webkit-scrollbar-thumb,
main.scroll-at-max::-webkit-scrollbar-thumb,
header.scroll-at-max::-webkit-scrollbar-thumb,
footer.scroll-at-max::-webkit-scrollbar-thumb {
    opacity: 0 !important;
    background-color: transparent !important;
}

/* Kaydırma çubuğu tam uzunlukta değilse opaklığı %100 olsun - tüm elementler için */
html:not(.scroll-at-max) div:not(.scroll-at-max)::-webkit-scrollbar-thumb,
html:not(.scroll-at-max) section:not(.scroll-at-max)::-webkit-scrollbar-thumb,
html:not(.scroll-at-max) article:not(.scroll-at-max)::-webkit-scrollbar-thumb,
html:not(.scroll-at-max) aside:not(.scroll-at-max)::-webkit-scrollbar-thumb,
html:not(.scroll-at-max) nav:not(.scroll-at-max)::-webkit-scrollbar-thumb,
html:not(.scroll-at-max) main:not(.scroll-at-max)::-webkit-scrollbar-thumb,
html:not(.scroll-at-max) header:not(.scroll-at-max)::-webkit-scrollbar-thumb,
html:not(.scroll-at-max) footer:not(.scroll-at-max)::-webkit-scrollbar-thumb,
div:not(.scroll-at-max)::-webkit-scrollbar-thumb,
section:not(.scroll-at-max)::-webkit-scrollbar-thumb,
article:not(.scroll-at-max)::-webkit-scrollbar-thumb,
aside:not(.scroll-at-max)::-webkit-scrollbar-thumb,
nav:not(.scroll-at-max)::-webkit-scrollbar-thumb,
main:not(.scroll-at-max)::-webkit-scrollbar-thumb,
header:not(.scroll-at-max)::-webkit-scrollbar-thumb,
footer:not(.scroll-at-max)::-webkit-scrollbar-thumb {
    opacity: 1 !important;
    background-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 0.3) !important;
    transition: background-color 0.3s ease !important;
}

/* Hover durumunda kaydırma çubuğu rengini değiştir */
html:not(.scroll-at-max) div:not(.scroll-at-max)::-webkit-scrollbar-thumb:hover,
html:not(.scroll-at-max) section:not(.scroll-at-max)::-webkit-scrollbar-thumb:hover,
html:not(.scroll-at-max) article:not(.scroll-at-max)::-webkit-scrollbar-thumb:hover,
html:not(.scroll-at-max) aside:not(.scroll-at-max)::-webkit-scrollbar-thumb:hover,
html:not(.scroll-at-max) nav:not(.scroll-at-max)::-webkit-scrollbar-thumb:hover,
html:not(.scroll-at-max) main:not(.scroll-at-max)::-webkit-scrollbar-thumb:hover,
html:not(.scroll-at-max) header:not(.scroll-at-max)::-webkit-scrollbar-thumb:hover,
html:not(.scroll-at-max) footer:not(.scroll-at-max)::-webkit-scrollbar-thumb:hover,
div:not(.scroll-at-max)::-webkit-scrollbar-thumb:hover,
section:not(.scroll-at-max)::-webkit-scrollbar-thumb:hover,
article:not(.scroll-at-max)::-webkit-scrollbar-thumb:hover,
aside:not(.scroll-at-max)::-webkit-scrollbar-thumb:hover,
nav:not(.scroll-at-max)::-webkit-scrollbar-thumb:hover,
main:not(.scroll-at-max)::-webkit-scrollbar-thumb:hover,
header:not(.scroll-at-max)::-webkit-scrollbar-thumb:hover,
footer:not(.scroll-at-max)::-webkit-scrollbar-thumb:hover {
    background-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 0.5) !important;
}
