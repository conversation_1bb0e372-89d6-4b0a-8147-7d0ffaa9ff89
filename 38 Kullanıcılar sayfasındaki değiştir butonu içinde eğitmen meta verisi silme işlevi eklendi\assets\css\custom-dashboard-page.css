/**
 * TUTOR LMS DASHBOARD ÖZEL CSS DOSYASI
 * Bu dosya Tutor LMS eklentisinin dashboard görünümünü özelleştirmek için kullanılır.
 * Spotlight moduna benzer şekilde tema header ve footer'ı gizler.
 */

/* Dashboard sidebar ve header için border rengini kapat */
body.tutor-dashboard-page .tutor-dashboard-left-menu,
body.tutor-dashboard-page .tutor-frontend-dashboard-header {
    --tutor-border-color: transparent !important;
}

/* Dashboard için Spotlight Modu Stilleri */
body.tutor-dashboard-page {
    margin: 0 !important;
    padding: 0 !important;
    overflow-x: hidden !important;
}

/* Tema header'ını gizle */
body.tutor-dashboard-page #masthead,
body.tutor-dashboard-page header.site-header,
body.tutor-dashboard-page header.wp-block-template-part,
body.tutor-dashboard-page header.entry-header,
body.tutor-dashboard-page .site-header,
body.tutor-dashboard-page .main-header,
body.tutor-dashboard-page .header-area,
body.tutor-dashboard-page .elementor-location-header,
body.tutor-dashboard-page #header,
body.tutor-dashboard-page .header:not(.tutor-dashboard-header):not(.tutor-frontend-dashboard-header),
body.tutor-dashboard-page nav.main-navigation,
body.tutor-dashboard-page nav.primary-navigation,
body.tutor-dashboard-page .navigation-top,
body.tutor-dashboard-page .top-header,
body.tutor-dashboard-page .top-bar {
    display: none !important;
}

/* Tema footer'ını gizle */
body.tutor-dashboard-page footer.site-footer,
body.tutor-dashboard-page footer.wp-block-template-part,
body.tutor-dashboard-page .site-footer,
body.tutor-dashboard-page .main-footer,
body.tutor-dashboard-page .footer-area,
body.tutor-dashboard-page .elementor-location-footer,
body.tutor-dashboard-page [class*="footer"],
body.tutor-dashboard-page [id*="footer"],
body.tutor-dashboard-page .colophon,
body.tutor-dashboard-page .site-info {
    display: none !important;
}

/* Tema sidebar'ını gizle */
body.tutor-dashboard-page .site-sidebar,
body.tutor-dashboard-page .sidebar,
body.tutor-dashboard-page .widget-area,
body.tutor-dashboard-page aside.sidebar {
    display: none !important;
}

/* Ana içerik alanını tam genişliğe ayarla */
body.tutor-dashboard-page .site-content,
body.tutor-dashboard-page #content,
body.tutor-dashboard-page .content-area,
body.tutor-dashboard-page main.site-main,
body.tutor-dashboard-page .tutor-container,
body.tutor-dashboard-page .tutor-wrap,
body.tutor-dashboard-page .tutor-dashboard-content-container {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Tutor dashboard içeriğini düzenle */
body.tutor-dashboard-page .tutor-dashboard {
    margin: 0 !important;
    padding: 0 !important;
    max-width: 100% !important;
}

/* Dashboard profil alanını göster ve düzenle */
body.tutor-dashboard-page .tutor-dashboard-header,
body.tutor-dashboard-page .tutor-frontend-dashboard-header,
body.tutor-dashboard-page .tutor-header-left-side,
body.tutor-dashboard-page .tutor-header-right-side {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Header içindeki sol ve sağ tarafı düzenle */
body.tutor-dashboard-page .tutor-header-left-side {
    flex: 1 !important; /* Sol tarafı genişlet */
}

body.tutor-dashboard-page .tutor-header-right-side {
    display: flex !important;
    align-items: center !important;
    justify-content: flex-end !important; /* Sağa hizala */
}

body.tutor-dashboard-page .tutor-dashboard-header-avatar {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

body.tutor-dashboard-page .tutor-user-info {
    display: flex !important;
    flex-direction: column !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Profil alanını düzenle */
body.tutor-dashboard-page .tutor-header-right-side .tutor-dashboard-header-avatar {
    margin-left: 15px !important;
    order: 2 !important; /* Avatar sonra gelsin */
}

/* Kullanıcı bilgilerini düzenle */
body.tutor-dashboard-page .tutor-header-right-side .tutor-user-info {
    order: 3 !important; /* Kullanıcı bilgileri en sonda gelsin */
}

/* Avatar boyutunu ayarla ve ortala */
body.tutor-dashboard-page .tutor-avatar-xl,
body.tutor-dashboard-page .tutor-dashboard-header-avatar .tutor-avatar.tutor-avatar-xl {
    width: 35px !important;
    height: 35px !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

/* Avatar içindeki resmi ortala */
body.tutor-dashboard-page .tutor-dashboard-header-avatar .tutor-avatar img {
    margin: 0 auto !important;
    display: block !important;
}

/* Create a New Course butonunu düzenle */
body.tutor-dashboard-page .tutor-header-right-side .tutor-create-new-course,
body.tutor-dashboard-page .tutor-header-right-side a.tutor-btn {
    margin-right: 15px !important;
    order: 1 !important; /* Buton önce gelsin */
}

/* Header içindeki profil ve buton alanını düzenle */
body.tutor-dashboard-page .tutor-header-right-side {
    display: flex !important;
    flex-direction: row !important; /* Buton solda, profil sağda olacak */
    align-items: center !important;
    /* margin-right: 20px !important; */ /* Sağa doğru biraz daha boşluk ekle - KAPATILDI */
}

/* Profil bölümünü sağ taraftaki header alanına ekle */
body.tutor-dashboard-page .tutor-header-right-side:before {
    content: "" !important;
    display: block !important;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line></svg>') !important;
    width: 24px !important;
    height: 24px !important;
    margin-right: 10px !important;
    visibility: hidden !important; /* Görünmez yap, sadece yer tutucu olarak kullan */
}

body.tutor-dashboard-page .tutor-dashboard-header-username,
body.tutor-dashboard-page .tutor-dashboard-header-stats,
body.tutor-dashboard-page .tutor-dashboard-header-ratings,
body.tutor-dashboard-page .tutor-dashboard-header-display-name,
body.tutor-dashboard-page .tutor-dashboard-header-greetings {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Profil isminin boyutunu küçült */
body.tutor-dashboard-page .tutor-dashboard-header-username,
body.tutor-dashboard-page .tutor-header-right-side .tutor-user-info .tutor-fs-4 {
    font-size: 14px !important; /* Daha küçük font boyutu */
    line-height: 1.2 !important; /* Satır yüksekliğini azalt */
}

/* Sadece Sidebar'ı Sticky Yap ve Genişliğini Ayarla */
body.tutor-dashboard-page .tutor-dashboard-left-menu {
    position: fixed !important;
    height: 100vh !important;
    overflow-y: auto !important;
    z-index: 100 !important;
    width: 320px !important; /* Sabit genişlik */
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05) !important; /* Hafif gölge ekle */
    background-color: #f8f9fa !important; /* Açık gri arka plan */

    /* Kaydırma çubuğu stilini ayarla - başlangıçta şeffaf olsun */
    scrollbar-width: thin !important; /* Firefox için ince kaydırma çubuğu */
    scrollbar-color: transparent transparent !important; /* Firefox için başlangıçta şeffaf kaydırma çubuğu */
    -ms-overflow-style: auto !important; /* IE ve Edge için */
}

/* Webkit (Chrome, Safari) için kaydırma çubuğu stilini ayarla */
body.tutor-dashboard-page .tutor-dashboard-left-menu::-webkit-scrollbar {
    width: 5px !important; /* Kaydırma çubuğu genişliği */
    background-color: transparent !important; /* Kaydırma çubuğu arka plan rengi */
    display: block !important; /* Her zaman göster */
}

body.tutor-dashboard-page .tutor-dashboard-left-menu::-webkit-scrollbar-thumb {
    background-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 0) !important; /* Başlangıçta şeffaf kaydırma çubuğu */
    border-radius: 10px !important; /* Kaydırma çubuğu köşe yuvarlaklığı */
    transition: background-color 0.2s ease !important; /* Geçiş efekti */
}

/* Hover durumunda scrollbar'ı görünür yap */
body.tutor-dashboard-page .tutor-dashboard-left-menu:hover::-webkit-scrollbar-thumb {
    background-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 0.3) !important; /* Hover durumunda görünür kaydırma çubuğu */
}

/* Kaydırma çubuğunun üzerine gelindiğinde daha belirgin yap */
body.tutor-dashboard-page .tutor-dashboard-left-menu::-webkit-scrollbar-thumb:hover {
    background-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 1) !important; /* Kaydırma çubuğu hover durumunda tam opaklık */
}

/* Firefox için hover durumunda scrollbar'ı görünür yap */
body.tutor-dashboard-page .tutor-dashboard-left-menu:hover {
    scrollbar-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 0.3) transparent !important; /* Firefox için görünür kaydırma çubuğu */
}

/* Logo konteyner stilleri */
body.tutor-dashboard-page .tutor-dashboard-logo-container {
    border-bottom: 1px solid #e9ecef !important;
    margin-bottom: 10px !important;
}

/* Logo stilleri */
body.tutor-dashboard-page .tutor-dashboard-logo {
    width: 200px !important;
    height: 130px !important; /* Yükseklik 130px olarak ayarlandı */
    margin: 0 auto !important;
    background-color: #f8f9fa !important;
    border-radius: 8px !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
}

body.tutor-dashboard-page .tutor-dashboard-logo img {
    max-width: 100% !important; /* Genişlik 100% olarak ayarlandı */
    max-height: 100% !important; /* Yükseklik 100% olarak ayarlandı */
    object-fit: contain !important;
}

body.tutor-dashboard-page .tutor-dashboard-logo:hover {
    /* background-color: #e9ecef !important; */
    /* box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important; */
}

/* Sidebar Permalinks Bölümünü Düzenle */
body.tutor-dashboard-page .tutor-dashboard .tutor-dashboard-left-menu .tutor-dashboard-permalinks {
    padding: 10px 0px 0px 15px !important;
    margin-right: 0 !important;
}

/* Sidebar Menü Öğelerini Düzenle */
body.tutor-dashboard-page .tutor-dashboard .tutor-dashboard-left-menu .tutor-dashboard-menu-item-link {
    border-radius: 10px !important;
}

/* Sidebardaki "Profilim", "Ayarlar" ve "Çıkış Yap" seçeneklerini göster */
/* Bu bölüm artık kullanılmıyor, seçenekler görünür durumda */

/* Header'ın sağ tarafındaki kullanıcı bilgilerini tamamen gizle */
body.tutor-dashboard-page .tutor-header-right-side .tutor-user-info.tutor-ml-24 {
    display: none !important;
}

/* Ana İçerik Alanını Sidebar'a Göre Ayarla ve Sağa Taşı */
body.tutor-dashboard-page .tutor-col-12.tutor-col-md-8.tutor-col-lg-9 {
    margin-left: 350px !important; /* Sidebar genişliği + ekstra boşluk */
    padding-left: 50px !important; /* İçeriği daha fazla sağa taşı */
    padding-top: 61px !important; /* Header yüksekliği + ekstra boşluk */
}

/* Dashboard başlıklarını düzenle */
body.tutor-dashboard-page .tutor-dashboard-title,
body.tutor-dashboard-page .tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-text-capitalize.tutor-mb-24.tutor-dashboard-title,
body.tutor-dashboard-page .tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-mb-16.tutor-text-capitalize,
body.tutor-dashboard-page .tutor-profile-title,
body.tutor-dashboard-page .tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-mb-24,
body.tutor-dashboard-page .tutor-fs-5.tutor-fw-medium.tutor-color-black,
body.tutor-dashboard-page .header-title.tutor-fs-5.tutor-fw-medium.tutor-color-black,
body.tutor-dashboard-page h3.tutor-fs-5,
body.tutor-dashboard-page h2.tutor-fs-5,
body.tutor-dashboard-page h1.tutor-fs-5 {
    display: block !important;
    font-size: 1.5rem !important;
    margin-bottom: 1.5rem !important;
    animation: none !important;
    transform: none !important;
    transition: none !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Navigasyon çizgisini kaldır */
body.tutor-dashboard-page .tutor-nav:not(.tutor-nav-pills):not(.tutor-nav-tabs) {
    border-bottom: none !important;
}

/* İçerik Kartlarını Sağa Taşı */
body.tutor-dashboard-page .tutor-dashboard-content-inner .tutor-row {
    /* margin-left: 30px !important; */ /* Sağa taşıma kodunu kapattık */
}

/* Dashboard İçeriğini Sağa Taşı */
body.tutor-dashboard-page .tutor-dashboard-content {
    padding-left: 30px !important;
    padding-right: 30px !important;
}

/* Dashboard içeriğinin üst boşluğunu ayarla */
.tutor-dashboard .tutor-dashboard-content {
    padding-top: 35px !important; /* Üst boşluğu 35px yap */
}

/* İstatistik Kartlarını Sağa Taşı */
body.tutor-dashboard-page .tutor-dashboard-content-inner .tutor-col-lg-4,
body.tutor-dashboard-page .tutor-dashboard-content-inner .tutor-col-lg-3,
body.tutor-dashboard-page .tutor-dashboard-content-inner .tutor-col-md-6 {
    padding-left: 15px !important;
    padding-right: 15px !important;
}

/* Kurs Listesini Sağa Taşı */
body.tutor-dashboard-page .tutor-dashboard-content-inner .tutor-dashboard-content-inner-table {
    margin-left: 30px !important;
}

/* Başlık Alanını Sağa Taşı */
body.tutor-dashboard-page .tutor-dashboard-content-inner h3,
body.tutor-dashboard-page .tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-mb-24,
body.tutor-dashboard-page .tutor-dashboard-content-inner .tutor-fs-5,
body.tutor-dashboard-page .tutor-dashboard-content-inner .tutor-dashboard-title {
    /* margin-left: 30px !important; */ /* Sağa taşıma kodunu kapattık */
}

/* Profil Alanını Sabit (Sticky) Yap */
body.tutor-dashboard-page .tutor-frontend-dashboard-header,
body.tutor-dashboard-page #tutor-page-wrap > div > div.tutor-container > div.tutor-row.tutor-d-flex.tutor-justify-between.tutor-frontend-dashboard-header {
    margin-right: 0 !important;
    position: fixed !important;
    top: 0 !important;
    z-index: 99 !important;
    background-color: #f9f9f9 !important;
    padding: 15px 10px 15px 0px !important;
    /* box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05) !important; */
    border-bottom: 1px solid #e9ecef !important;
    width: calc(100% - 320px) !important; /* Tam genişlik - sidebar genişliği */
    left: 320px !important; /* Sidebar genişliği */
    display: flex !important;
    justify-content: flex-end !important; /* Öğeleri sağa hizala */
}

/* Sol taraftaki header alanını gizle */
body.tutor-dashboard-page .tutor-header-left-side.tutor-dashboard-header.tutor-col-md-6.tutor-d-flex.tutor-align-center {
    display: none !important;
}

/* Admin bar'ı olan sayfalar için üst boşluk ayarla */
body.admin-bar.tutor-dashboard-page {
    margin-top: 32px !important; /* Admin bar yüksekliği */
}

/* Admin bar varsa header'ın konumunu ayarla */
body.admin-bar.tutor-dashboard-page .tutor-frontend-dashboard-header,
body.admin-bar.tutor-dashboard-page #tutor-page-wrap > div > div.tutor-container > div.tutor-row.tutor-d-flex.tutor-justify-between.tutor-frontend-dashboard-header {
    top: 32px !important; /* Admin bar yüksekliği */
}

/* Mobil Uyumluluk */
@media (max-width: 991px) {
    /* Mobil görünümde içerik alanını düzenle */
    body.tutor-dashboard-page .tutor-col-12.tutor-col-md-8.tutor-col-lg-9 {
        margin-left: 0 !important;
        padding-left: 15px !important;
        padding-right: 15px !important;
        width: 100% !important;
        padding-top: 61px !important; /* Mobil header yüksekliği + ekstra boşluk */
    }

    /* Mobil görünümde sidebar'ı düzenle */
    body.tutor-dashboard-page .tutor-dashboard-left-menu {
        position: fixed !important;
        width: 250px !important;
        left: -250px !important;
        transition: left 0.3s ease !important;

        /* Mobil görünümde de kaydırma çubuğu stilini koru - başlangıçta şeffaf olsun */
        scrollbar-width: thin !important;
        scrollbar-color: transparent transparent !important;
        -ms-overflow-style: auto !important;
    }

    /* Mobil görünümde Webkit için kaydırma çubuğu stilini ayarla */
    body.tutor-dashboard-page .tutor-dashboard-left-menu.show::-webkit-scrollbar-thumb {
        background-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 0) !important; /* Başlangıçta şeffaf kaydırma çubuğu */
        transition: background-color 0.2s ease !important; /* Geçiş efekti */
    }

    /* Mobil görünümde hover durumunda scrollbar'ı görünür yap */
    body.tutor-dashboard-page .tutor-dashboard-left-menu.show:hover::-webkit-scrollbar-thumb {
        background-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 0.3) !important; /* Hover durumunda görünür kaydırma çubuğu */
    }

    /* Mobil görünümde kaydırma çubuğunun üzerine gelindiğinde daha belirgin yap */
    body.tutor-dashboard-page .tutor-dashboard-left-menu.show::-webkit-scrollbar-thumb:hover {
        background-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 1) !important; /* Kaydırma çubuğu hover durumunda tam opaklık */
    }

    /* Mobil görünümde Firefox için hover durumunda scrollbar'ı görünür yap */
    body.tutor-dashboard-page .tutor-dashboard-left-menu.show:hover {
        scrollbar-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 0.3) transparent !important; /* Firefox için görünür kaydırma çubuğu */
    }

    /* Mobil görünümde logo konteyner stilleri */
    body.tutor-dashboard-page .tutor-dashboard-logo-container {
        border-bottom: 1px solid #e9ecef !important;
        margin-bottom: 5px !important; /* Mobil için daha az margin */
    }

    /* Mobil görünümde logo stilleri */
    body.tutor-dashboard-page .tutor-dashboard-logo {
        width: 180px !important;
        height: 80px !important; /* Mobil için yükseklik ayarlandı */
    }

    body.tutor-dashboard-page .tutor-dashboard-logo img {
        max-width: 100% !important; /* Genişlik 100% olarak ayarlandı */
        max-height: 100% !important; /* Yükseklik 100% olarak ayarlandı */
    }

    /* Mobil görünümde açık sidebar */
    body.tutor-dashboard-page .tutor-dashboard-left-menu.show {
        left: 0 !important;
    }

    /* Mobil görünümde içerik kenar boşluklarını düzenle */
    body.tutor-dashboard-page .tutor-dashboard-content-inner .tutor-dashboard-content-inner-table,
    body.tutor-dashboard-page .tutor-frontend-dashboard-header {
        margin-left: 15px !important;
        margin-right: 15px !important;
    }

    /* Mobil görünümde başlık alanları için margin ayarı */
    body.tutor-dashboard-page .tutor-dashboard-content-inner h3,
    body.tutor-dashboard-page .tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-mb-24,
    body.tutor-dashboard-page .tutor-dashboard-content-inner .tutor-fs-5,
    body.tutor-dashboard-page .tutor-dashboard-content-inner .tutor-dashboard-title,
    body.tutor-dashboard-page .tutor-fs-5.tutor-fw-medium.tutor-color-black,
    body.tutor-dashboard-page .header-title.tutor-fs-5.tutor-fw-medium.tutor-color-black,
    body.tutor-dashboard-page h3.tutor-fs-5,
    body.tutor-dashboard-page h2.tutor-fs-5,
    body.tutor-dashboard-page h1.tutor-fs-5 {
        display: block !important;
        font-size: 1.3rem !important;
        margin-bottom: 1.2rem !important;
        animation: none !important;
        transform: none !important;
        transition: none !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    /* Mobil görünümde row için margin ayarı */
    body.tutor-dashboard-page .tutor-dashboard-content-inner .tutor-row {
        /* margin-left: 15px !important; */ /* Sağa taşıma kodunu kapattık */
        /* margin-right: 15px !important; */ /* Sağa taşıma kodunu kapattık */
    }

    /* Mobil görünümde içerik padding'lerini düzenle */
    body.tutor-dashboard-page .tutor-dashboard-content {
        padding-left: 15px !important;
        padding-right: 15px !important;
    }

    /* Mobil görünümde header'ı düzenle */
    body.tutor-dashboard-page .tutor-frontend-dashboard-header,
    body.tutor-dashboard-page #tutor-page-wrap > div > div.tutor-container > div.tutor-row.tutor-d-flex.tutor-justify-between.tutor-frontend-dashboard-header {
        margin-left: 0 !important;
        padding: 15px 10px 15px 0px !important;
        border-bottom: 1px solid #e9ecef !important;
        width: 100% !important;
        left: 0 !important;
        position: fixed !important;
        justify-content: flex-end !important;
        background-color: #f9f9f9 !important;
    }

    /* Mobil görünümde sol header alanını gizle */
    body.tutor-dashboard-page .tutor-header-left-side.tutor-dashboard-header.tutor-col-md-6.tutor-d-flex.tutor-align-center {
        display: none !important;
    }

    body.tutor-dashboard-page .tutor-header-right-side {
        display: flex !important;
        flex-direction: row !important; /* Buton solda, profil sağda olacak */
        align-items: center !important;
        justify-content: flex-end !important;
        margin-top: 0px !important; /* Üst boşluğu kaldır */
        margin-bottom: 0px !important; /* Alt boşluğu kaldır */
        /* margin-right: 10px !important; */ /* Sağa doğru biraz daha boşluk ekle - KAPATILDI */
    }

    /* Mobil görünümde buton ve profil arasındaki boşluğu ayarla */
    body.tutor-dashboard-page .tutor-header-right-side .tutor-create-new-course,
    body.tutor-dashboard-page .tutor-header-right-side a.tutor-btn {
        margin-right: 10px !important;
        font-size: 12px !important; /* Mobil için daha küçük font */
        order: 1 !important; /* Buton önce gelsin */
    }

    body.tutor-dashboard-page .tutor-header-right-side .tutor-dashboard-header-avatar {
        margin-left: 10px !important;
        order: 2 !important; /* Avatar sonra gelsin */
    }

    body.tutor-dashboard-page .tutor-header-right-side .tutor-user-info {
        order: 3 !important; /* Kullanıcı bilgileri en sonda gelsin */
    }

    /* Mobil görünümde profil isminin boyutunu küçült */
    body.tutor-dashboard-page .tutor-dashboard-header-username,
    body.tutor-dashboard-page .tutor-header-right-side .tutor-user-info .tutor-fs-4 {
        font-size: 12px !important; /* Mobil için daha küçük font */
        line-height: 1.1 !important; /* Satır yüksekliğini daha da azalt */
    }

    /* Mobil görünümde avatar boyutunu ayarla */
    body.tutor-dashboard-page .tutor-avatar-xl,
    body.tutor-dashboard-page .tutor-dashboard-header-avatar .tutor-avatar.tutor-avatar-xl {
        width: 35px !important;
        height: 35px !important;
    }

    /* Admin bar varsa mobil görünümde header'ın konumunu ayarla */
    body.admin-bar.tutor-dashboard-page .tutor-frontend-dashboard-header,
    body.admin-bar.tutor-dashboard-page #tutor-page-wrap > div > div.tutor-container > div.tutor-row.tutor-d-flex.tutor-justify-between.tutor-frontend-dashboard-header {
        top: 46px !important; /* Mobil admin bar yüksekliği */
    }
}

/* Mobil için admin bar ayarı */
@media (max-width: 782px) {
    body.admin-bar.tutor-dashboard-page {
        margin-top: 46px !important; /* Mobil admin bar yüksekliği */
    }
}

/* Küçük mobil cihazlar için özel ayarlar */
@media screen and (max-width: 767.98px) {
    .tutor-header-right-side {
        margin-top: 0px !important; /* Üst boşluğu kaldır */
        margin-bottom: 0px !important; /* Alt boşluğu kaldır */
    }
}
