"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[979],{932:(t,e,r)=>{r.d(e,{Z:()=>Q});var n=r(917);var i=r(8003);var o=r.n(i);var a=r(9546);var u=r(7363);var c=r.n(u);var s=r(7536);var l=r(74);var d=r(5033);var f=r(5519);var m=r(7941);var p=r(9768);var _=r(8777);var v=r(8898);var g=r(9447);var h=r(8305);var y=r(6413);var b=r(1537);var w=r(5460);var E=r(2377);var O=r(6264);var Z=r(6873);var S=r(5589);var x=r(7034);var T=r(4900);var j=r(125);var L=r(7151);var D=r(9169);function P(t){"@babel/helpers - typeof";return P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},P(t)}function N(){N=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return N.apply(this,arguments)}function q(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */q=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function t(e,r,n){return e[r]=n}}function s(t,e,r,i){var o=e&&e.prototype instanceof f?e:f,a=Object.create(o.prototype),u=new S(i||[]);return n(a,"_invoke",{value:w(t,r,u)}),a}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=s;var d={};function f(){}function m(){}function p(){}var _={};c(_,o,(function(){return this}));var v=Object.getPrototypeOf,g=v&&v(v(x([])));g&&g!==e&&r.call(g,o)&&(_=g);var h=p.prototype=f.prototype=Object.create(_);function y(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function b(t,e){function i(n,o,a,u){var c=l(t[n],t,o);if("throw"!==c.type){var s=c.arg,d=s.value;return d&&"object"==P(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){i("next",t,a,u)}),(function(t){i("throw",t,a,u)})):e.resolve(d).then((function(t){s.value=t,a(s)}),(function(t){return i("throw",t,a,u)}))}u(c.arg)}var o;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){i(r,n,t,e)}))}return o=o?o.then(a,a):a()}})}function w(t,e,r){var n="suspendedStart";return function(i,o){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===i)throw o;return T()}for(r.method=i,r.arg=o;;){var a=r.delegate;if(a){var u=E(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var c=l(t,e,r);if("normal"===c.type){if(n=r.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(n="completed",r.method="throw",r.arg=c.arg)}}}function E(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,E(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var i=l(n,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,d;var o=i.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function O(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function Z(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(O,this),this.reset(!0)}function x(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return i.next=i}}return{next:T}}function T(){return{value:undefined,done:!0}}return m.prototype=p,n(h,"constructor",{value:p,configurable:!0}),n(p,"constructor",{value:m,configurable:!0}),m.displayName=c(p,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===m||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,p):(t.__proto__=p,c(t,u,"GeneratorFunction")),t.prototype=Object.create(h),t},t.awrap=function(t){return{__await:t}},y(b.prototype),c(b.prototype,a,(function(){return this})),t.AsyncIterator=b,t.async=function(e,r,n,i,o){void 0===o&&(o=Promise);var a=new b(s(e,r,n,i),o);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},y(h),c(h,u,"Generator"),c(h,o,(function(){return this})),c(h,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=x,S.prototype={constructor:S,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(Z),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function i(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],u=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),s=r.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function t(e,n){for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),Z(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var i=n.completion;if("throw"===i.type){var o=i.arg;Z(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:x(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function k(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function A(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?k(Object(r),!0).forEach((function(e){M(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):k(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function M(t,e,r){e=z(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function z(t){var e=F(t,"string");return P(e)==="symbol"?e:String(e)}function F(t,e){if(P(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(P(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function I(t,e,r,n,i,o,a){try{var u=t[o](a);var c=u.value}catch(t){r(t);return}if(u.done){e(c)}else{Promise.resolve(c).then(n,i)}}function C(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var o=t.apply(e,r);function a(t){I(o,n,i,a,u,"next",t)}function u(t){I(o,n,i,a,u,"throw",t)}a(undefined)}))}}var W=(0,x.zs)();var R=function t(e){var r,o,c;var b=e.onCancel,w=e.data,x=e.topicId,j=e.meetingId;var P=(0,O.a)({defaultValue:true}),k=P.ref,M=P.isScrolling;var z=(0,S.i0)(j?j:"",x?x:"");var F=w!==null&&w!==void 0?w:z.data;var I=(0,E.O)({defaultValues:{meeting_name:(r=F===null||F===void 0?void 0:F.post_title)!==null&&r!==void 0?r:"",meeting_summary:(o=F===null||F===void 0?void 0:F.post_content)!==null&&o!==void 0?o:"",meeting_start_date:F!==null&&F!==void 0&&F.meeting_data.start_datetime?(0,a["default"])(new Date(F.meeting_data.start_datetime),y.E_.yearMonthDay):"",meeting_start_time:F!==null&&F!==void 0&&F.meeting_data.start_datetime?(0,a["default"])(new Date(F.meeting_data.start_datetime),y.E_.hoursMinutes):"",meeting_end_date:F!==null&&F!==void 0&&F.meeting_data.end_datetime?(0,a["default"])(new Date(F.meeting_data.end_datetime),y.E_.yearMonthDay):"",meeting_end_time:F!==null&&F!==void 0&&F.meeting_data.end_datetime?(0,a["default"])(new Date(F.meeting_data.end_datetime),y.E_.hoursMinutes):"",meeting_timezone:(c=F===null||F===void 0?void 0:F.meeting_data.timezone)!==null&&c!==void 0?c:"",meeting_enrolledAsAttendee:(F===null||F===void 0?void 0:F.meeting_data.attendees)==="Yes"},shouldFocusError:true,mode:"onChange"});var R=(0,Z.pH)();var Q=h.y.timezones;var K=Object.keys(Q).map((function(t){return{label:Q[t],value:t}}));var Y=function(){var t=C(q().mark((function t(e){var r;return q().wrap((function t(n){while(1)switch(n.prev=n.next){case 0:if(W){n.next=2;break}return n.abrupt("return");case 2:n.next=4;return R.mutateAsync(A(A(A({},F&&{"post-id":Number(F.ID),"event-id":F.meeting_data.id}),x&&{topic_id:x}),{},{course_id:W,meeting_title:e.meeting_name,meeting_summary:e.meeting_summary,meeting_start_date:e.meeting_start_date,meeting_start_time:e.meeting_start_time,meeting_end_date:e.meeting_end_date,meeting_end_time:e.meeting_end_time,meeting_attendees_enroll_students:e.meeting_enrolledAsAttendee?"Yes":"No",meeting_timezone:e.meeting_timezone,attendees:e.meeting_enrolledAsAttendee?"Yes":"No"}));case 4:r=n.sent;if(r.status_code===200||r.status_code===201){b();I.reset()}case 6:case"end":return n.stop()}}),t)})));return function e(r){return t.apply(this,arguments)}}();(0,u.useEffect)((function(){if((0,L.$K)(F)){I.reset({meeting_name:F.post_title,meeting_summary:F.post_content,meeting_start_date:F.meeting_data.start_datetime?(0,a["default"])(new Date(F.meeting_data.start_datetime),y.E_.yearMonthDay):"",meeting_start_time:F.meeting_data.start_datetime?(0,a["default"])(new Date(F.meeting_data.start_datetime),y.E_.hoursMinutes):"",meeting_end_date:F.meeting_data.end_datetime?(0,a["default"])(new Date(F.meeting_data.end_datetime),y.E_.yearMonthDay):"",meeting_end_time:F.meeting_data.end_datetime?(0,a["default"])(new Date(F.meeting_data.end_datetime),y.E_.hoursMinutes):"",meeting_timezone:F.meeting_data.timezone,meeting_enrolledAsAttendee:F.meeting_data.attendees==="Yes"})}var t=setTimeout((function(){I.setFocus("meeting_name")}),250);return function(){clearTimeout(t)}}),[F]);return(0,n.tZ)("div",{css:G.container},(0,n.tZ)("div",{css:G.formWrapper,ref:k},(0,n.tZ)(T.Z,{when:!z.isLoading,fallback:(0,n.tZ)(d.fz,null)},(0,n.tZ)(s.Qr,{name:"meeting_name",control:I.control,rules:{required:(0,i.__)("Name is required","tutor")},render:function t(e){return(0,n.tZ)(p.Z,N({},e,{label:(0,i.__)("Meeting Name","tutor"),placeholder:(0,i.__)("Enter meeting name","tutor"),selectOnFocus:true}))}}),(0,n.tZ)(s.Qr,{name:"meeting_summary",control:I.control,rules:{required:(0,i.__)("Summary is required","tutor")},render:function t(e){return(0,n.tZ)(v.Z,N({},e,{label:(0,i.__)("Meeting Summary","tutor"),placeholder:(0,i.__)("Enter meeting summary","tutor"),rows:3,enableResize:true}))}}),(0,n.tZ)("div",{css:G.meetingDateTimeWrapper},(0,n.tZ)("div",{css:G.dateLabel},(0,i.__)("Meeting Start Date","tutor")),(0,n.tZ)("div",{css:G.meetingDateTime},(0,n.tZ)(s.Qr,{name:"meeting_start_date",control:I.control,rules:{required:(0,i.__)("Start date is required","tutor"),validate:D.Ek},render:function t(e){return(0,n.tZ)(m.Z,N({},e,{placeholder:(0,i.__)("Start date","tutor"),disabledBefore:(new Date).toISOString()}))}}),(0,n.tZ)(s.Qr,{name:"meeting_start_time",control:I.control,rules:{required:(0,i.__)("Start time is required","tutor"),validate:D.xB},render:function t(e){return(0,n.tZ)(g.Z,N({},e,{placeholder:(0,i.__)("Start time","tutor")}))}}))),(0,n.tZ)("div",{css:G.meetingDateTimeWrapper},(0,n.tZ)("div",{css:G.dateLabel},(0,i.__)("Meeting End Date","tutor")),(0,n.tZ)("div",{css:G.meetingDateTime},(0,n.tZ)(s.Qr,{name:"meeting_end_date",control:I.control,rules:{required:(0,i.__)("End date is required","tutor"),validate:{invalidDate:D.Ek,checkEndDate:function t(e){var r=I.watch("meeting_start_date");var n=e;if(r&&n){return new Date(r)>new Date(n)?(0,i.__)("End date should be greater than start date","tutor"):undefined}return undefined}},deps:["meeting_start_date"]},render:function t(e){return(0,n.tZ)(m.Z,N({},e,{placeholder:(0,i.__)("End date","tutor"),disabledBefore:I.watch("meeting_start_date")||(new Date).toISOString()}))}}),(0,n.tZ)(s.Qr,{name:"meeting_end_time",control:I.control,rules:{required:(0,i.__)("End time is required","tutor"),validate:{invalidTime:D.xB,checkEndTime:function t(e){var r=I.watch("meeting_start_date");var n=I.watch("meeting_start_time");var o=I.watch("meeting_end_date");var a=e;if(r&&o&&n&&a){return new Date("".concat(r," ").concat(n))>new Date("".concat(o," ").concat(a))?(0,i.__)("End time should be greater than start time","tutor"):undefined}return undefined}},deps:["meeting_start_time","meeting_start_date","meeting_end_date"]},render:function t(e){return(0,n.tZ)(g.Z,N({},e,{placeholder:(0,i.__)("End time","tutor")}))}}))),(0,n.tZ)(s.Qr,{name:"meeting_timezone",control:I.control,rules:{required:(0,i.__)("Timezone is required","tutor")},render:function t(e){return(0,n.tZ)(_.Z,N({},e,{label:(0,i.__)("Timezone","tutor"),placeholder:(0,i.__)("Select timezone","tutor"),options:K,selectOnFocus:true,isSearchable:true}))}}),(0,n.tZ)(s.Qr,{name:"meeting_enrolledAsAttendee",control:I.control,render:function t(e){return(0,n.tZ)(f.Z,N({},e,{label:(0,i.__)("Add enrolled students as attendees","tutor")}))}}))),(0,n.tZ)("div",{css:G.buttonWrapper({isScrolling:M})},(0,n.tZ)(l.Z,{variant:"text",size:"small",onClick:b},(0,i.__)("Cancel","tutor")),(0,n.tZ)(l.Z,{loading:R.isPending,variant:"primary",size:"small",onClick:I.handleSubmit(Y)},F||j?(0,i.__)("Update Meeting","tutor"):(0,i.__)("Create Meeting","tutor"))))};const Q=R;var G={container:(0,n.iv)(j.i.display.flex("column")," background:",b.Jv.background.white,";padding-block:",b.W0[12],";border-radius:",b.E0.card,";box-shadow:",b.AF.popover,";",w.c.caption("regular"),";*>label{font-size:",b.JB[15],";color:",b.Jv.text.title,";}"+(true?"":0),true?"":0),formWrapper:(0,n.iv)(j.i.display.flex("column"),";",j.i.overflowYAuto,";padding-inline:",b.W0[12],";padding-bottom:",b.W0[8],";gap:",b.W0[12],";height:400px;"+(true?"":0),true?"":0),dateLabel:(0,n.iv)(w.c.caption("medium")," color:",b.Jv.text.title,";"+(true?"":0),true?"":0),meetingDateTimeWrapper:(0,n.iv)(j.i.display.flex("column")," gap:",b.W0[6],";"+(true?"":0),true?"":0),meetingDateTime:(0,n.iv)(j.i.display.flex()," justify-content:space-between;align-items:flex-start;gap:",b.W0[6],";"+(true?"":0),true?"":0),buttonWrapper:function t(e){var r=e.isScrolling,i=r===void 0?false:r;return(0,n.iv)(j.i.display.flex()," padding-top:",b.W0[8],";padding-inline:",b.W0[12],";justify-content:flex-end;gap:",b.W0[8],";z-index:",b.W5.positive,";",i&&(0,n.iv)("box-shadow:",b.AF.scrollable,";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)}}},7100:(t,e,r)=>{r.d(e,{Z:()=>R});var n=r(917);var i=r(8003);var o=r.n(i);var a=r(9546);var u=r(7363);var c=r.n(u);var s=r(7536);var l=r(74);var d=r(5033);var f=r(7941);var m=r(9768);var p=r(8898);var _=r(9447);var v=r(1537);var g=r(5460);var h=r(2377);var y=r(6873);var b=r(5589);var w=r(7034);var E=r(8777);var O=r(8305);var Z=r(6413);var S=r(4900);var x=r(6264);var T=r(125);var j=r(7151);var L=r(9169);function D(t){"@babel/helpers - typeof";return D="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},D(t)}function P(){P=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return P.apply(this,arguments)}function N(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */N=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function t(e,r,n){return e[r]=n}}function s(t,e,r,i){var o=e&&e.prototype instanceof f?e:f,a=Object.create(o.prototype),u=new S(i||[]);return n(a,"_invoke",{value:w(t,r,u)}),a}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=s;var d={};function f(){}function m(){}function p(){}var _={};c(_,o,(function(){return this}));var v=Object.getPrototypeOf,g=v&&v(v(x([])));g&&g!==e&&r.call(g,o)&&(_=g);var h=p.prototype=f.prototype=Object.create(_);function y(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function b(t,e){function i(n,o,a,u){var c=l(t[n],t,o);if("throw"!==c.type){var s=c.arg,d=s.value;return d&&"object"==D(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){i("next",t,a,u)}),(function(t){i("throw",t,a,u)})):e.resolve(d).then((function(t){s.value=t,a(s)}),(function(t){return i("throw",t,a,u)}))}u(c.arg)}var o;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){i(r,n,t,e)}))}return o=o?o.then(a,a):a()}})}function w(t,e,r){var n="suspendedStart";return function(i,o){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===i)throw o;return T()}for(r.method=i,r.arg=o;;){var a=r.delegate;if(a){var u=E(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var c=l(t,e,r);if("normal"===c.type){if(n=r.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(n="completed",r.method="throw",r.arg=c.arg)}}}function E(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,E(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var i=l(n,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,d;var o=i.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function O(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function Z(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(O,this),this.reset(!0)}function x(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return i.next=i}}return{next:T}}function T(){return{value:undefined,done:!0}}return m.prototype=p,n(h,"constructor",{value:p,configurable:!0}),n(p,"constructor",{value:m,configurable:!0}),m.displayName=c(p,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===m||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,p):(t.__proto__=p,c(t,u,"GeneratorFunction")),t.prototype=Object.create(h),t},t.awrap=function(t){return{__await:t}},y(b.prototype),c(b.prototype,a,(function(){return this})),t.AsyncIterator=b,t.async=function(e,r,n,i,o){void 0===o&&(o=Promise);var a=new b(s(e,r,n,i),o);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},y(h),c(h,u,"Generator"),c(h,o,(function(){return this})),c(h,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=x,S.prototype={constructor:S,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(Z),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function i(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],u=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),s=r.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function t(e,n){for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),Z(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var i=n.completion;if("throw"===i.type){var o=i.arg;Z(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:x(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function q(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function k(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?q(Object(r),!0).forEach((function(e){A(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):q(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function A(t,e,r){e=M(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function M(t){var e=z(t,"string");return D(e)==="symbol"?e:String(e)}function z(t,e){if(D(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(D(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function F(t,e,r,n,i,o,a){try{var u=t[o](a);var c=u.value}catch(t){r(t);return}if(u.done){e(c)}else{Promise.resolve(c).then(n,i)}}function I(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var o=t.apply(e,r);function a(t){F(o,n,i,a,u,"next",t)}function u(t){F(o,n,i,a,u,"throw",t)}a(undefined)}))}}var C=(0,w.zs)();var W=function t(e){var r,o,c,v,g,w,T,D,q;var A=e.onCancel,M=e.data,z=e.meetingHost,F=e.topicId,W=e.meetingId;var R=(0,x.a)({defaultValue:true}),G=R.ref,K=R.isScrolling;var Y=(0,b.qY)(W?W:"",F?F:"");var U=M!==null&&M!==void 0?M:Y.data;var B=(r=(o=U===null||U===void 0?void 0:U.meeting_starts_at)!==null&&o!==void 0?o:U===null||U===void 0?void 0:U.meeting_data.start_time)!==null&&r!==void 0?r:"";var J=(0,h.O)({defaultValues:{meeting_name:(c=U===null||U===void 0?void 0:U.post_title)!==null&&c!==void 0?c:"",meeting_summary:(v=U===null||U===void 0?void 0:U.post_content)!==null&&v!==void 0?v:"",meeting_date:B?(0,a["default"])(new Date(B),Z.E_.yearMonthDay):"",meeting_time:B?(0,a["default"])(new Date(B),Z.E_.hoursMinutes):"",meeting_duration:U!==null&&U!==void 0&&U.meeting_data.duration?String(U===null||U===void 0?void 0:U.meeting_data.duration):"40",meeting_duration_unit:(g=U===null||U===void 0?void 0:U.meeting_data.duration_unit)!==null&&g!==void 0?g:"min",meeting_timezone:(w=U===null||U===void 0?void 0:U.meeting_data.timezone)!==null&&w!==void 0?w:"",auto_recording:(T=U===null||U===void 0?void 0:(D=U.meeting_data.settings)===null||D===void 0?void 0:D.auto_recording)!==null&&T!==void 0?T:"none",meeting_password:(q=U===null||U===void 0?void 0:U.meeting_data.password)!==null&&q!==void 0?q:"",meeting_host:Object.values(z)[0]},shouldFocusError:true,mode:"onChange"});var V=(0,y.Z4)();var H=O.y.timezones;var $=Object.keys(H).map((function(t){return{label:H[t],value:t}}));var X=function(){var t=I(N().mark((function t(e){var r;return N().wrap((function t(n){while(1)switch(n.prev=n.next){case 0:if(C){n.next=2;break}return n.abrupt("return");case 2:n.next=4;return V.mutateAsync(k(k(k({},U&&{meeting_id:Number(U.ID)}),F&&{topic_id:Number(F)}),{},{course_id:C,meeting_title:e.meeting_name,meeting_summary:e.meeting_summary,meeting_date:(0,a["default"])(new Date(e.meeting_date),Z.E_.yearMonthDay),meeting_time:e.meeting_time,meeting_duration:Number(e.meeting_duration),meeting_duration_unit:e.meeting_duration_unit,meeting_timezone:e.meeting_timezone,auto_recording:e.auto_recording,meeting_password:e.meeting_password,click_form:F?"course_builder":"metabox",meeting_host:Object.keys(z)[0]}));case 4:r=n.sent;if(r.data){A();J.reset()}case 6:case"end":return n.stop()}}),t)})));return function e(r){return t.apply(this,arguments)}}();(0,u.useEffect)((function(){if((0,j.$K)(U)){var t,e;J.reset({meeting_name:U.post_title,meeting_summary:U.post_content,meeting_date:B?(0,a["default"])(new Date(B),Z.E_.yearMonthDay):"",meeting_time:B?(0,a["default"])(new Date(B),Z.E_.hoursMinutes):"",meeting_duration:String(U.meeting_data.duration),meeting_duration_unit:U.meeting_data.duration_unit,meeting_timezone:U.meeting_data.timezone,auto_recording:(t=(e=U.meeting_data.settings)===null||e===void 0?void 0:e.auto_recording)!==null&&t!==void 0?t:"none",meeting_password:U.meeting_data.password,meeting_host:Object.values(z)[0]})}var r=setTimeout((function(){J.setFocus("meeting_name")}),250);return function(){clearTimeout(r)}}),[U]);return(0,n.tZ)("div",{css:Q.container},(0,n.tZ)("div",{css:Q.formWrapper,ref:G},(0,n.tZ)(S.Z,{when:!Y.isLoading,fallback:(0,n.tZ)(d.fz,null)},(0,n.tZ)(s.Qr,{name:"meeting_name",control:J.control,rules:{required:(0,i.__)("Name is required","tutor")},render:function t(e){return(0,n.tZ)(m.Z,P({},e,{label:(0,i.__)("Meeting Name","tutor"),placeholder:(0,i.__)("Enter meeting name","tutor"),selectOnFocus:true}))}}),(0,n.tZ)(s.Qr,{name:"meeting_summary",control:J.control,rules:{required:(0,i.__)("Summary is required","tutor")},render:function t(e){return(0,n.tZ)(p.Z,P({},e,{label:(0,i.__)("Meeting Summary","tutor"),placeholder:(0,i.__)("Enter meeting summary","tutor"),rows:3,enableResize:true}))}}),(0,n.tZ)("div",{css:Q.meetingDateTimeWrapper},(0,n.tZ)(s.Qr,{name:"meeting_date",control:J.control,rules:{required:(0,i.__)("Date is required","tutor")},render:function t(e){return(0,n.tZ)(f.Z,P({},e,{label:(0,i.__)("Meeting Date","tutor"),placeholder:(0,i.__)("Enter meeting date","tutor"),disabledBefore:(new Date).toISOString()}))}}),(0,n.tZ)(s.Qr,{name:"meeting_time",control:J.control,rules:{required:(0,i.__)("Time is required","tutor"),validate:L.xB},render:function t(e){return(0,n.tZ)(_.Z,P({},e,{placeholder:(0,i.__)("Start time","tutor")}))}}),(0,n.tZ)("div",{css:Q.meetingTimeWrapper},(0,n.tZ)(s.Qr,{name:"meeting_duration",control:J.control,rules:{required:(0,i.__)("Duration is required","tutor")},render:function t(e){return(0,n.tZ)(m.Z,P({},e,{label:(0,i.__)("Meeting Duration","tutor"),placeholder:(0,i.__)("Duration","tutor"),type:"number",selectOnFocus:true}))}}),(0,n.tZ)(s.Qr,{name:"meeting_duration_unit",control:J.control,rules:{required:(0,i.__)("Duration unit is required","tutor")},render:function t(e){return(0,n.tZ)(E.Z,P({},e,{label:(0,n.tZ)("span",null," "),options:[{label:(0,i.__)("Minutes","tutor"),value:"min"},{label:(0,i.__)("Hours","tutor"),value:"hr"}]}))}}))),(0,n.tZ)(s.Qr,{name:"meeting_timezone",control:J.control,rules:{required:(0,i.__)("Timezone is required","tutor")},render:function t(e){return(0,n.tZ)(E.Z,P({},e,{label:(0,i.__)("Timezone","tutor"),placeholder:(0,i.__)("Select timezone","tutor"),options:$,selectOnFocus:true,isSearchable:true}))}}),(0,n.tZ)(s.Qr,{name:"auto_recording",control:J.control,rules:{required:(0,i.__)("Auto recording is required","tutor")},render:function t(e){return(0,n.tZ)(E.Z,P({},e,{label:(0,i.__)("Auto Recording","tutor"),placeholder:(0,i.__)("Select auto recording option","tutor"),options:[{label:(0,i.__)("No recordings","tutor"),value:"none"},{label:(0,i.__)("Local","tutor"),value:"local"},{label:(0,i.__)("Cloud","tutor"),value:"cloud"}]}))}}),(0,n.tZ)(s.Qr,{name:"meeting_password",control:J.control,rules:{required:(0,i.__)("Password is required","tutor")},render:function t(e){return(0,n.tZ)(m.Z,P({},e,{label:(0,i.__)("Meeting Password","tutor"),placeholder:(0,i.__)("Enter meeting password","tutor"),type:"password",isPassword:true,selectOnFocus:true}))}}),(0,n.tZ)(s.Qr,{name:"meeting_host",control:J.control,rules:{required:(0,i.__)("Meeting host is required","tutor")},render:function t(e){return(0,n.tZ)(m.Z,P({},e,{label:(0,i.__)("Meeting Host","tutor"),placeholder:(0,i.__)("Enter meeting host","tutor"),disabled:true,selectOnFocus:true}))}}))),(0,n.tZ)("div",{css:Q.buttonWrapper({isScrolling:K})},(0,n.tZ)(l.Z,{variant:"text",size:"small",onClick:A},(0,i.__)("Cancel","tutor")),(0,n.tZ)(l.Z,{loading:V.isPending,variant:"primary",size:"small",onClick:J.handleSubmit(X)},U||W?(0,i.__)("Update Meeting","tutor"):(0,i.__)("Create Meeting","tutor"))))};const R=W;var Q={container:(0,n.iv)(T.i.display.flex("column")," background:",v.Jv.background.white,";padding-block:",v.W0[12],";border-radius:",v.E0.card,";box-shadow:",v.AF.popover,";",g.c.caption("regular"),";*>label{font-size:",v.JB[15],";color:",v.Jv.text.title,";}"+(true?"":0),true?"":0),formWrapper:(0,n.iv)(T.i.display.flex("column"),";",T.i.overflowYAuto,";padding-inline:",v.W0[12],";padding-bottom:",v.W0[8],";gap:",v.W0[12],";height:400px;"+(true?"":0),true?"":0),meetingDateTimeWrapper:(0,n.iv)(T.i.display.flex("column")," gap:",v.W0[6],";"+(true?"":0),true?"":0),meetingTimeWrapper:(0,n.iv)(T.i.display.flex()," justify-content:space-between;align-items:flex-start;gap:",v.W0[6],";"+(true?"":0),true?"":0),buttonWrapper:function t(e){var r=e.isScrolling,i=r===void 0?false:r;return(0,n.iv)(T.i.display.flex()," padding-top:",v.W0[8],";padding-inline:",v.W0[12],";justify-content:flex-end;gap:",v.W0[8],";z-index:",v.W5.positive,";",i&&(0,n.iv)("box-shadow:",v.AF.scrollable,";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)}}},2322:(t,e,r)=>{r.d(e,{Z:()=>m});var n=r(917);var i=r(9250);var o=r(74);var a=r(6595);var u=r(6413);var c=r(1537);var s=r(5460);var l=r(4900);function d(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var f=function t(e){var r=e.title,c=e.backUrl,s=e.rightButton,d=e.isExternalUrl;var f=(0,i.s0)();var m=function t(){if(c){if(d){window.location.href=c;return}f(c)}else{f(-1)}};return(0,n.tZ)("div",{css:_.wrapper},(0,n.tZ)("div",{css:_.left},(0,n.tZ)(l.Z,{when:c},(0,n.tZ)(o.Z,{variant:"text",buttonCss:_.button({isRTL:u.dZ}),onClick:m},(0,n.tZ)(a.Z,{name:"back",width:32,height:32}))),(0,n.tZ)("h6",{css:_.title},r)),s)};const m=f;var p=true?{name:"21xn5r",styles:"transform:rotate(180deg)"}:0;var _={wrapper:true?{name:"bcffy2",styles:"display:flex;align-items:center;justify-content:space-between"}:0,left:(0,n.iv)("display:flex;align-items:center;gap:",c.W0[16],";"+(true?"":0),true?"":0),button:function t(e){var r=e.isRTL;return(0,n.iv)("padding:0;border-radius:",c.E0[2],";",r&&p,";"+(true?"":0),true?"":0)},title:(0,n.iv)(s.c.heading6("medium"),";"+(true?"":0),true?"":0)}},5589:(t,e,r)=>{r.d(e,{AI:()=>C,CR:()=>G,FV:()=>D,G4:()=>M,PI:()=>F,P_:()=>Y,SF:()=>S,T3:()=>R,Wn:()=>N,i0:()=>V,iL:()=>x,qY:()=>B,rM:()=>j,ri:()=>$,yO:()=>k});var n=r(8551);var i=r(202);var o=r(249);var a=r(8003);var u=r.n(a);var c=r(3389);var s=r(6413);var l=r(7307);var d=r(3603);var f=r(5219);function m(t){"@babel/helpers - typeof";return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},m(t)}function p(t,e){return y(t)||h(t,e)||v(t,e)||_()}function _(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function v(t,e){if(!t)return;if(typeof t==="string")return g(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return g(t,e)}function g(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function h(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],c=!0,s=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){s=!0,i=t}finally{try{if(!c&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(s)throw i}}return u}}function y(t){if(Array.isArray(t))return t}function b(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function w(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?b(Object(r),!0).forEach((function(e){E(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function E(t,e,r){e=O(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function O(t){var e=Z(t,"string");return m(e)==="symbol"?e:String(e)}function Z(t,e){if(m(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(m(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var S=function t(e,r,n,i,o){var a,u;return w(w(w(w(w(w(w({},r&&{lesson_id:r}),{},{topic_id:n,title:e.title,description:e.description,thumbnail_id:(a=(u=e.thumbnail)===null||u===void 0?void 0:u.id)!==null&&a!==void 0?a:null},e.video?Object.fromEntries(Object.entries(e.video).map((function(t){var r;var n=p(t,2),i=n[0],o=n[1];return["video[".concat(i,"]"),i==="source"&&!o?"-1":i==="poster_url"&&!((r=e.video)!==null&&r!==void 0&&r.poster)?"":o]}))):{}),{},{"video[runtime][hours]":e.duration.hour||0,"video[runtime][minutes]":e.duration.minute||0,"video[runtime][seconds]":e.duration.second||0},(0,f.ro)(s.AO.TUTOR_COURSE_PREVIEW)&&{_is_preview:e.lesson_preview?1:0}),{},{tutor_attachments:(e.tutor_attachments||[]).map((function(t){return t.id}))},(0,f.ro)(s.AO.CONTENT_DRIP)&&i==="unlock_by_date"&&{"content_drip_settings[unlock_date]":e.content_drip_settings.unlock_date||""}),(0,f.ro)(s.AO.CONTENT_DRIP)&&i==="specific_days"&&{"content_drip_settings[after_xdays_of_enroll]":e.content_drip_settings.after_xdays_of_enroll||"0"}),(0,f.ro)(s.AO.CONTENT_DRIP)&&i==="after_finishing_prerequisites"&&{"content_drip_settings[prerequisites]":e.content_drip_settings.prerequisites||[]}),Object.fromEntries(o.map((function(t){return[t,e[t]||""]}))))};var x=function t(e,r,n,i,o){return w(w(w(w(w({},r&&{assignment_id:r}),{},{topic_id:n,title:e.title,summary:e.summary,attachments:(e.attachments||[]).map((function(t){return t.id})),"assignment_option[time_duration][time]":e.time_duration.time,"assignment_option[time_duration][value]":e.time_duration.value,"assignment_option[total_mark]":e.total_mark,"assignment_option[pass_mark]":e.pass_mark,"assignment_option[upload_files_limit]":e.upload_files_limit,"assignment_option[upload_file_size_limit]":e.upload_file_size_limit},(0,f.ro)(s.AO.CONTENT_DRIP)&&i==="unlock_by_date"&&{"content_drip_settings[unlock_date]":e.content_drip_settings.unlock_date||""}),(0,f.ro)(s.AO.CONTENT_DRIP)&&i==="specific_days"&&{"content_drip_settings[after_xdays_of_enroll]":e.content_drip_settings.after_xdays_of_enroll||"0"}),(0,f.ro)(s.AO.CONTENT_DRIP)&&i==="after_finishing_prerequisites"&&{"content_drip_settings[prerequisites]":e.content_drip_settings.prerequisites||[]}),Object.fromEntries(o.map((function(t){return[t,e[t]||""]}))))};var T=function t(e){return l.R.get(d.Z.GET_COURSE_CONTENTS,{params:{course_id:e}})};var j=function t(e){return(0,n.a)({queryKey:["Topic",e],queryFn:function t(){return T(e).then((function(t){return t.data.map((function(t){return w(w({},t),{},{contents:t.contents.map((function(t){return w(w({},t),{},{post_type:t.quiz_type?"tutor_h5p_quiz":t.post_type})}))})}))}))},enabled:!!e})};var L=function t(e){return l.R.post(d.Z.SAVE_TOPIC,e)};var D=function t(){var e=(0,i.NL)();var r=(0,c.p)(),n=r.showToast;return(0,o.D)({mutationFn:L,onSuccess:function t(r){if(r.data){n({message:(0,a.__)("Topic saved successfully","tutor"),type:"success"});e.invalidateQueries({queryKey:["Topic"]})}},onError:function t(e){n({type:"danger",message:(0,f.Mo)(e)})}})};var P=function t(e){return l.R.post(d.Z.DELETE_TOPIC,{topic_id:e})};var N=function t(e){var r=(0,i.NL)();var n=(0,c.p)(),u=n.showToast;return(0,o.D)({mutationFn:P,onSuccess:function t(n,i){if(n.status_code===200){u({message:(0,a.__)(n.message,"tutor"),type:"success"});r.setQueryData(["Topic",e],(function(t){var e=JSON.parse(JSON.stringify(t));return e.filter((function(t){return String(t.id)!==String(i)}))}))}},onError:function t(e){u({type:"danger",message:(0,f.Mo)(e)});r.invalidateQueries({queryKey:["Topic"]})}})};var q=function t(e,r){return l.R.get(d.Z.GET_LESSON_DETAILS,{params:{topic_id:r,lesson_id:e}})};var k=function t(e,r){return(0,n.a)({queryKey:["Lesson",e,r],queryFn:function t(){return q(e,r).then((function(t){return t.data}))},enabled:!!e&&!!r})};var A=function t(e){return l.R.post(d.Z.SAVE_LESSON,e)};var M=function t(e){var r=(0,i.NL)();var n=(0,c.p)(),u=n.showToast;return(0,o.D)({mutationFn:function t(e){return A(e)},onSuccess:function t(n,i){if(n.data){r.invalidateQueries({queryKey:["Topic",e]});r.invalidateQueries({queryKey:["Lesson",i.lesson_id,i.topic_id]});u({message:(0,a.__)("Lesson saved successfully","tutor"),type:"success"})}},onError:function t(e){u({type:"danger",message:(0,f.Mo)(e)})}})};var z=function t(e){return l.R.post(d.Z.DELETE_TOPIC_CONTENT,{lesson_id:e})};var F=function t(){var e=(0,i.NL)();var r=(0,c.p)(),n=r.showToast;return(0,o.D)({mutationFn:z,onSuccess:function t(r){if(r.status_code===200){n({message:(0,a.__)(r.message,"tutor"),type:"success"});e.invalidateQueries({queryKey:["Topic"]})}},onError:function t(e){n({type:"danger",message:(0,f.Mo)(e)})}})};var I=function t(e){return l.R.post(d.Z.UPDATE_COURSE_CONTENT_ORDER,e)};var C=function t(){var e=(0,c.p)(),r=e.showToast;return(0,o.D)({mutationFn:I,onError:function t(e){r({type:"danger",message:(0,f.Mo)(e)})}})};var W=function t(e,r){return l.R.get(d.Z.GET_ASSIGNMENT_DETAILS,{params:{topic_id:r,assignment_id:e}})};var R=function t(e,r){return(0,n.a)({queryKey:["Assignment",e,r],queryFn:function t(){return W(e,r).then((function(t){return t.data}))},enabled:!!e&&!!r})};var Q=function t(e){return l.R.post(d.Z.SAVE_ASSIGNMENT,e)};var G=function t(e){var r=(0,i.NL)();var n=(0,c.p)(),u=n.showToast;return(0,o.D)({mutationFn:function t(e){return Q(e)},onSuccess:function t(n,i){if(n.status_code===200||n.status_code===201){r.invalidateQueries({queryKey:["Topic",Number(e)]});r.invalidateQueries({queryKey:["Assignment",i.assignment_id,i.topic_id]});u({message:(0,a.__)(n.message,"tutor"),type:"success"})}},onError:function t(e){u({type:"danger",message:(0,f.Mo)(e)})}})};var K=function t(e){return l.R.post(d.Z.DUPLICATE_CONTENT,e)};var Y=function t(e){var r=(0,i.NL)();var n=(0,c.p)(),u=n.showToast;return(0,o.D)({mutationFn:K,onSuccess:function t(n,i){if(n.status_code===200||n.status_code===201){u({message:(0,a.__)(n.message,"tutor"),type:"success"});if(["lesson","assignment","quiz","topic"].includes(i.content_type)){r.invalidateQueries({queryKey:["Topic"]});return}if(["question"].includes(i.content_type)){r.invalidateQueries({queryKey:["Quiz",e]});return}}},onError:function t(n,i){u({message:(0,f.Mo)(n),type:"danger"});if(["answer"].includes(i.content_type)){r.invalidateQueries({queryKey:["Quiz",e]})}}})};var U=function t(e,r){return l.R.get(d.Z.GET_ZOOM_MEETING_DETAILS,{params:{meeting_id:e,topic_id:r}})};var B=function t(e,r){return(0,n.a)({queryKey:["ZoomMeeting",e],queryFn:function t(){return U(e,r).then((function(t){return t.data}))},enabled:!!e&&!!r})};var J=function t(e,r){return l.R.get(d.Z.GET_GOOGLE_MEET_DETAILS,{params:{meeting_id:e,topic_id:r}})};var V=function t(e,r){return(0,n.a)({queryKey:["GoogleMeet",e],queryFn:function t(){return J(e,r).then((function(t){return t.data}))},enabled:!!e&&!!r})};var H=function t(e){return l.R.post(d.Z.GET_H5P_LESSON_CONTENT,{search_filter:e})};var $=function t(e,r){return(0,n.a)({queryKey:["H5PLessonContents",e],queryFn:function t(){return H(e).then((function(t){return t.data}))},enabled:r==="lesson"})}},2676:(t,e,r)=>{r.d(e,{Z:()=>n});const n=r.p+"images/9b6a9d99adc17e88c06f2ee6fb3c387e-not-found-2x.webp"},1604:(t,e,r)=>{r.d(e,{Z:()=>n});const n=r.p+"images/743768c1bc7847c4dff86a04d365c429-not-found.webp"}}]);