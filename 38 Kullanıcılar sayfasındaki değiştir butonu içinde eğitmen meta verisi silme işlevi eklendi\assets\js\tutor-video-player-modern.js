/**
 * Tutor Video Oynatıcı Modern JavaScript
 * Video oynatıcıyı modern etkileşimler ve özelliklerle geliştirir
 */

(function($) {
    'use strict';

    // Belge hazır olduğunda başlat
    $(document).ready(function() {
        initModernVideoPlayer();
    });

    /**
     * Modern video oynatıcı geliştirmelerini başlat
     */
    function initModernVideoPlayer() {
        // Yükleme döndürme simgesini gizle
        $('.tutor-video-player .loading-spinner').hide();

        // Mobil deneyim için özel kontroller ekle
        if (window.matchMedia('(max-width: 767px)').matches) {
            enhanceMobileExperience();
        }

        // Tam ekrana geçerken/çıkarken yumuşak geçiş ekle
        document.addEventListener('fullscreenchange', handleFullscreenChange);
        document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
        document.addEventListener('mozfullscreenchange', handleFullscreenChange);
        document.addEventListener('MSFullscreenChange', handleFullscreenChange);
    }

    /**
     * Tam ekran değişiklik olaylarını işle
     */
    function handleFullscreenChange() {
        const isFullscreen = document.fullscreenElement ||
                           document.webkitFullscreenElement ||
                           document.mozFullScreenElement ||
                           document.msFullscreenElement;

        if (isFullscreen) {
            $(isFullscreen).addClass('tutor-fullscreen-active');
        } else {
            $('.tutor-fullscreen-active').removeClass('tutor-fullscreen-active');
        }
    }

    /**
     * Mobil deneyimi özel kontrollerle geliştir
     */
    function enhanceMobileExperience() {
        // Çift dokunma ile ileri/geri sarma işlevini ekle
        $('.tutor-video-player video').each(function() {
            const video = $(this)[0];
            let lastTapTime = 0;

            $(this).on('touchend', function(e) {
                const currentTime = new Date().getTime();
                const tapLength = currentTime - lastTapTime;

                if (tapLength < 500 && tapLength > 0) {
                    // Çift dokunma algılandı
                    const screenWidth = $(this).width();
                    const tapPosition = e.originalEvent.changedTouches[0].pageX;

                    if (tapPosition < screenWidth / 2) {
                        // Sol taraf - 10 saniye geri git
                        video.currentTime = Math.max(0, video.currentTime - 10);
                    } else {
                        // Sağ taraf - 10 saniye ileri git
                        video.currentTime = Math.min(video.duration, video.currentTime + 10);
                    }

                    e.preventDefault();
                }

                lastTapTime = currentTime;
            });
        });
    }

})(jQuery);