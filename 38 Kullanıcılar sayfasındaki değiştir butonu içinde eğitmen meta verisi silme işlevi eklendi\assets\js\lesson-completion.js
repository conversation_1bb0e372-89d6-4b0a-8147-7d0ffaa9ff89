/**
 * Ders tamamlama işlemlerini yöneten JavaScript
 * Bu script, ders tamamlama butonunun davranışını değiştirir
 * Buton tıklandığında AJAX ile ders tamamlama/tamamlamayı geri alma işlemlerini yapar
 */

(function($) {
    'use strict';

    // DOM yüklendiğinde çalışacak
    $(document).ready(function() {
        initLessonCompletionButton();
    });

    /**
     * Ders tamamlama butonunu başlat
     */
    function initLessonCompletionButton() {
        // Ders tamamlama butonuna tıklama olayı ekle
        $(document).on('click', '.lesson-completion-btn', function(e) {
            e.preventDefault();

            const button = $(this);
            const lessonId = button.data('lesson-id');
            const isCompleted = button.data('completed') === 'true';

            // Butonun durumunu değiştir (tıklanamaz yap)
            button.prop('disabled', true);

            // AJAX isteği gönder
            $.ajax({
                url: tutor_data.ajaxurl,
                type: 'POST',
                data: {
                    action: 'toggle_lesson_completion',
                    lesson_id: lessonId,
                    is_completed: isCompleted,
                    _wpnonce: tutor_data.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Butonun durumunu güncelle
                        updateButtonState(button, !isCompleted);

                        // Sidebar'daki tamamlanma işaretini güncelle
                        updateSidebarCompletionStatus(lessonId, !isCompleted);

                        // İlerleme çubuğunu güncelle
                        updateProgressBar();
                    } else {
                        console.error('İşlem başarısız:', response.data);
                    }

                    // Butonu tekrar tıklanabilir yap
                    button.prop('disabled', false);
                },
                error: function(xhr, status, error) {
                    console.error('AJAX hatası:', status, error);

                    // Butonu tekrar tıklanabilir yap
                    button.prop('disabled', false);
                }
            });
        });
    }

    /**
     * Butonun durumunu güncelle
     *
     * @param {jQuery} button - Buton elementi
     * @param {boolean} isCompleted - Tamamlanma durumu
     */
    function updateButtonState(button, isCompleted) {
        // Butonun data özelliğini güncelle
        button.data('completed', isCompleted ? 'true' : 'false');

        // Butonun sınıfını güncelle
        if (isCompleted) {
            button.removeClass('tutor-btn-primary').addClass('tutor-btn-success');
            button.find('.completion-btn-text').text('Tamamlandı');
        } else {
            button.removeClass('tutor-btn-success').addClass('tutor-btn-primary');
            button.find('.completion-btn-text').text('Dersi tamamla');
        }
    }

    /**
     * Sidebar'daki tamamlanma işaretini güncelle
     *
     * @param {number} lessonId - Ders ID'si
     * @param {boolean} isCompleted - Tamamlanma durumu
     */
    function updateSidebarCompletionStatus(lessonId, isCompleted) {
        console.log('Sidebar güncelleniyor, ders ID:', lessonId, 'Tamamlandı mı:', isCompleted);

        // Önce tüm sidebar öğelerini al
        const allSidebarItems = $('.tutor-course-topic-item');
        console.log('Toplam sidebar öğe sayısı:', allSidebarItems.length);

        // Mevcut URL'den ders ID'sini al
        const currentUrl = window.location.href;
        const currentLessonId = lessonId.toString();

        // Sidebar'daki ilgili ders öğesini bul - href içinde ders ID'sini ara
        allSidebarItems.each(function() {
            const item = $(this);
            const link = item.find('a').first();
            const href = link.attr('href') || '';

            // Ders ID'si URL'de var mı kontrol et
            if (href.includes('/' + currentLessonId) ||
                href.includes('lesson_id=' + currentLessonId) ||
                href.includes('post=' + currentLessonId)) {

                console.log('Eşleşen sidebar öğesi bulundu:', href);

                // Checkbox'u bul
                const checkbox = item.find('.tutor-form-check-input');

                if (checkbox.length) {
                    console.log('Checkbox bulundu, durumu güncelleniyor:', isCompleted ? 'tamamlandı' : 'tamamlanmadı');

                    // Checkbox'u güncelle
                    if (isCompleted) {
                        checkbox.prop('checked', true);
                        checkbox.attr('checked', 'checked');
                    } else {
                        checkbox.prop('checked', false);
                        checkbox.removeAttr('checked');
                    }

                    // Checkbox'un görünürlüğünü zorla güncelle
                    setTimeout(function() {
                        if (isCompleted) {
                            checkbox.addClass('checked-visible');
                        } else {
                            checkbox.removeClass('checked-visible');
                        }
                    }, 50);
                } else {
                    console.log('Checkbox bulunamadı');
                }
            }
        });

        // Alternatif yöntem: Ders ID'sine göre doğrudan seçici kullan
        const lessonCheckboxes = $('.tutor-course-topic-item-lesson').filter(function() {
            const dataId = $(this).data('lesson-id') || '';
            return dataId.toString() === currentLessonId;
        }).find('.tutor-form-check-input');

        if (lessonCheckboxes.length) {
            console.log('Data-lesson-id ile checkbox bulundu, sayı:', lessonCheckboxes.length);
            lessonCheckboxes.each(function() {
                if (isCompleted) {
                    $(this).prop('checked', true);
                    $(this).attr('checked', 'checked');
                } else {
                    $(this).prop('checked', false);
                    $(this).removeAttr('checked');
                }
            });
        }
    }

    /**
     * İlerleme çubuğunu güncelle
     */
    function updateProgressBar() {
        // Mevcut ders ID'sini al
        const currentLessonId = $('.lesson-completion-btn').data('lesson-id');

        // Sayfayı yenilemeden ilerleme çubuğunu güncellemek için AJAX isteği gönder
        $.ajax({
            url: tutor_data.ajaxurl,
            type: 'POST',
            data: {
                action: 'get_course_progress',
                lesson_id: currentLessonId
            },
            success: function(response) {
                if (response.success && response.data) {
                    console.log('Kurs ilerleme durumu:', response.data);

                    // İlerleme çubuğunu güncelle
                    const progressValue = response.data.completed_percent;

                    // Zorla görünür güncelleme için CSS değişkenini doğrudan güncelle
                    document.documentElement.style.setProperty('--tutor-progress-value', progressValue + '%');

                    // Tüm ilerleme çubuklarını güncelle
                    $('.tutor-progress-bar').each(function() {
                        // Önce stil özelliğini kaldır, sonra yeniden ekle (zorla yeniden render için)
                        $(this).css('--tutor-progress-value', '');
                        setTimeout(() => {
                            $(this).css('--tutor-progress-value', progressValue + '%');
                        }, 10);
                    });

                    // Yüzde metinlerini güncelle
                    $('.progress-percentage .tutor-fw-medium, .tutor-fs-7 .tutor-fw-medium, .tutor-color-muted .tutor-fw-medium').each(function() {
                        $(this).text(progressValue + '%');
                    });

                    // Sidebar'daki ilerleme yüzdesini güncelle
                    $('.tutor-fs-7.tutor-color-muted span').each(function() {
                        const $this = $(this);
                        if ($this.text().includes('%')) {
                            $this.html(progressValue + '% <span>Complete</span>');
                        }
                    });

                    // Mobil görünümdeki ilerleme çubuğunu güncelle
                    $('.tutor-spotlight-mobile-progress-complete .tutor-progress-bar').css('--tutor-progress-value', progressValue + '%');
                    $('.tutor-spotlight-mobile-progress-complete .tutor-fs-7').html(progressValue + '% <span>Complete</span>');

                    // Zorla yeniden render için ilerleme çubuklarına animasyon ekle
                    $('.tutor-progress-bar').addClass('tutor-progress-updated');
                    setTimeout(() => {
                        $('.tutor-progress-bar').removeClass('tutor-progress-updated');
                    }, 300);
                }
            },
            error: function(xhr, status, error) {
                console.error('Kurs ilerleme durumu alınamadı:', error);
            }
        });
    }

})(jQuery);
