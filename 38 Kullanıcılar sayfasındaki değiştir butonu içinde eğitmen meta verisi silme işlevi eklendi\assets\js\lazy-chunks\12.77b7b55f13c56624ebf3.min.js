"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[12],{9752:(e,t,n)=>{n.d(t,{LB:()=>Rt,y9:()=>Ht,g4:()=>be,Lg:()=>we,uN:()=>Xe,we:()=>De,pE:()=>M,ey:()=>O,VK:()=>J,_8:()=>I,hI:()=>W,Cj:()=>It,O1:()=>St,Zj:()=>Ot,VT:()=>y,Dy:()=>w});var r=n(7363);var o=n.n(r);var i=n(1533);var s=n(4285);const c={display:"none"};function a(e){let{id:t,value:n}=e;return o().createElement("div",{id:t,style:c},n)}function l(e){let{id:t,announcement:n,ariaLiveType:r="assertive"}=e;const i={position:"fixed",width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"};return o().createElement("div",{id:t,style:i,role:"status","aria-live":r,"aria-atomic":true},n)}function u(){const[e,t]=(0,r.useState)("");const n=(0,r.useCallback)((e=>{if(e!=null){t(e)}}),[]);return{announce:n,announcement:e}}const d=(0,r.createContext)(null);function f(e){const t=(0,r.useContext)(d);(0,r.useEffect)((()=>{if(!t){throw new Error("useDndMonitor must be used within a children of <DndContext>")}const n=t(e);return n}),[e,t])}function h(){const[e]=(0,r.useState)((()=>new Set));const t=(0,r.useCallback)((t=>{e.add(t);return()=>e.delete(t)}),[e]);const n=(0,r.useCallback)((t=>{let{type:n,event:r}=t;e.forEach((e=>{var t;return(t=e[n])==null?void 0:t.call(e,r)}))}),[e]);return[n,t]}const g={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "};const v={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;if(n){return"Draggable item "+t.id+" was moved over droppable area "+n.id+"."}return"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;if(n){return"Draggable item "+t.id+" was dropped over droppable area "+n.id}return"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function p(e){let{announcements:t=v,container:n,hiddenTextDescribedById:c,screenReaderInstructions:d=g}=e;const{announce:h,announcement:p}=u();const b=(0,s.Ld)("DndLiveRegion");const[m,y]=(0,r.useState)(false);(0,r.useEffect)((()=>{y(true)}),[]);f((0,r.useMemo)((()=>({onDragStart(e){let{active:n}=e;h(t.onDragStart({active:n}))},onDragMove(e){let{active:n,over:r}=e;if(t.onDragMove){h(t.onDragMove({active:n,over:r}))}},onDragOver(e){let{active:n,over:r}=e;h(t.onDragOver({active:n,over:r}))},onDragEnd(e){let{active:n,over:r}=e;h(t.onDragEnd({active:n,over:r}))},onDragCancel(e){let{active:n,over:r}=e;h(t.onDragCancel({active:n,over:r}))}})),[h,t]));if(!m){return null}const w=o().createElement(o().Fragment,null,o().createElement(a,{id:c,value:d.draggable}),o().createElement(l,{id:b,announcement:p}));return n?(0,i.createPortal)(w,n):w}var b;(function(e){e["DragStart"]="dragStart";e["DragMove"]="dragMove";e["DragEnd"]="dragEnd";e["DragCancel"]="dragCancel";e["DragOver"]="dragOver";e["RegisterDroppable"]="registerDroppable";e["SetDroppableDisabled"]="setDroppableDisabled";e["UnregisterDroppable"]="unregisterDroppable"})(b||(b={}));function m(){}function y(e,t){return(0,r.useMemo)((()=>({sensor:e,options:t!=null?t:{}})),[e,t])}function w(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++){t[n]=arguments[n]}return(0,r.useMemo)((()=>[...t].filter((e=>e!=null))),[...t])}const x=Object.freeze({x:0,y:0});function R(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function C(e,t){const n=(0,s.DC)(e);if(!n){return"0 0"}const r={x:(n.x-t.left)/t.width*100,y:(n.y-t.top)/t.height*100};return r.x+"% "+r.y+"%"}function E(e,t){let{data:{value:n}}=e;let{data:{value:r}}=t;return n-r}function D(e,t){let{data:{value:n}}=e;let{data:{value:r}}=t;return r-n}function S(e){let{left:t,top:n,height:r,width:o}=e;return[{x:t,y:n},{x:t+o,y:n},{x:t,y:n+r},{x:t+o,y:n+r}]}function I(e,t){if(!e||e.length===0){return null}const[n]=e;return t?n[t]:n}function N(e,t,n){if(t===void 0){t=e.left}if(n===void 0){n=e.top}return{x:t+e.width*.5,y:n+e.height*.5}}const M=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=N(t,t.left,t.top);const i=[];for(const e of r){const{id:t}=e;const r=n.get(t);if(r){const n=R(N(r),o);i.push({id:t,data:{droppableContainer:e,value:n}})}}return i.sort(E)};const O=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=S(t);const i=[];for(const e of r){const{id:t}=e;const r=n.get(t);if(r){const n=S(r);const s=o.reduce(((e,t,r)=>e+R(n[r],t)),0);const c=Number((s/4).toFixed(4));i.push({id:t,data:{droppableContainer:e,value:c}})}}return i.sort(E)};function k(e,t){const n=Math.max(t.top,e.top);const r=Math.max(t.left,e.left);const o=Math.min(t.left+t.width,e.left+e.width);const i=Math.min(t.top+t.height,e.top+e.height);const s=o-r;const c=i-n;if(r<o&&n<i){const n=t.width*t.height;const r=e.width*e.height;const o=s*c;const i=o/(n+r-o);return Number(i.toFixed(4))}return 0}const L=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=[];for(const e of r){const{id:r}=e;const i=n.get(r);if(i){const n=k(i,t);if(n>0){o.push({id:r,data:{droppableContainer:e,value:n}})}}}return o.sort(D)};function T(e,t){const{top:n,left:r,bottom:o,right:i}=t;return n<=e.y&&e.y<=o&&r<=e.x&&e.x<=i}const A=e=>{let{droppableContainers:t,droppableRects:n,pointerCoordinates:r}=e;if(!r){return[]}const o=[];for(const e of t){const{id:t}=e;const i=n.get(t);if(i&&T(r,i)){const n=S(i);const s=n.reduce(((e,t)=>e+R(r,t)),0);const c=Number((s/4).toFixed(4));o.push({id:t,data:{droppableContainer:e,value:c}})}}return o.sort(E)};function j(e,t,n){return{...e,scaleX:t&&n?t.width/n.width:1,scaleY:t&&n?t.height/n.height:1}}function B(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:x}function z(e){return function t(n){for(var r=arguments.length,o=new Array(r>1?r-1:0),i=1;i<r;i++){o[i-1]=arguments[i]}return o.reduce(((t,n)=>({...t,top:t.top+e*n.y,bottom:t.bottom+e*n.y,left:t.left+e*n.x,right:t.right+e*n.x})),{...n})}}const X=z(1);function P(e){if(e.startsWith("matrix3d(")){const t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}else if(e.startsWith("matrix(")){const t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}function F(e,t,n){const r=P(t);if(!r){return e}const{scaleX:o,scaleY:i,x:s,y:c}=r;const a=e.left-s-(1-o)*parseFloat(n);const l=e.top-c-(1-i)*parseFloat(n.slice(n.indexOf(" ")+1));const u=o?e.width/o:e.width;const d=i?e.height/i:e.height;return{width:u,height:d,top:l,right:a+u,bottom:l+d,left:a}}const Y={ignoreTransform:false};function J(e,t){if(t===void 0){t=Y}let n=e.getBoundingClientRect();if(t.ignoreTransform){const{transform:t,transformOrigin:r}=(0,s.Jj)(e).getComputedStyle(e);if(t){n=F(n,t,r)}}const{top:r,left:o,width:i,height:c,bottom:a,right:l}=n;return{top:r,left:o,width:i,height:c,bottom:a,right:l}}function U(e){return J(e,{ignoreTransform:true})}function H(e){const t=e.innerWidth;const n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}function K(e,t){if(t===void 0){t=(0,s.Jj)(e).getComputedStyle(e)}return t.position==="fixed"}function q(e,t){if(t===void 0){t=(0,s.Jj)(e).getComputedStyle(e)}const n=/(auto|scroll|overlay)/;const r=["overflow","overflowX","overflowY"];return r.some((e=>{const r=t[e];return typeof r==="string"?n.test(r):false}))}function W(e,t){const n=[];function r(o){if(t!=null&&n.length>=t){return n}if(!o){return n}if((0,s.qk)(o)&&o.scrollingElement!=null&&!n.includes(o.scrollingElement)){n.push(o.scrollingElement);return n}if(!(0,s.Re)(o)||(0,s.vZ)(o)){return n}if(n.includes(o)){return n}const i=(0,s.Jj)(e).getComputedStyle(o);if(o!==e){if(q(o,i)){n.push(o)}}if(K(o,i)){return n}return r(o.parentNode)}if(!e){return n}return r(e)}function G(e){const[t]=W(e,1);return t!=null?t:null}function V(e){if(!s.Nq||!e){return null}if((0,s.FJ)(e)){return e}if(!(0,s.UG)(e)){return null}if((0,s.qk)(e)||e===(0,s.r3)(e).scrollingElement){return window}if((0,s.Re)(e)){return e}return null}function _(e){if((0,s.FJ)(e)){return e.scrollX}return e.scrollLeft}function $(e){if((0,s.FJ)(e)){return e.scrollY}return e.scrollTop}function Z(e){return{x:_(e),y:$(e)}}var Q;(function(e){e[e["Forward"]=1]="Forward";e[e["Backward"]=-1]="Backward"})(Q||(Q={}));function ee(e){if(!s.Nq||!e){return false}return e===document.scrollingElement}function te(e){const t={x:0,y:0};const n=ee(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth};const r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height};const o=e.scrollTop<=t.y;const i=e.scrollLeft<=t.x;const s=e.scrollTop>=r.y;const c=e.scrollLeft>=r.x;return{isTop:o,isLeft:i,isBottom:s,isRight:c,maxScroll:r,minScroll:t}}const ne={x:.2,y:.2};function re(e,t,n,r,o){let{top:i,left:s,right:c,bottom:a}=n;if(r===void 0){r=10}if(o===void 0){o=ne}const{isTop:l,isBottom:u,isLeft:d,isRight:f}=te(e);const h={x:0,y:0};const g={x:0,y:0};const v={height:t.height*o.y,width:t.width*o.x};if(!l&&i<=t.top+v.height){h.y=Q.Backward;g.y=r*Math.abs((t.top+v.height-i)/v.height)}else if(!u&&a>=t.bottom-v.height){h.y=Q.Forward;g.y=r*Math.abs((t.bottom-v.height-a)/v.height)}if(!f&&c>=t.right-v.width){h.x=Q.Forward;g.x=r*Math.abs((t.right-v.width-c)/v.width)}else if(!d&&s<=t.left+v.width){h.x=Q.Backward;g.x=r*Math.abs((t.left+v.width-s)/v.width)}return{direction:h,speed:g}}function oe(e){if(e===document.scrollingElement){const{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}const{top:t,left:n,right:r,bottom:o}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:o,width:e.clientWidth,height:e.clientHeight}}function ie(e){return e.reduce(((e,t)=>(0,s.IH)(e,Z(t))),x)}function se(e){return e.reduce(((e,t)=>e+_(t)),0)}function ce(e){return e.reduce(((e,t)=>e+$(t)),0)}function ae(e,t){if(t===void 0){t=J}if(!e){return}const{top:n,left:r,bottom:o,right:i}=t(e);const s=G(e);if(!s){return}if(o<=0||i<=0||n>=window.innerHeight||r>=window.innerWidth){e.scrollIntoView({block:"center",inline:"center"})}}const le=[["x",["left","right"],se],["y",["top","bottom"],ce]];class ue{constructor(e,t){this.rect=void 0;this.width=void 0;this.height=void 0;this.top=void 0;this.bottom=void 0;this.right=void 0;this.left=void 0;const n=W(t);const r=ie(n);this.rect={...e};this.width=e.width;this.height=e.height;for(const[e,t,o]of le){for(const i of t){Object.defineProperty(this,i,{get:()=>{const t=o(n);const s=r[e]-t;return this.rect[i]+s},enumerable:true})}}Object.defineProperty(this,"rect",{enumerable:false})}}class de{constructor(e){this.target=void 0;this.listeners=[];this.removeAll=()=>{this.listeners.forEach((e=>{var t;return(t=this.target)==null?void 0:t.removeEventListener(...e)}))};this.target=e}add(e,t,n){var r;(r=this.target)==null?void 0:r.addEventListener(e,t,n);this.listeners.push([e,t,n])}}function fe(e){const{EventTarget:t}=(0,s.Jj)(e);return e instanceof t?e:(0,s.r3)(e)}function he(e,t){const n=Math.abs(e.x);const r=Math.abs(e.y);if(typeof t==="number"){return Math.sqrt(n**2+r**2)>t}if("x"in t&&"y"in t){return n>t.x&&r>t.y}if("x"in t){return n>t.x}if("y"in t){return r>t.y}return false}var ge;(function(e){e["Click"]="click";e["DragStart"]="dragstart";e["Keydown"]="keydown";e["ContextMenu"]="contextmenu";e["Resize"]="resize";e["SelectionChange"]="selectionchange";e["VisibilityChange"]="visibilitychange"})(ge||(ge={}));function ve(e){e.preventDefault()}function pe(e){e.stopPropagation()}var be;(function(e){e["Space"]="Space";e["Down"]="ArrowDown";e["Right"]="ArrowRight";e["Left"]="ArrowLeft";e["Up"]="ArrowUp";e["Esc"]="Escape";e["Enter"]="Enter"})(be||(be={}));const me={start:[be.Space,be.Enter],cancel:[be.Esc],end:[be.Space,be.Enter]};const ye=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case be.Right:return{...n,x:n.x+25};case be.Left:return{...n,x:n.x-25};case be.Down:return{...n,y:n.y+25};case be.Up:return{...n,y:n.y-25}}return undefined};class we{constructor(e){this.props=void 0;this.autoScrollEnabled=false;this.referenceCoordinates=void 0;this.listeners=void 0;this.windowListeners=void 0;this.props=e;const{event:{target:t}}=e;this.props=e;this.listeners=new de((0,s.r3)(t));this.windowListeners=new de((0,s.Jj)(t));this.handleKeyDown=this.handleKeyDown.bind(this);this.handleCancel=this.handleCancel.bind(this);this.attach()}attach(){this.handleStart();this.windowListeners.add(ge.Resize,this.handleCancel);this.windowListeners.add(ge.VisibilityChange,this.handleCancel);setTimeout((()=>this.listeners.add(ge.Keydown,this.handleKeyDown)))}handleStart(){const{activeNode:e,onStart:t}=this.props;const n=e.node.current;if(n){ae(n)}t(x)}handleKeyDown(e){if((0,s.vd)(e)){const{active:t,context:n,options:r}=this.props;const{keyboardCodes:o=me,coordinateGetter:i=ye,scrollBehavior:c="smooth"}=r;const{code:a}=e;if(o.end.includes(a)){this.handleEnd(e);return}if(o.cancel.includes(a)){this.handleCancel(e);return}const{collisionRect:l}=n.current;const u=l?{x:l.left,y:l.top}:x;if(!this.referenceCoordinates){this.referenceCoordinates=u}const d=i(e,{active:t,context:n.current,currentCoordinates:u});if(d){const t=(0,s.$X)(d,u);const r={x:0,y:0};const{scrollableAncestors:o}=n.current;for(const n of o){const o=e.code;const{isTop:i,isRight:s,isLeft:a,isBottom:l,maxScroll:u,minScroll:f}=te(n);const h=oe(n);const g={x:Math.min(o===be.Right?h.right-h.width/2:h.right,Math.max(o===be.Right?h.left:h.left+h.width/2,d.x)),y:Math.min(o===be.Down?h.bottom-h.height/2:h.bottom,Math.max(o===be.Down?h.top:h.top+h.height/2,d.y))};const v=o===be.Right&&!s||o===be.Left&&!a;const p=o===be.Down&&!l||o===be.Up&&!i;if(v&&g.x!==d.x){const e=n.scrollLeft+t.x;const i=o===be.Right&&e<=u.x||o===be.Left&&e>=f.x;if(i&&!t.y){n.scrollTo({left:e,behavior:c});return}if(i){r.x=n.scrollLeft-e}else{r.x=o===be.Right?n.scrollLeft-u.x:n.scrollLeft-f.x}if(r.x){n.scrollBy({left:-r.x,behavior:c})}break}else if(p&&g.y!==d.y){const e=n.scrollTop+t.y;const i=o===be.Down&&e<=u.y||o===be.Up&&e>=f.y;if(i&&!t.x){n.scrollTo({top:e,behavior:c});return}if(i){r.y=n.scrollTop-e}else{r.y=o===be.Down?n.scrollTop-u.y:n.scrollTop-f.y}if(r.y){n.scrollBy({top:-r.y,behavior:c})}break}}this.handleMove(e,(0,s.IH)((0,s.$X)(d,this.referenceCoordinates),r))}}}handleMove(e,t){const{onMove:n}=this.props;e.preventDefault();n(t)}handleEnd(e){const{onEnd:t}=this.props;e.preventDefault();this.detach();t()}handleCancel(e){const{onCancel:t}=this.props;e.preventDefault();this.detach();t()}detach(){this.listeners.removeAll();this.windowListeners.removeAll()}}we.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=me,onActivation:o}=t;let{active:i}=n;const{code:s}=e.nativeEvent;if(r.start.includes(s)){const t=i.activatorNode.current;if(t&&e.target!==t){return false}e.preventDefault();o==null?void 0:o({event:e.nativeEvent});return true}return false}}];function xe(e){return Boolean(e&&"distance"in e)}function Re(e){return Boolean(e&&"delay"in e)}class Ce{constructor(e,t,n){var r;if(n===void 0){n=fe(e.event.target)}this.props=void 0;this.events=void 0;this.autoScrollEnabled=true;this.document=void 0;this.activated=false;this.initialCoordinates=void 0;this.timeoutId=null;this.listeners=void 0;this.documentListeners=void 0;this.windowListeners=void 0;this.props=e;this.events=t;const{event:o}=e;const{target:i}=o;this.props=e;this.events=t;this.document=(0,s.r3)(i);this.documentListeners=new de(this.document);this.listeners=new de(n);this.windowListeners=new de((0,s.Jj)(i));this.initialCoordinates=(r=(0,s.DC)(o))!=null?r:x;this.handleStart=this.handleStart.bind(this);this.handleMove=this.handleMove.bind(this);this.handleEnd=this.handleEnd.bind(this);this.handleCancel=this.handleCancel.bind(this);this.handleKeydown=this.handleKeydown.bind(this);this.removeTextSelection=this.removeTextSelection.bind(this);this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;this.listeners.add(e.move.name,this.handleMove,{passive:false});this.listeners.add(e.end.name,this.handleEnd);this.windowListeners.add(ge.Resize,this.handleCancel);this.windowListeners.add(ge.DragStart,ve);this.windowListeners.add(ge.VisibilityChange,this.handleCancel);this.windowListeners.add(ge.ContextMenu,ve);this.documentListeners.add(ge.Keydown,this.handleKeydown);if(t){if(n!=null&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options})){return this.handleStart()}if(Re(t)){this.timeoutId=setTimeout(this.handleStart,t.delay);return}if(xe(t)){return}}this.handleStart()}detach(){this.listeners.removeAll();this.windowListeners.removeAll();setTimeout(this.documentListeners.removeAll,50);if(this.timeoutId!==null){clearTimeout(this.timeoutId);this.timeoutId=null}}handleStart(){const{initialCoordinates:e}=this;const{onStart:t}=this.props;if(e){this.activated=true;this.documentListeners.add(ge.Click,pe,{capture:true});this.removeTextSelection();this.documentListeners.add(ge.SelectionChange,this.removeTextSelection);t(e)}}handleMove(e){var t;const{activated:n,initialCoordinates:r,props:o}=this;const{onMove:i,options:{activationConstraint:c}}=o;if(!r){return}const a=(t=(0,s.DC)(e))!=null?t:x;const l=(0,s.$X)(r,a);if(!n&&c){if(xe(c)){if(c.tolerance!=null&&he(l,c.tolerance)){return this.handleCancel()}if(he(l,c.distance)){return this.handleStart()}}if(Re(c)){if(he(l,c.tolerance)){return this.handleCancel()}}return}if(e.cancelable){e.preventDefault()}i(a)}handleEnd(){const{onEnd:e}=this.props;this.detach();e()}handleCancel(){const{onCancel:e}=this.props;this.detach();e()}handleKeydown(e){if(e.code===be.Esc){this.handleCancel()}}removeTextSelection(){var e;(e=this.document.getSelection())==null?void 0:e.removeAllRanges()}}const Ee={move:{name:"pointermove"},end:{name:"pointerup"}};class De extends Ce{constructor(e){const{event:t}=e;const n=(0,s.r3)(t.target);super(e,Ee,n)}}De.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e;let{onActivation:r}=t;if(!n.isPrimary||n.button!==0){return false}r==null?void 0:r({event:n});return true}}];const Se={move:{name:"mousemove"},end:{name:"mouseup"}};var Ie;(function(e){e[e["RightClick"]=2]="RightClick"})(Ie||(Ie={}));class Ne extends Ce{constructor(e){super(e,Se,(0,s.r3)(e.event.target))}}Ne.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e;let{onActivation:r}=t;if(n.button===Ie.RightClick){return false}r==null?void 0:r({event:n});return true}}];const Me={move:{name:"touchmove"},end:{name:"touchend"}};class Oe extends Ce{constructor(e){super(e,Me)}static setup(){window.addEventListener(Me.move.name,e,{capture:false,passive:false});return function t(){window.removeEventListener(Me.move.name,e)};function e(){}}}Oe.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e;let{onActivation:r}=t;const{touches:o}=n;if(o.length>1){return false}r==null?void 0:r({event:n});return true}}];var ke;(function(e){e[e["Pointer"]=0]="Pointer";e[e["DraggableRect"]=1]="DraggableRect"})(ke||(ke={}));var Le;(function(e){e[e["TreeOrder"]=0]="TreeOrder";e[e["ReversedTreeOrder"]=1]="ReversedTreeOrder"})(Le||(Le={}));function Te(e){let{acceleration:t,activator:n=ke.Pointer,canScroll:o,draggingRect:i,enabled:c,interval:a=5,order:l=Le.TreeOrder,pointerCoordinates:u,scrollableAncestors:d,scrollableAncestorRects:f,delta:h,threshold:g}=e;const v=je({delta:h,disabled:!c});const[p,b]=(0,s.Yz)();const m=(0,r.useRef)({x:0,y:0});const y=(0,r.useRef)({x:0,y:0});const w=(0,r.useMemo)((()=>{switch(n){case ke.Pointer:return u?{top:u.y,bottom:u.y,left:u.x,right:u.x}:null;case ke.DraggableRect:return i}}),[n,i,u]);const x=(0,r.useRef)(null);const R=(0,r.useCallback)((()=>{const e=x.current;if(!e){return}const t=m.current.x*y.current.x;const n=m.current.y*y.current.y;e.scrollBy(t,n)}),[]);const C=(0,r.useMemo)((()=>l===Le.TreeOrder?[...d].reverse():d),[l,d]);(0,r.useEffect)((()=>{if(!c||!d.length||!w){b();return}for(const e of C){if((o==null?void 0:o(e))===false){continue}const n=d.indexOf(e);const r=f[n];if(!r){continue}const{direction:i,speed:s}=re(e,r,w,t,g);for(const e of["x","y"]){if(!v[e][i[e]]){s[e]=0;i[e]=0}}if(s.x>0||s.y>0){b();x.current=e;p(R,a);m.current=s;y.current=i;return}}m.current={x:0,y:0};y.current={x:0,y:0};b()}),[t,R,o,b,c,a,JSON.stringify(w),JSON.stringify(v),p,d,C,f,JSON.stringify(g)])}const Ae={x:{[Q.Backward]:false,[Q.Forward]:false},y:{[Q.Backward]:false,[Q.Forward]:false}};function je(e){let{delta:t,disabled:n}=e;const r=(0,s.D9)(t);return(0,s.Gj)((e=>{if(n||!r||!e){return Ae}const o={x:Math.sign(t.x-r.x),y:Math.sign(t.y-r.y)};return{x:{[Q.Backward]:e.x[Q.Backward]||o.x===-1,[Q.Forward]:e.x[Q.Forward]||o.x===1},y:{[Q.Backward]:e.y[Q.Backward]||o.y===-1,[Q.Forward]:e.y[Q.Forward]||o.y===1}}}),[n,t,r])}function Be(e,t){const n=t!==null?e.get(t):undefined;const r=n?n.node.current:null;return(0,s.Gj)((e=>{var n;if(t===null){return null}return(n=r!=null?r:e)!=null?n:null}),[r,t])}function ze(e,t){return(0,r.useMemo)((()=>e.reduce(((e,n)=>{const{sensor:r}=n;const o=r.activators.map((e=>({eventName:e.eventName,handler:t(e.handler,n)})));return[...e,...o]}),[])),[e,t])}var Xe;(function(e){e[e["Always"]=0]="Always";e[e["BeforeDragging"]=1]="BeforeDragging";e[e["WhileDragging"]=2]="WhileDragging"})(Xe||(Xe={}));var Pe;(function(e){e["Optimized"]="optimized"})(Pe||(Pe={}));const Fe=new Map;function Ye(e,t){let{dragging:n,dependencies:o,config:i}=t;const[c,a]=(0,r.useState)(null);const{frequency:l,measure:u,strategy:d}=i;const f=(0,r.useRef)(e);const h=m();const g=(0,s.Ey)(h);const v=(0,r.useCallback)((function(e){if(e===void 0){e=[]}if(g.current){return}a((t=>{if(t===null){return e}return t.concat(e.filter((e=>!t.includes(e))))}))}),[g]);const p=(0,r.useRef)(null);const b=(0,s.Gj)((t=>{if(h&&!n){return Fe}if(!t||t===Fe||f.current!==e||c!=null){const t=new Map;for(let n of e){if(!n){continue}if(c&&c.length>0&&!c.includes(n.id)&&n.rect.current){t.set(n.id,n.rect.current);continue}const e=n.node.current;const r=e?new ue(u(e),e):null;n.rect.current=r;if(r){t.set(n.id,r)}}return t}return t}),[e,c,n,h,u]);(0,r.useEffect)((()=>{f.current=e}),[e]);(0,r.useEffect)((()=>{if(h){return}v()}),[n,h]);(0,r.useEffect)((()=>{if(c&&c.length>0){a(null)}}),[JSON.stringify(c)]);(0,r.useEffect)((()=>{if(h||typeof l!=="number"||p.current!==null){return}p.current=setTimeout((()=>{v();p.current=null}),l)}),[l,h,v,...o]);return{droppableRects:b,measureDroppableContainers:v,measuringScheduled:c!=null};function m(){switch(d){case Xe.Always:return false;case Xe.BeforeDragging:return n;default:return!n}}}function Je(e,t){return(0,s.Gj)((n=>{if(!e){return null}if(n){return n}return typeof t==="function"?t(e):e}),[t,e])}function Ue(e,t){return Je(e,t)}function He(e){let{callback:t,disabled:n}=e;const o=(0,s.zX)(t);const i=(0,r.useMemo)((()=>{if(n||typeof window==="undefined"||typeof window.MutationObserver==="undefined"){return undefined}const{MutationObserver:e}=window;return new e(o)}),[o,n]);(0,r.useEffect)((()=>()=>i==null?void 0:i.disconnect()),[i]);return i}function Ke(e){let{callback:t,disabled:n}=e;const o=(0,s.zX)(t);const i=(0,r.useMemo)((()=>{if(n||typeof window==="undefined"||typeof window.ResizeObserver==="undefined"){return undefined}const{ResizeObserver:e}=window;return new e(o)}),[n]);(0,r.useEffect)((()=>()=>i==null?void 0:i.disconnect()),[i]);return i}function qe(e){return new ue(J(e),e)}function We(e,t,n){if(t===void 0){t=qe}const[o,i]=(0,r.useReducer)(l,null);const c=He({callback(t){if(!e){return}for(const n of t){const{type:t,target:r}=n;if(t==="childList"&&r instanceof HTMLElement&&r.contains(e)){i();break}}}});const a=Ke({callback:i});(0,s.LI)((()=>{i();if(e){a==null?void 0:a.observe(e);c==null?void 0:c.observe(document.body,{childList:true,subtree:true})}else{a==null?void 0:a.disconnect();c==null?void 0:c.disconnect()}}),[e]);return o;function l(r){if(!e){return null}if(e.isConnected===false){var o;return(o=r!=null?r:n)!=null?o:null}const i=t(e);if(JSON.stringify(r)===JSON.stringify(i)){return r}return i}}function Ge(e){const t=Je(e);return B(e,t)}const Ve=[];function _e(e){const t=(0,r.useRef)(e);const n=(0,s.Gj)((n=>{if(!e){return Ve}if(n&&n!==Ve&&e&&t.current&&e.parentNode===t.current.parentNode){return n}return W(e)}),[e]);(0,r.useEffect)((()=>{t.current=e}),[e]);return n}function $e(e){const[t,n]=(0,r.useState)(null);const o=(0,r.useRef)(e);const i=(0,r.useCallback)((e=>{const t=V(e.target);if(!t){return}n((e=>{if(!e){return null}e.set(t,Z(t));return new Map(e)}))}),[]);(0,r.useEffect)((()=>{const t=o.current;if(e!==t){r(t);const s=e.map((e=>{const t=V(e);if(t){t.addEventListener("scroll",i,{passive:true});return[t,Z(t)]}return null})).filter((e=>e!=null));n(s.length?new Map(s):null);o.current=e}return()=>{r(e);r(t)};function r(e){e.forEach((e=>{const t=V(e);t==null?void 0:t.removeEventListener("scroll",i)}))}}),[i,e]);return(0,r.useMemo)((()=>{if(e.length){return t?Array.from(t.values()).reduce(((e,t)=>(0,s.IH)(e,t)),x):ie(e)}return x}),[e,t])}function Ze(e,t){if(t===void 0){t=[]}const n=(0,r.useRef)(null);(0,r.useEffect)((()=>{n.current=null}),t);(0,r.useEffect)((()=>{const t=e!==x;if(t&&!n.current){n.current=e}if(!t&&n.current){n.current=null}}),[e]);return n.current?(0,s.$X)(e,n.current):x}function Qe(e){(0,r.useEffect)((()=>{if(!s.Nq){return}const t=e.map((e=>{let{sensor:t}=e;return t.setup==null?void 0:t.setup()}));return()=>{for(const e of t){e==null?void 0:e()}}}),e.map((e=>{let{sensor:t}=e;return t})))}function et(e,t){return(0,r.useMemo)((()=>e.reduce(((e,n)=>{let{eventName:r,handler:o}=n;e[r]=e=>{o(e,t)};return e}),{})),[e,t])}function tt(e){return(0,r.useMemo)((()=>e?H(e):null),[e])}const nt=[];function rt(e,t){if(t===void 0){t=J}const[n]=e;const o=tt(n?(0,s.Jj)(n):null);const[i,c]=(0,r.useReducer)(l,nt);const a=Ke({callback:c});if(e.length>0&&i===nt){c()}(0,s.LI)((()=>{if(e.length){e.forEach((e=>a==null?void 0:a.observe(e)))}else{a==null?void 0:a.disconnect();c()}}),[e]);return i;function l(){if(!e.length){return nt}return e.map((e=>ee(e)?o:new ue(t(e),e)))}}function ot(e){if(!e){return null}if(e.children.length>1){return e}const t=e.children[0];return(0,s.Re)(t)?t:e}function it(e){let{measure:t}=e;const[n,o]=(0,r.useState)(null);const i=(0,r.useCallback)((e=>{for(const{target:n}of e){if((0,s.Re)(n)){o((e=>{const r=t(n);return e?{...e,width:r.width,height:r.height}:r}));break}}}),[t]);const c=Ke({callback:i});const a=(0,r.useCallback)((e=>{const n=ot(e);c==null?void 0:c.disconnect();if(n){c==null?void 0:c.observe(n)}o(n?t(n):null)}),[t,c]);const[l,u]=(0,s.wm)(a);return(0,r.useMemo)((()=>({nodeRef:l,rect:n,setRef:u})),[n,l,u])}const st=[{sensor:De,options:{}},{sensor:we,options:{}}];const ct={current:{}};const at={draggable:{measure:U},droppable:{measure:U,strategy:Xe.WhileDragging,frequency:Pe.Optimized},dragOverlay:{measure:J}};class lt extends Map{get(e){var t;return e!=null?(t=super.get(e))!=null?t:undefined:undefined}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter((e=>{let{disabled:t}=e;return!t}))}getNodeFor(e){var t,n;return(t=(n=this.get(e))==null?void 0:n.node.current)!=null?t:undefined}}const ut={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new lt,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:m},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:at,measureDroppableContainers:m,windowRect:null,measuringScheduled:false};const dt={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:m,draggableNodes:new Map,over:null,measureDroppableContainers:m};const ft=(0,r.createContext)(dt);const ht=(0,r.createContext)(ut);function gt(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new lt}}}function vt(e,t){switch(t.type){case b.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case b.DragMove:if(!e.draggable.active){return e}return{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case b.DragEnd:case b.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case b.RegisterDroppable:{const{element:n}=t;const{id:r}=n;const o=new lt(e.droppable.containers);o.set(r,n);return{...e,droppable:{...e.droppable,containers:o}}}case b.SetDroppableDisabled:{const{id:n,key:r,disabled:o}=t;const i=e.droppable.containers.get(n);if(!i||r!==i.key){return e}const s=new lt(e.droppable.containers);s.set(n,{...i,disabled:o});return{...e,droppable:{...e.droppable,containers:s}}}case b.UnregisterDroppable:{const{id:n,key:r}=t;const o=e.droppable.containers.get(n);if(!o||r!==o.key){return e}const i=new lt(e.droppable.containers);i.delete(n);return{...e,droppable:{...e.droppable,containers:i}}}default:{return e}}}function pt(e){let{disabled:t}=e;const{active:n,activatorEvent:o,draggableNodes:i}=(0,r.useContext)(ft);const c=(0,s.D9)(o);const a=(0,s.D9)(n==null?void 0:n.id);(0,r.useEffect)((()=>{if(t){return}if(!o&&c&&a!=null){if(!(0,s.vd)(c)){return}if(document.activeElement===c.target){return}const e=i.get(a);if(!e){return}const{activatorNode:t,node:n}=e;if(!t.current&&!n.current){return}requestAnimationFrame((()=>{for(const e of[t.current,n.current]){if(!e){continue}const t=(0,s.so)(e);if(t){t.focus();break}}}))}}),[o,t,i,a,c]);return null}function bt(e,t){let{transform:n,...r}=t;return e!=null&&e.length?e.reduce(((e,t)=>t({transform:e,...r})),n):n}function mt(e){return(0,r.useMemo)((()=>({draggable:{...at.draggable,...e==null?void 0:e.draggable},droppable:{...at.droppable,...e==null?void 0:e.droppable},dragOverlay:{...at.dragOverlay,...e==null?void 0:e.dragOverlay}})),[e==null?void 0:e.draggable,e==null?void 0:e.droppable,e==null?void 0:e.dragOverlay])}function yt(e){let{activeNode:t,measure:n,initialRect:o,config:i=true}=e;const c=(0,r.useRef)(false);const{x:a,y:l}=typeof i==="boolean"?{x:i,y:i}:i;(0,s.LI)((()=>{const e=!a&&!l;if(e||!t){c.current=false;return}if(c.current||!o){return}const r=t==null?void 0:t.node.current;if(!r||r.isConnected===false){return}const i=n(r);const s=B(i,o);if(!a){s.x=0}if(!l){s.y=0}c.current=true;if(Math.abs(s.x)>0||Math.abs(s.y)>0){const e=G(r);if(e){e.scrollBy({top:s.y,left:s.x})}}}),[t,a,l,o,n])}const wt=(0,r.createContext)({...x,scaleX:1,scaleY:1});var xt;(function(e){e[e["Uninitialized"]=0]="Uninitialized";e[e["Initializing"]=1]="Initializing";e[e["Initialized"]=2]="Initialized"})(xt||(xt={}));const Rt=(0,r.memo)((function e(t){var n,c,a,l;let{id:u,accessibility:f,autoScroll:g=true,children:v,sensors:m=st,collisionDetection:y=L,measuring:w,modifiers:x,...R}=t;const C=(0,r.useReducer)(vt,undefined,gt);const[E,D]=C;const[S,N]=h();const[M,O]=(0,r.useState)(xt.Uninitialized);const k=M===xt.Initialized;const{draggable:{active:T,nodes:A,translate:B},droppable:{containers:z}}=E;const P=T?A.get(T):null;const F=(0,r.useRef)({initial:null,translated:null});const Y=(0,r.useMemo)((()=>{var e;return T!=null?{id:T,data:(e=P==null?void 0:P.data)!=null?e:ct,rect:F}:null}),[T,P]);const J=(0,r.useRef)(null);const[U,H]=(0,r.useState)(null);const[K,q]=(0,r.useState)(null);const W=(0,s.Ey)(R,Object.values(R));const G=(0,s.Ld)("DndDescribedBy",u);const V=(0,r.useMemo)((()=>z.getEnabled()),[z]);const _=mt(w);const{droppableRects:$,measureDroppableContainers:Z,measuringScheduled:Q}=Ye(V,{dragging:k,dependencies:[B.x,B.y],config:_.droppable});const ee=Be(A,T);const te=(0,r.useMemo)((()=>K?(0,s.DC)(K):null),[K]);const ne=je();const re=Ue(ee,_.draggable.measure);yt({activeNode:T?A.get(T):null,config:ne.layoutShiftCompensation,initialRect:re,measure:_.draggable.measure});const oe=We(ee,_.draggable.measure,re);const ie=We(ee?ee.parentElement:null);const se=(0,r.useRef)({activatorEvent:null,active:null,activeNode:ee,collisionRect:null,collisions:null,droppableRects:$,draggableNodes:A,draggingNode:null,draggingNodeRect:null,droppableContainers:z,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null});const ce=z.getNodeFor((n=se.current.over)==null?void 0:n.id);const ae=it({measure:_.dragOverlay.measure});const le=(c=ae.nodeRef.current)!=null?c:ee;const ue=k?(a=ae.rect)!=null?a:oe:null;const de=Boolean(ae.nodeRef.current&&ae.rect);const fe=Ge(de?null:oe);const he=tt(le?(0,s.Jj)(le):null);const ge=_e(k?ce!=null?ce:ee:null);const ve=rt(ge);const pe=bt(x,{transform:{x:B.x-fe.x,y:B.y-fe.y,scaleX:1,scaleY:1},activatorEvent:K,active:Y,activeNodeRect:oe,containerNodeRect:ie,draggingNodeRect:ue,over:se.current.over,overlayNodeRect:ae.rect,scrollableAncestors:ge,scrollableAncestorRects:ve,windowRect:he});const be=te?(0,s.IH)(te,B):null;const me=$e(ge);const ye=Ze(me);const we=Ze(me,[oe]);const xe=(0,s.IH)(pe,ye);const Re=ue?X(ue,pe):null;const Ce=Y&&Re?y({active:Y,collisionRect:Re,droppableRects:$,droppableContainers:V,pointerCoordinates:be}):null;const Ee=I(Ce,"id");const[De,Se]=(0,r.useState)(null);const Ie=de?pe:(0,s.IH)(pe,we);const Ne=j(Ie,(l=De==null?void 0:De.rect)!=null?l:null,oe);const Me=(0,r.useCallback)(((e,t)=>{let{sensor:n,options:r}=t;if(J.current==null){return}const o=A.get(J.current);if(!o){return}const s=e.nativeEvent;const c=new n({active:J.current,activeNode:o,event:s,options:r,context:se,onStart(e){const t=J.current;if(t==null){return}const n=A.get(t);if(!n){return}const{onDragStart:r}=W.current;const o={active:{id:t,data:n.data,rect:F}};(0,i.unstable_batchedUpdates)((()=>{r==null?void 0:r(o);O(xt.Initializing);D({type:b.DragStart,initialCoordinates:e,active:t});S({type:"onDragStart",event:o})}))},onMove(e){D({type:b.DragMove,coordinates:e})},onEnd:a(b.DragEnd),onCancel:a(b.DragCancel)});(0,i.unstable_batchedUpdates)((()=>{H(c);q(e.nativeEvent)}));function a(e){return async function t(){const{active:n,collisions:r,over:o,scrollAdjustedTranslate:c}=se.current;let a=null;if(n&&c){const{cancelDrop:t}=W.current;a={activatorEvent:s,active:n,collisions:r,delta:c,over:o};if(e===b.DragEnd&&typeof t==="function"){const n=await Promise.resolve(t(a));if(n){e=b.DragCancel}}}J.current=null;(0,i.unstable_batchedUpdates)((()=>{D({type:e});O(xt.Uninitialized);Se(null);H(null);q(null);const t=e===b.DragEnd?"onDragEnd":"onDragCancel";if(a){const e=W.current[t];e==null?void 0:e(a);S({type:t,event:a})}}))}}}),[A]);const Oe=(0,r.useCallback)(((e,t)=>(n,r)=>{const o=n.nativeEvent;const i=A.get(r);if(J.current!==null||!i||o.dndKit||o.defaultPrevented){return}const s={active:i};const c=e(n,t.options,s);if(c===true){o.dndKit={capturedBy:t.sensor};J.current=r;Me(n,t)}}),[A,Me]);const ke=ze(m,Oe);Qe(m);(0,s.LI)((()=>{if(oe&&M===xt.Initializing){O(xt.Initialized)}}),[oe,M]);(0,r.useEffect)((()=>{const{onDragMove:e}=W.current;const{active:t,activatorEvent:n,collisions:r,over:o}=se.current;if(!t||!n){return}const s={active:t,activatorEvent:n,collisions:r,delta:{x:xe.x,y:xe.y},over:o};(0,i.unstable_batchedUpdates)((()=>{e==null?void 0:e(s);S({type:"onDragMove",event:s})}))}),[xe.x,xe.y]);(0,r.useEffect)((()=>{const{active:e,activatorEvent:t,collisions:n,droppableContainers:r,scrollAdjustedTranslate:o}=se.current;if(!e||J.current==null||!t||!o){return}const{onDragOver:s}=W.current;const c=r.get(Ee);const a=c&&c.rect.current?{id:c.id,rect:c.rect.current,data:c.data,disabled:c.disabled}:null;const l={active:e,activatorEvent:t,collisions:n,delta:{x:o.x,y:o.y},over:a};(0,i.unstable_batchedUpdates)((()=>{Se(a);s==null?void 0:s(l);S({type:"onDragOver",event:l})}))}),[Ee]);(0,s.LI)((()=>{se.current={activatorEvent:K,active:Y,activeNode:ee,collisionRect:Re,collisions:Ce,droppableRects:$,draggableNodes:A,draggingNode:le,draggingNodeRect:ue,droppableContainers:z,over:De,scrollableAncestors:ge,scrollAdjustedTranslate:xe};F.current={initial:ue,translated:Re}}),[Y,ee,Ce,Re,A,le,ue,$,z,De,ge,xe]);Te({...ne,delta:B,draggingRect:Re,pointerCoordinates:be,scrollableAncestors:ge,scrollableAncestorRects:ve});const Le=(0,r.useMemo)((()=>{const e={active:Y,activeNode:ee,activeNodeRect:oe,activatorEvent:K,collisions:Ce,containerNodeRect:ie,dragOverlay:ae,draggableNodes:A,droppableContainers:z,droppableRects:$,over:De,measureDroppableContainers:Z,scrollableAncestors:ge,scrollableAncestorRects:ve,measuringConfiguration:_,measuringScheduled:Q,windowRect:he};return e}),[Y,ee,oe,K,Ce,ie,ae,A,z,$,De,Z,ge,ve,_,Q,he]);const Ae=(0,r.useMemo)((()=>{const e={activatorEvent:K,activators:ke,active:Y,activeNodeRect:oe,ariaDescribedById:{draggable:G},dispatch:D,draggableNodes:A,over:De,measureDroppableContainers:Z};return e}),[K,ke,Y,oe,D,G,A,De,Z]);return o().createElement(d.Provider,{value:N},o().createElement(ft.Provider,{value:Ae},o().createElement(ht.Provider,{value:Le},o().createElement(wt.Provider,{value:Ne},v)),o().createElement(pt,{disabled:(f==null?void 0:f.restoreFocus)===false})),o().createElement(p,{...f,hiddenTextDescribedById:G}));function je(){const e=(U==null?void 0:U.autoScrollEnabled)===false;const t=typeof g==="object"?g.enabled===false:g===false;const n=k&&!e&&!t;if(typeof g==="object"){return{...g,enabled:n}}return{enabled:n}}}));const Ct=(0,r.createContext)(null);const Et="button";const Dt="Droppable";function St(e){let{id:t,data:n,disabled:o=false,attributes:i}=e;const c=(0,s.Ld)(Dt);const{activators:a,activatorEvent:l,active:u,activeNodeRect:d,ariaDescribedById:f,draggableNodes:h,over:g}=(0,r.useContext)(ft);const{role:v=Et,roleDescription:p="draggable",tabIndex:b=0}=i!=null?i:{};const m=(u==null?void 0:u.id)===t;const y=(0,r.useContext)(m?wt:Ct);const[w,x]=(0,s.wm)();const[R,C]=(0,s.wm)();const E=et(a,t);const D=(0,s.Ey)(n);(0,s.LI)((()=>{h.set(t,{id:t,key:c,node:w,activatorNode:R,data:D});return()=>{const e=h.get(t);if(e&&e.key===c){h.delete(t)}}}),[h,t]);const S=(0,r.useMemo)((()=>({role:v,tabIndex:b,"aria-disabled":o,"aria-pressed":m&&v===Et?true:undefined,"aria-roledescription":p,"aria-describedby":f.draggable})),[o,v,b,m,p,f.draggable]);return{active:u,activatorEvent:l,activeNodeRect:d,attributes:S,isDragging:m,listeners:o?undefined:E,node:w,over:g,setNodeRef:x,setActivatorNodeRef:C,transform:y}}function It(){return(0,r.useContext)(ht)}const Nt="Droppable";const Mt={timeout:25};function Ot(e){let{data:t,disabled:n=false,id:o,resizeObserverConfig:i}=e;const c=(0,s.Ld)(Nt);const{active:a,dispatch:l,over:u,measureDroppableContainers:d}=(0,r.useContext)(ft);const f=(0,r.useRef)({disabled:n});const h=(0,r.useRef)(false);const g=(0,r.useRef)(null);const v=(0,r.useRef)(null);const{disabled:p,updateMeasurementsFor:m,timeout:y}={...Mt,...i};const w=(0,s.Ey)(m!=null?m:o);const x=(0,r.useCallback)((()=>{if(!h.current){h.current=true;return}if(v.current!=null){clearTimeout(v.current)}v.current=setTimeout((()=>{d(Array.isArray(w.current)?w.current:[w.current]);v.current=null}),y)}),[y]);const R=Ke({callback:x,disabled:p||!a});const C=(0,r.useCallback)(((e,t)=>{if(!R){return}if(t){R.unobserve(t);h.current=false}if(e){R.observe(e)}}),[R]);const[E,D]=(0,s.wm)(C);const S=(0,s.Ey)(t);(0,r.useEffect)((()=>{if(!R||!E.current){return}R.disconnect();h.current=false;R.observe(E.current)}),[E,R]);(0,s.LI)((()=>{l({type:b.RegisterDroppable,element:{id:o,key:c,disabled:n,node:E,rect:g,data:S}});return()=>l({type:b.UnregisterDroppable,key:c,id:o})}),[o]);(0,r.useEffect)((()=>{if(n!==f.current.disabled){l({type:b.SetDroppableDisabled,id:o,key:c,disabled:n});f.current.disabled=n}}),[o,c,n,l]);return{active:a,rect:g,isOver:(u==null?void 0:u.id)===o,node:E,over:u,setNodeRef:D}}function kt(e){let{animation:t,children:n}=e;const[i,c]=(0,r.useState)(null);const[a,l]=(0,r.useState)(null);const u=(0,s.D9)(n);if(!n&&!i&&u){c(u)}(0,s.LI)((()=>{if(!a){return}const e=i==null?void 0:i.key;const n=i==null?void 0:i.props.id;if(e==null||n==null){c(null);return}Promise.resolve(t(n,a)).then((()=>{c(null)}))}),[t,i,a]);return o().createElement(o().Fragment,null,n,i?(0,r.cloneElement)(i,{ref:l}):null)}const Lt={x:0,y:0,scaleX:1,scaleY:1};function Tt(e){let{children:t}=e;return o().createElement(ft.Provider,{value:dt},o().createElement(wt.Provider,{value:Lt},t))}const At={position:"fixed",touchAction:"none"};const jt=e=>{const t=(0,s.vd)(e);return t?"transform 250ms ease":undefined};const Bt=(0,r.forwardRef)(((e,t)=>{let{as:n,activatorEvent:r,adjustScale:i,children:c,className:a,rect:l,style:u,transform:d,transition:f=jt}=e;if(!l){return null}const h=i?d:{...d,scaleX:1,scaleY:1};const g={...At,width:l.width,height:l.height,top:l.top,left:l.left,transform:s.ux.Transform.toString(h),transformOrigin:i&&r?C(r,l):undefined,transition:typeof f==="function"?f(r):f,...u};return o().createElement(n,{className:a,style:g,ref:t},c)}));const zt=e=>t=>{let{active:n,dragOverlay:r}=t;const o={};const{styles:i,className:s}=e;if(i!=null&&i.active){for(const[e,t]of Object.entries(i.active)){if(t===undefined){continue}o[e]=n.node.style.getPropertyValue(e);n.node.style.setProperty(e,t)}}if(i!=null&&i.dragOverlay){for(const[e,t]of Object.entries(i.dragOverlay)){if(t===undefined){continue}r.node.style.setProperty(e,t)}}if(s!=null&&s.active){n.node.classList.add(s.active)}if(s!=null&&s.dragOverlay){r.node.classList.add(s.dragOverlay)}return function e(){for(const[e,t]of Object.entries(o)){n.node.style.setProperty(e,t)}if(s!=null&&s.active){n.node.classList.remove(s.active)}}};const Xt=e=>{let{transform:{initial:t,final:n}}=e;return[{transform:s.ux.Transform.toString(t)},{transform:s.ux.Transform.toString(n)}]};const Pt={duration:250,easing:"ease",keyframes:Xt,sideEffects:zt({styles:{active:{opacity:"0"}}})};function Ft(e){let{config:t,draggableNodes:n,droppableContainers:r,measuringConfiguration:o}=e;return(0,s.zX)(((e,i)=>{if(t===null){return}const c=n.get(e);if(!c){return}const a=c.node.current;if(!a){return}const l=ot(i);if(!l){return}const{transform:u}=(0,s.Jj)(i).getComputedStyle(i);const d=P(u);if(!d){return}const f=typeof t==="function"?t:Yt(t);ae(a,o.draggable.measure);return f({active:{id:e,data:c.data,node:a,rect:o.draggable.measure(a)},draggableNodes:n,dragOverlay:{node:i,rect:o.dragOverlay.measure(l)},droppableContainers:r,measuringConfiguration:o,transform:d})}))}function Yt(e){const{duration:t,easing:n,sideEffects:r,keyframes:o}={...Pt,...e};return e=>{let{active:i,dragOverlay:s,transform:c,...a}=e;if(!t){return}const l={x:s.rect.left-i.rect.left,y:s.rect.top-i.rect.top};const u={scaleX:c.scaleX!==1?i.rect.width*c.scaleX/s.rect.width:1,scaleY:c.scaleY!==1?i.rect.height*c.scaleY/s.rect.height:1};const d={x:c.x-l.x,y:c.y-l.y,...u};const f=o({...a,active:i,dragOverlay:s,transform:{initial:c,final:d}});const[h]=f;const g=f[f.length-1];if(JSON.stringify(h)===JSON.stringify(g)){return}const v=r==null?void 0:r({active:i,dragOverlay:s,...a});const p=s.node.animate(f,{duration:t,easing:n,fill:"forwards"});return new Promise((e=>{p.onfinish=()=>{v==null?void 0:v();e()}}))}}let Jt=0;function Ut(e){return(0,r.useMemo)((()=>{if(e==null){return}Jt++;return Jt}),[e])}const Ht=o().memo((e=>{let{adjustScale:t=false,children:n,dropAnimation:i,style:s,transition:c,modifiers:a,wrapperElement:l="div",className:u,zIndex:d=999}=e;const{activatorEvent:f,active:h,activeNodeRect:g,containerNodeRect:v,draggableNodes:p,droppableContainers:b,dragOverlay:m,over:y,measuringConfiguration:w,scrollableAncestors:x,scrollableAncestorRects:R,windowRect:C}=It();const E=(0,r.useContext)(wt);const D=Ut(h==null?void 0:h.id);const S=bt(a,{activatorEvent:f,active:h,activeNodeRect:g,containerNodeRect:v,draggingNodeRect:m.rect,over:y,overlayNodeRect:m.rect,scrollableAncestors:x,scrollableAncestorRects:R,transform:E,windowRect:C});const I=Je(g);const N=Ft({config:i,draggableNodes:p,droppableContainers:b,measuringConfiguration:w});const M=I?m.setRef:undefined;return o().createElement(Tt,null,o().createElement(kt,{animation:N},h&&D?o().createElement(Bt,{key:D,id:h.id,ref:M,as:l,activatorEvent:f,adjustScale:t,className:u,transition:c,rect:I,style:{zIndex:d,...s},transform:S},n):null))}))},2339:(e,t,n)=>{n.d(t,{hg:()=>u});var r=n(4285);function o(e){return t=>{let{transform:n}=t;return{...n,x:Math.ceil(n.x/e)*e,y:Math.ceil(n.y/e)*e}}}const i=e=>{let{transform:t}=e;return{...t,y:0}};function s(e,t,n){const r={...e};if(t.top+e.y<=n.top){r.y=n.top-t.top}else if(t.bottom+e.y>=n.top+n.height){r.y=n.top+n.height-t.bottom}if(t.left+e.x<=n.left){r.x=n.left-t.left}else if(t.right+e.x>=n.left+n.width){r.x=n.left+n.width-t.right}return r}const c=e=>{let{containerNodeRect:t,draggingNodeRect:n,transform:r}=e;if(!n||!t){return r}return s(r,n,t)};const a=e=>{let{draggingNodeRect:t,transform:n,scrollableAncestorRects:r}=e;const o=r[0];if(!t||!o){return n}return s(n,t,o)};const l=e=>{let{transform:t}=e;return{...t,x:0}};const u=e=>{let{transform:t,draggingNodeRect:n,windowRect:r}=e;if(!n||!r){return t}return s(t,n,r)};const d=e=>{let{activatorEvent:t,draggingNodeRect:n,transform:r}=e;if(n&&t){const e=getEventCoordinates(t);if(!e){return r}const o=e.x-n.left;const i=e.y-n.top;return{...r,x:r.x+o-n.width/2,y:r.y+i-n.height/2}}return r}},5587:(e,t,n)=>{n.d(t,{Fo:()=>C,cP:()=>D,is:()=>j,nB:()=>k,qw:()=>y});var r=n(7363);var o=n.n(r);var i=n(9752);var s=n(4285);function c(e,t,n){const r=e.slice();r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]);return r}function a(e,t,n){const r=e.slice();r[t]=e[n];r[n]=e[t];return r}function l(e,t){return e.reduce(((e,n,r)=>{const o=t.get(n);if(o){e[r]=o}return e}),Array(e.length))}function u(e){return e!==null&&e>=0}function d(e,t){if(e===t){return true}if(e.length!==t.length){return false}for(let n=0;n<e.length;n++){if(e[n]!==t[n]){return false}}return true}function f(e){if(typeof e==="boolean"){return{draggable:e,droppable:e}}return e}const h={scaleX:1,scaleY:1};const g=e=>{var t;let{rects:n,activeNodeRect:r,activeIndex:o,overIndex:i,index:s}=e;const c=(t=n[o])!=null?t:r;if(!c){return null}const a=v(n,s,o);if(s===o){const e=n[i];if(!e){return null}return{x:o<i?e.left+e.width-(c.left+c.width):e.left-c.left,y:0,...h}}if(s>o&&s<=i){return{x:-c.width-a,y:0,...h}}if(s<o&&s>=i){return{x:c.width+a,y:0,...h}}return{x:0,y:0,...h}};function v(e,t,n){const r=e[t];const o=e[t-1];const i=e[t+1];if(!r||!o&&!i){return 0}if(n<t){return o?r.left-(o.left+o.width):i.left-(r.left+r.width)}return i?i.left-(r.left+r.width):r.left-(o.left+o.width)}const p=e=>{let{rects:t,activeIndex:n,overIndex:r,index:o}=e;const i=c(t,r,n);const s=t[o];const a=i[o];if(!a||!s){return null}return{x:a.left-s.left,y:a.top-s.top,scaleX:a.width/s.width,scaleY:a.height/s.height}};const b=e=>{let{activeIndex:t,index:n,rects:r,overIndex:o}=e;let i;let s;if(n===t){i=r[n];s=r[o]}if(n===o){i=r[n];s=r[t]}if(!s||!i){return null}return{x:s.left-i.left,y:s.top-i.top,scaleX:s.width/i.width,scaleY:s.height/i.height}};const m={scaleX:1,scaleY:1};const y=e=>{var t;let{activeIndex:n,activeNodeRect:r,index:o,rects:i,overIndex:s}=e;const c=(t=i[n])!=null?t:r;if(!c){return null}if(o===n){const e=i[s];if(!e){return null}return{x:0,y:n<s?e.top+e.height-(c.top+c.height):e.top-c.top,...m}}const a=w(i,o,n);if(o>n&&o<=s){return{x:0,y:-c.height-a,...m}}if(o<n&&o>=s){return{x:0,y:c.height+a,...m}}return{x:0,y:0,...m}};function w(e,t,n){const r=e[t];const o=e[t-1];const i=e[t+1];if(!r){return 0}if(n<t){return o?r.top-(o.top+o.height):i?i.top-(r.top+r.height):0}return i?i.top-(r.top+r.height):o?r.top-(o.top+o.height):0}const x="Sortable";const R=o().createContext({activeIndex:-1,containerId:x,disableTransforms:false,items:[],overIndex:-1,useDragOverlay:false,sortedRects:[],strategy:p,disabled:{draggable:false,droppable:false}});function C(e){let{children:t,id:n,items:c,strategy:a=p,disabled:u=false}=e;const{active:h,dragOverlay:g,droppableRects:v,over:b,measureDroppableContainers:m}=(0,i.Cj)();const y=(0,s.Ld)(x,n);const w=Boolean(g.rect!==null);const C=(0,r.useMemo)((()=>c.map((e=>typeof e==="object"&&"id"in e?e.id:e))),[c]);const E=h!=null;const D=h?C.indexOf(h.id):-1;const S=b?C.indexOf(b.id):-1;const I=(0,r.useRef)(C);const N=!d(C,I.current);const M=S!==-1&&D===-1||N;const O=f(u);(0,s.LI)((()=>{if(N&&E){m(C)}}),[N,C,E,m]);(0,r.useEffect)((()=>{I.current=C}),[C]);const k=(0,r.useMemo)((()=>({activeIndex:D,containerId:y,disabled:O,disableTransforms:M,items:C,overIndex:S,useDragOverlay:w,sortedRects:l(C,v),strategy:a})),[D,y,O.draggable,O.droppable,M,C,S,v,w,a]);return o().createElement(R.Provider,{value:k},t)}const E=e=>{let{id:t,items:n,activeIndex:r,overIndex:o}=e;return c(n,r,o).indexOf(t)};const D=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:o,items:i,newIndex:s,previousItems:c,previousContainerId:a,transition:l}=e;if(!l||!r){return false}if(c!==i&&o===s){return false}if(n){return true}return s!==o&&t===a};const S={duration:200,easing:"ease"};const I="transform";const N=s.ux.Transition.toString({property:I,duration:0,easing:"linear"});const M={roleDescription:"sortable"};function O(e){let{disabled:t,index:n,node:o,rect:c}=e;const[a,l]=(0,r.useState)(null);const u=(0,r.useRef)(n);(0,s.LI)((()=>{if(!t&&n!==u.current&&o.current){const e=c.current;if(e){const t=(0,i.VK)(o.current,{ignoreTransform:true});const n={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};if(n.x||n.y){l(n)}}}if(n!==u.current){u.current=n}}),[t,n,o,c]);(0,r.useEffect)((()=>{if(a){l(null)}}),[a]);return a}function k(e){let{animateLayoutChanges:t=D,attributes:n,disabled:o,data:c,getNewIndex:a=E,id:l,strategy:d,resizeObserverConfig:f,transition:h=S}=e;const{items:g,containerId:v,activeIndex:p,disabled:b,disableTransforms:m,sortedRects:y,overIndex:w,useDragOverlay:x,strategy:C}=(0,r.useContext)(R);const k=L(o,b);const T=g.indexOf(l);const A=(0,r.useMemo)((()=>({sortable:{containerId:v,index:T,items:g},...c})),[v,c,T,g]);const j=(0,r.useMemo)((()=>g.slice(g.indexOf(l))),[g,l]);const{rect:B,node:z,isOver:X,setNodeRef:P}=(0,i.Zj)({id:l,data:A,disabled:k.droppable,resizeObserverConfig:{updateMeasurementsFor:j,...f}});const{active:F,activatorEvent:Y,activeNodeRect:J,attributes:U,setNodeRef:H,listeners:K,isDragging:q,over:W,setActivatorNodeRef:G,transform:V}=(0,i.O1)({id:l,data:A,attributes:{...M,...n},disabled:k.draggable});const _=(0,s.HB)(P,H);const $=Boolean(F);const Z=$&&!m&&u(p)&&u(w);const Q=!x&&q;const ee=Q&&Z?V:null;const te=d!=null?d:C;const ne=Z?ee!=null?ee:te({rects:y,activeNodeRect:J,activeIndex:p,overIndex:w,index:T}):null;const re=u(p)&&u(w)?a({id:l,items:g,activeIndex:p,overIndex:w}):T;const oe=F==null?void 0:F.id;const ie=(0,r.useRef)({activeId:oe,items:g,newIndex:re,containerId:v});const se=g!==ie.current.items;const ce=t({active:F,containerId:v,isDragging:q,isSorting:$,id:l,index:T,items:g,newIndex:ie.current.newIndex,previousItems:ie.current.items,previousContainerId:ie.current.containerId,transition:h,wasDragging:ie.current.activeId!=null});const ae=O({disabled:!ce,index:T,node:z,rect:B});(0,r.useEffect)((()=>{if($&&ie.current.newIndex!==re){ie.current.newIndex=re}if(v!==ie.current.containerId){ie.current.containerId=v}if(g!==ie.current.items){ie.current.items=g}}),[$,re,v,g]);(0,r.useEffect)((()=>{if(oe===ie.current.activeId){return}if(oe&&!ie.current.activeId){ie.current.activeId=oe;return}const e=setTimeout((()=>{ie.current.activeId=oe}),50);return()=>clearTimeout(e)}),[oe]);return{active:F,activeIndex:p,attributes:U,data:A,rect:B,index:T,newIndex:re,items:g,isOver:X,isSorting:$,isDragging:q,listeners:K,node:z,overIndex:w,over:W,setNodeRef:_,setActivatorNodeRef:G,setDroppableNodeRef:P,setDraggableNodeRef:H,transform:ae!=null?ae:ne,transition:le()};function le(){if(ae||se&&ie.current.newIndex===T){return N}if(Q&&!(0,s.vd)(Y)||!h){return undefined}if($||ce){return s.ux.Transition.toString({...h,property:I})}return undefined}}function L(e,t){var n,r;if(typeof e==="boolean"){return{draggable:e,droppable:false}}return{draggable:(n=e==null?void 0:e.draggable)!=null?n:t.draggable,droppable:(r=e==null?void 0:e.droppable)!=null?r:t.droppable}}function T(e){if(!e){return false}const t=e.data.current;if(t&&"sortable"in t&&typeof t.sortable==="object"&&"containerId"in t.sortable&&"items"in t.sortable&&"index"in t.sortable){return true}return false}const A=[i.g4.Down,i.g4.Right,i.g4.Up,i.g4.Left];const j=(e,t)=>{let{context:{active:n,collisionRect:r,droppableRects:o,droppableContainers:c,over:a,scrollableAncestors:l}}=t;if(A.includes(e.code)){e.preventDefault();if(!n||!r){return}const t=[];c.getEnabled().forEach((n=>{if(!n||n!=null&&n.disabled){return}const s=o.get(n.id);if(!s){return}switch(e.code){case i.g4.Down:if(r.top<s.top){t.push(n)}break;case i.g4.Up:if(r.top>s.top){t.push(n)}break;case i.g4.Left:if(r.left>s.left){t.push(n)}break;case i.g4.Right:if(r.left<s.left){t.push(n)}break}}));const u=(0,i.ey)({active:n,collisionRect:r,droppableRects:o,droppableContainers:t,pointerCoordinates:null});let d=(0,i._8)(u,"id");if(d===(a==null?void 0:a.id)&&u.length>1){d=u[1].id}if(d!=null){const e=c.get(n.id);const t=c.get(d);const a=t?o.get(t.id):null;const u=t==null?void 0:t.node.current;if(u&&a&&e&&t){const n=(0,i.hI)(u);const o=n.some(((e,t)=>l[t]!==e));const c=B(e,t);const d=z(e,t);const f=o||!c?{x:0,y:0}:{x:d?r.width-a.width:0,y:d?r.height-a.height:0};const h={x:a.left,y:a.top};const g=f.x&&f.y?h:(0,s.$X)(h,f);return g}}}return undefined};function B(e,t){if(!T(e)||!T(t)){return false}return e.data.current.sortable.containerId===t.data.current.sortable.containerId}function z(e,t){if(!T(e)||!T(t)){return false}if(!B(e,t)){return false}return e.data.current.sortable.index<t.data.current.sortable.index}},4285:(e,t,n)=>{n.d(t,{$X:()=>D,D9:()=>w,DC:()=>M,Ey:()=>b,FJ:()=>c,Gj:()=>m,HB:()=>i,IH:()=>E,Jj:()=>l,LI:()=>g,Ld:()=>R,Nq:()=>s,Re:()=>d,UG:()=>a,Yz:()=>p,qk:()=>u,r3:()=>h,so:()=>L,ux:()=>O,vZ:()=>f,vd:()=>I,wm:()=>y,zX:()=>v});var r=n(7363);var o=n.n(r);function i(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++){t[n]=arguments[n]}return(0,r.useMemo)((()=>e=>{t.forEach((t=>t(e)))}),t)}const s=typeof window!=="undefined"&&typeof window.document!=="undefined"&&typeof window.document.createElement!=="undefined";function c(e){const t=Object.prototype.toString.call(e);return t==="[object Window]"||t==="[object global]"}function a(e){return"nodeType"in e}function l(e){var t,n;if(!e){return window}if(c(e)){return e}if(!a(e)){return window}return(t=(n=e.ownerDocument)==null?void 0:n.defaultView)!=null?t:window}function u(e){const{Document:t}=l(e);return e instanceof t}function d(e){if(c(e)){return false}return e instanceof l(e).HTMLElement}function f(e){return e instanceof l(e).SVGElement}function h(e){if(!e){return document}if(c(e)){return e.document}if(!a(e)){return document}if(u(e)){return e}if(d(e)||f(e)){return e.ownerDocument}return document}const g=s?r.useLayoutEffect:r.useEffect;function v(e){const t=(0,r.useRef)(e);g((()=>{t.current=e}));return(0,r.useCallback)((function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++){n[r]=arguments[r]}return t.current==null?void 0:t.current(...n)}),[])}function p(){const e=(0,r.useRef)(null);const t=(0,r.useCallback)(((t,n)=>{e.current=setInterval(t,n)}),[]);const n=(0,r.useCallback)((()=>{if(e.current!==null){clearInterval(e.current);e.current=null}}),[]);return[t,n]}function b(e,t){if(t===void 0){t=[e]}const n=(0,r.useRef)(e);g((()=>{if(n.current!==e){n.current=e}}),t);return n}function m(e,t){const n=(0,r.useRef)();return(0,r.useMemo)((()=>{const t=e(n.current);n.current=t;return t}),[...t])}function y(e){const t=v(e);const n=(0,r.useRef)(null);const o=(0,r.useCallback)((e=>{if(e!==n.current){t==null?void 0:t(e,n.current)}n.current=e}),[]);return[n,o]}function w(e){const t=(0,r.useRef)();(0,r.useEffect)((()=>{t.current=e}),[e]);return t.current}let x={};function R(e,t){return(0,r.useMemo)((()=>{if(t){return t}const n=x[e]==null?0:x[e]+1;x[e]=n;return e+"-"+n}),[e,t])}function C(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++){r[o-1]=arguments[o]}return r.reduce(((t,n)=>{const r=Object.entries(n);for(const[n,o]of r){const r=t[n];if(r!=null){t[n]=r+e*o}}return t}),{...t})}}const E=C(1);const D=C(-1);function S(e){return"clientX"in e&&"clientY"in e}function I(e){if(!e){return false}const{KeyboardEvent:t}=l(e.target);return t&&e instanceof t}function N(e){if(!e){return false}const{TouchEvent:t}=l(e.target);return t&&e instanceof t}function M(e){if(N(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}else if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}if(S(e)){return{x:e.clientX,y:e.clientY}}return null}const O=Object.freeze({Translate:{toString(e){if(!e){return}const{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e){return}const{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(!e){return}return[O.Translate.toString(e),O.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}});const k="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function L(e){if(e.matches(k)){return e}return e.querySelector(k)}}}]);