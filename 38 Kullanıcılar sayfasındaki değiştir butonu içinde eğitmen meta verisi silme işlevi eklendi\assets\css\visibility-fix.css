/**
 * GÖRÜNÜRLÜK DÜZELTMELERİ
 * <PERSON>u dosya, dashboard'daki bazı elementlerin görünürlük sorunlarını düzeltmek için kullanılır.
 */

/* Tüm içerik elementlerinin görünürl<PERSON><PERSON><PERSON><PERSON><PERSON>ğ<PERSON> */
.tutor-dashboard-content-inner,
.tutor-dashboard-content-inner *,
.tutor-frontend-dashboard-course-progress,
.tutor-frontend-dashboard-course-progress *,
.tutor-course-listing-item,
.tutor-course-listing-item *,
.tutor-course-card,
.tutor-course-card * {
    opacity: 1 !important;
    visibility: visible !important;
}

/* <PERSON><PERSON> kartları i<PERSON> d<PERSON>zel<PERSON> */
.tutor-course-card,
.tutor-course-listing-item {
    display: block !important;
    background-color: #fff !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
    margin-bottom: 20px !important;
    overflow: hidden !important;
    border: 1px solid #e9ecef !important;
    transition: all 0.3s ease !important;
}

.tutor-course-card:hover,
.tutor-course-listing-item:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 8px 15px rgba(67, 97, 238, 0.15) !important;
}

/* <PERSON>rs resimleri için düzeltmeler */
.tutor-course-thumbnail,
.tutor-course-thumbnail a {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    overflow: hidden !important;
    border-radius: 8px 8px 0 0 !important;
    position: relative !important;
}

.tutor-course-thumbnail img {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    width: 100% !important;
    height: auto !important;
    object-fit: cover !important;
    transition: transform 0.5s ease !important;
}

.tutor-course-thumbnail:hover img {
    transform: scale(1.1) !important;
}

/* Tablo görünürlüğü düzeltmeleri */
.tutor-table,
.tutor-table *,
.tutor-table tr,
.tutor-table td,
.tutor-table th {
    opacity: 1 !important;
    visibility: visible !important;
}

.tutor-table {
    display: table !important;
    width: 100% !important;
    border-collapse: separate !important;
    border-spacing: 0 !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    margin-bottom: 30px !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
    border: 1px solid #e9ecef !important;
}

.tutor-table tr {
    display: table-row !important;
    transition: all 0.3s ease !important;
}

.tutor-table tr:hover {
    background-color: rgba(67, 97, 238, 0.05) !important;
}

.tutor-table th {
    display: table-cell !important;
    padding: 15px !important;
    font-weight: 600 !important;
    color: #212327 !important;
    background-color: #f8f9fa !important;
    border-bottom: 2px solid #e9ecef !important;
    text-align: left !important;
}

.tutor-table td {
    display: table-cell !important;
    padding: 15px !important;
    border-bottom: 1px solid #e9ecef !important;
    color: #6c757d !important;
    vertical-align: middle !important;
}

.tutor-table tr:last-child td {
    border-bottom: none !important;
}

/* Kurslarım tablosu için özel stiller */
.tutor-dashboard-content-inner table {
    display: table !important;
    width: 100% !important;
    border-collapse: separate !important;
    border-spacing: 0 !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    margin-bottom: 30px !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
    border: 1px solid #e9ecef !important;
}

.tutor-dashboard-content-inner table tr {
    display: table-row !important;
    transition: all 0.3s ease !important;
}

.tutor-dashboard-content-inner table tr:hover {
    background-color: rgba(67, 97, 238, 0.05) !important;
}

.tutor-dashboard-content-inner table th {
    display: table-cell !important;
    padding: 15px !important;
    font-weight: 600 !important;
    color: #212327 !important;
    background-color: #f8f9fa !important;
    border-bottom: 2px solid #e9ecef !important;
    text-align: left !important;
}

.tutor-dashboard-content-inner table td {
    display: table-cell !important;
    padding: 15px !important;
    border-bottom: 1px solid #e9ecef !important;
    color: #6c757d !important;
    vertical-align: middle !important;
}

.tutor-dashboard-content-inner table tr:last-child td {
    border-bottom: none !important;
}

/* Liste görünürlüğü düzeltmeleri */
.tutor-list,
.tutor-list * {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Animasyon düzeltmeleri */
.tutor-animate-card,
.tutor-profile-title,
.tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-mb-24 {
    opacity: 1 !important;
    animation: none !important;
    transform: none !important;
    transition: none !important;
}

/* Devam Eden Kurslar bölümü düzeltmeleri */
.tutor-frontend-dashboard-course-progress {
    display: block !important;
    margin-top: 30px !important;
}

/* Devam Eden Kurslar başlığı düzeltmesi */
.tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-text-capitalize.tutor-mb-24 {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    margin-bottom: 24px !important;
    font-size: 18px !important;
    font-weight: 500 !important;
    color: #212327 !important;
}

/* İlerleme çubuğu düzeltmeleri */
.tutor-progress-bar {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: 8px !important;
    background-color: #e9ecef !important;
    border-radius: 999px !important;
    overflow: hidden !important;
    margin: 10px 0 !important;
}

.tutor-progress-value {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: 100% !important;
    background: linear-gradient(90deg, var(--tutor-color-primary), var(--tutor-color-primary)) !important;
    border-radius: 999px !important;
}

/* Kurs kartları için düzeltmeler */
.tutor-course-listing-grid-3 .tutor-course-listing-item {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

/* Kurs içerik elementleri için düzeltmeler */
.tutor-course-listing-item-title,
.tutor-course-listing-item-title a,
.tutor-course-card-title,
.tutor-course-card-title a {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    color: #212327 !important;
    margin: 15px 0 10px !important;
    padding: 0 15px !important;
    text-decoration: none !important;
    transition: color 0.3s ease !important;
    line-height: 1.4 !important;
}

.tutor-course-listing-item-title a:hover,
.tutor-course-card-title a:hover {
    color: #4361ee !important;
}

.tutor-course-listing-item-meta,
.tutor-course-listing-item-meta *,
.tutor-course-card-meta,
.tutor-course-card-meta * {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
    font-size: 14px !important;
    color: #6c757d !important;
    margin: 5px 0 !important;
    padding: 0 15px !important;
}

.tutor-course-listing-item-footer,
.tutor-course-listing-item-footer *,
.tutor-course-card-footer,
.tutor-course-card-footer * {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
    padding: 15px !important;
    border-top: 1px solid #e9ecef !important;
    margin-top: 10px !important;
}

/* Flex container düzeltmeleri */
.tutor-course-listing-grid {
    display: flex !important;
    flex-wrap: wrap !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Yıldız derecelendirme düzeltmeleri */
.tutor-ratings,
.tutor-ratings-stars,
.tutor-ratings-average,
.tutor-ratings-count,
.tutor-star-rating-group,
.tutor-star-rating-group *,
.tutor-ratings-container,
.tutor-ratings-container * {
    display: inline-block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

.tutor-ratings-stars,
.tutor-star-rating-group {
    color: #f9a134 !important;
    font-size: 14px !important;
    margin-right: 5px !important;
    letter-spacing: 2px !important;
}

.tutor-ratings-average,
.tutor-ratings-count {
    font-size: 14px !important;
    color: #6c757d !important;
}

/* Kurslarım tablosundaki yıldız derecelendirmeleri */
.tutor-dashboard-content-inner table .tutor-star-rating-group {
    display: flex !important;
    align-items: center !important;
    opacity: 1 !important;
    visibility: visible !important;
}

.tutor-dashboard-content-inner table .tutor-icon-star-line,
.tutor-dashboard-content-inner table .tutor-icon-star-full,
.tutor-dashboard-content-inner table .tutor-icon-star-half {
    display: inline-block !important;
    opacity: 1 !important;
    visibility: visible !important;
    color: #f9a134 !important;
    font-size: 16px !important;
    margin-right: 2px !important;
}

.tutor-dashboard-content-inner table .tutor-icon-star-line {
    color: #e9ecef !important;
}

/* Tamamlanma yüzdesi düzeltmeleri */
.tutor-course-completion-text {
    font-size: 14px !important;
    color: #6c757d !important;
    margin: 5px 0 !important;
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Kurs tamamlanma sayısı düzeltmeleri */
.tutor-course-completed-text {
    font-size: 14px !important;
    color: #6c757d !important;
    margin: 5px 0 !important;
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Devam Et butonu düzeltmeleri */
.tutor-btn.tutor-btn-outline-primary {
    display: inline-block !important;
    opacity: 1 !important;
    visibility: visible !important;
    padding: 8px 16px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    color: var(--tutor-color-primary) !important;
    background-color: transparent !important;
    border: 1px solid var(--tutor-color-primary) !important;
    border-radius: 6px !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    text-align: center !important;
}

.tutor-btn.tutor-btn-outline-primary:hover {
    color: var(--tutor-color-primary) !important;
    background-color: color-mix(in srgb, var(--tutor-color-primary) 10%, transparent) !important;
}
