/**
 * Sidebar Hover Düzeltmesi
 * Bu CSS dosyası, sidebar menü öğelerinin hover durumundaki rengini değişken rengine göre ayarlar.
 * Böylece tüm hover efektleri tema renkleriyle uyumlu olur.
 */

/* Tüm sidebar menü öğelerinin hover durumu */
.tutor-dashboard-menu-item-link:hover {
    background-color: var(--tutor-primary-lighter) !important;
    color: var(--tutor-color-primary) !important;
    transform: translateX(5px) !important;
}

/* Sidebar menü ikonlarının hover durumu */
.tutor-dashboard-menu-item-link:hover i,
.tutor-dashboard-menu-item-link:hover .tutor-icon-dashboard,
.tutor-dashboard-menu-item-link:hover .tutor-dashboard-menu-item-icon {
    color: var(--tutor-color-primary) !important;
}

/* Yorumlar menü öğesinin hover durumu - özel seçici */
.tutor-dashboard-menu-item-yorumlar .tutor-dashboard-menu-item-link:hover,
.tutor-dashboard-menu-item-reviews .tutor-dashboard-menu-item-link:hover {
    background-color: var(--tutor-primary-lighter) !important;
    color: var(--tutor-color-primary) !important;
    transform: translateX(5px) !important;
}

/* Yorumlar menü öğesi ikonunun hover durumu - özel seçici */
.tutor-dashboard-menu-item-yorumlar .tutor-dashboard-menu-item-link:hover i,
.tutor-dashboard-menu-item-yorumlar .tutor-dashboard-menu-item-link:hover .tutor-dashboard-menu-item-icon,
.tutor-dashboard-menu-item-reviews .tutor-dashboard-menu-item-link:hover i,
.tutor-dashboard-menu-item-reviews .tutor-dashboard-menu-item-link:hover .tutor-dashboard-menu-item-icon {
    color: var(--tutor-color-primary) !important;
}

/* Tüm sidebar menü öğelerinin metin rengi */
.tutor-dashboard-menu-item-link .tutor-dashboard-menu-item-text {
    transition: var(--tutor-transition) !important;
}

/* Tüm sidebar menü öğelerinin hover durumundaki metin rengi */
.tutor-dashboard-menu-item-link:hover .tutor-dashboard-menu-item-text {
    color: var(--tutor-color-primary) !important;
}
