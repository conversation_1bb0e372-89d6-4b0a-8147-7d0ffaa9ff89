=== Tutor LMS - eLearning and online course solution ===
Contributors: themeum
Donate link: https://tutorlms.com
Tags: lms, course, elearning, education, learning management system
Requires at least: 5.3
Tested up to: 6.7
Requires PHP: 7.4
Stable tag: 3.3.1
License: GPLv3
License URI: https://www.gnu.org/licenses/gpl-3.0.html

A complete WordPress LMS plugin to create any eLearning website easily.

== Description ==

Tutor LMS is a lightweight, feature-packed, and robust WordPress LMS plugin to create & sell courses online easily. All the features of this learning management system hit all the checkpoints for a full-fledged online course marketplace.

You can create unlimited courses, quizzes, interactive lessons, generate reports, making Tutor LMS the best free WordPress LMS plugin. So, it’s become a breeze to manage educational institutes, online courses, or eLearning websites without writing a single line of code.

👉 [Tutor LMS Pro](https://tutorlms.com/pricing)

👉 [Tutor LMS Themes](https://www.themeum.com/wordpress-themes/)

👉 [Live Demo](https://demo.themeum.com/tutor/)

👉 [Documentation](https://docs.themeum.com/tutor-lms/)

You can also join our [official Facebook Community](https://www.facebook.com/groups/tutorlms) to know all the latest news and be part of the Tutor LMS.

Also, check out our Tutor LMS overview video:

https://www.youtube.com/watch?v=78t8LnQjOVs 

Overall, Tutor LMS is the best WordPress LMS plugin for eLearning enthusiasts. It comes with every core feature you’ll need to build a perfect eLearning website. Additionally, it also has plenty of premium add-ons to empower the core plugin more! A few important addons are Course Builder, Certificate, Social Login, Reports, Content Drip, Assignments, Calendar, etc.

🔥 [Get Tutor LMS](https://tutorlms.com/) today and create, customize, manage, and sell online courses efficiently.

== Listen to What People Have to Say: ==

🙆 “After hours and hours of research and trying several LMS platforms, I have decided to purchase Tutor LMS. I’ve run into some bugs, but they were quickly solved by their customer support team. I’m using it for some time now and have been able to check out the beta. It will be a great step and make this amazing LMS even better!” —nowhereman78

🙆 “This LMS has pretty much everything you’d expect from an LMS and more: Course builder, lessons, several quiz/assignment types, video embed, zoom integration, front-end dashboard, payment/woocommerce integrations, and a lot of other great add-ons. It provides a streamlined experience and is easy to use for both admins, tutors and students. Great support and reasonably priced.” —mariusandersen

🙆 “I’ve been using WordPress-based LMSs for a few years now and after just a few weeks, this has to be my favorite. Let’s hope that continues.” —terryheick

== 🌟 Top Features ==

Here are the most notable features of Tutor LMS:
🏗️ Advanced course builder
🛒 Native eCommerce
📆 Built-in subscriptions
🖥️Tutor LMS AI Studio
📚 Unlimited courses and lessons
🎓 Course bundle
👁️ Course preview
🧩 Advanced quiz builder
⏱️ Quiz timer
📝 Quiz attempts
📊 Frontend student dashboard
📌 Personalized dashboard for each role (admins, instructors, students)
📽️ Video lessons
📹 Multiple video sources (self-hosted, YouTube, Vimeo, embedded video, etc)
📅 Content drip
👩‍🏫 Unlimited teacher profiles
📝 Lesson management
🏆 Advanced drag-and-drop certificate builder
💳 Centralized monetization settings
💰 Earning & commission allocation
💸 Multiple withdrawal options
📜 Purchase history
🎨 Course customization
📊 Course widget
📝 Instructor registration form
⭐ Course review and rating system
❓ Q&A for students with the course teacher
🎥 Video thumbnail (trailer/teaser)
🚀 Tracking course progress
🌟 Course difficulty level
⏳ Define course duration
🌐 Course marketplace
📋 Add course requirements and instruction
🔦 Spotlight mode
🏗️ Page builder support
📚 Gutenberg compatible
🔐 Content security
📈 Advanced analytics and more
👥 Manual enrollment
📊 Creating custom grading scales
🔔 Automated notifications
👩‍🏫 Multi-instructor system
🔐 Social login functionalities
📧 Email verification
✉️ Email template editor
🔒 Tutor LMS REST API authentication support
👤 Manage active login sessions
📱 Two-factor authentication
🛡️ Fraud protection
🔄 RTL ready
🌐 Multilingual support

You can learn more about our product from [our website](https://tutorlms.com/).


== Tutor LMS Extended Features ==

Tutor LMS is a perfect solution for anyone who wants to create a full-fledged learning management system. Here are some core uses of Tutor LMS:

👥 Blended Learning
📚 Community Education
🏫 Industrial Training
💻 Online Classroom
🎓 Training Courses
🧑‍💼 Employee Training

These are just a few possibilities! There’s a whole new world of eLearning that’s yet to be discovered using this WordPress LMS plugin.

== 🎨 MINIMALISTIC AND UNIFORM DESIGN ==

Tutor LMS has the most minimalistic, functional, and uniform user interface for an LMS plugin. The entire interface has been redesigned with perfectionism in mind. It's also more accessible now to make things easier for users with disabilities.

== 📒 INTUITIVE COURSE BUILDER ==

With Tutor LMS's state-of-the-art course builder, creating engaging online courses has never been easier. This advanced WordPress LMS plugin empowers you to design unlimited courses with unlimited lessons, quizzes, assignments, videos, and more.
 
To focus solely on the course-building process, Tutor LMS also lets you hide headers and footers in the course builder using the spotlight mode. So, whether you're a seasoned educator or just starting your online teaching journey, this WordPress LMS plugin equips you with the tools you need to create dynamic and interactive learning experiences for your students. 

== 🧩 ADVANCED QUIZ BUILDER ==

Create unlimited quizzes with this WordPress course plugin. You can add questions, set the time limit, restrict attempts, set passing grades, etc, right from this WordPress LMS plugin.

This free LMS plugin supports various quizzes that you can use to fulfill all your quizzing needs. For example, true/false, multiple choice, single choice, matching, Fill-in-the-blanks, etc.

== 🛒 NATIVE ECOMMERCE ==

Tutor LMS has the most important streamlined eCommerce solution to sell courses directly without relying on third-party plugins. It simplifies the payment processing with support for lots of popular payment gateways, like PayPal, Stripe, Razorpay, etc. 

With features like advanced coupon management, you can create custom codes or automatic discounts, set validity periods, and run promotional campaigns. Managing taxes is equally simple, with auto-calculated regional tax rates ensuring compliance for learners across the globe. 

Additionally, the upgraded order management interface enables you to track, update, and refund orders quickly and efficiently. Native eCommerce brings unparalleled speed and convenience, empowering you to manage your online course sales like a pro.

== 📆 BUILT-IN SUBSCRIPTIONS ==

The built-in Subscriptions feature in Tutor LMS offers a fully integrated solution for managing recurring payments. You can design flexible subscription plans tailored to your audience, including monthly, yearly, or custom durations. Add enrollment fees, schedule exclusive discounts, and even offer certificates as part of premium subscription packages. 

With subscriptions built directly into the platform, you no longer need external plugins, resulting in a faster, more reliable site. This feature provides complete control over pricing models, ensuring a smooth workflow for instructors and a seamless experience for students. 

== 🖥️ AI STUDIO ==

Tutor LMS revolutionizes course creation with its cutting-edge AI Studio. This innovative tool allows instructors to generate high-quality course content, including course outlines, lessons, quizzes, etc with minimal effort. Simply provide a prompt, and the AI will create structured, engaging content in seconds. 

The Tutor LMS AI Studio also supports feature image generation, helping you craft visually appealing thumbnails that stand out. By automating time-consuming tasks, AI Studio enables educators to focus on teaching and enhancing the learning experience.

== 📚 COURSE BUNDLES ==

With Tutor LMS, you can sell multiple courses in a bundle. Bundling related courses allows you to present them as a cohesive package. It encourages students to explore complementary topics or skills that are highly relevant to your course.

You can also offer these bundles at a discounted price, making them more enticing for learners. Alternatively, bundles can be structured to guide students through a predefined learning path. It will ensure a logical progression of skills and knowledge acquisition. This feature not only increases course accessibility but also enhances the overall learning experience by providing curated learning journeys tailored to students' needs and interests.

== 🖥 FRONTEND STUDENT DASHBOARD ==

Each student registered on your WordPress learning management system will have a frontend dashboard to see their enrolled courses, progress, results, announcements, etc.

== 🧑‍🏫 CREATE COURSE MARKETPLACE & SHARE COMMISSION ==

Tutor LMS is the best LMS plugin to create a course marketplace. You can add unlimited courses and instructors using Tutor LMS. As an Admin, you can review instructor profiles before approval, manage instructor’s earnings distribution, set commission rates, deduct charges, and more. Instructors can also withdraw their earnings conveniently via bank transfer, PayPal, eCheck, etc.

== 📹 MULTIMEDIA ATTACHMENTS ==

Keep your eLearning students engaged using multimedia lessons, H5P interactive lessons, SCORM files, etc. Tutor LMS supports versatile video sources such as Vimeo, YouTube, Bunny Stream, etc. You can also set an introductory video for your course. This video will act as the featured video of your course.

== 💬 ENGAGE STUDENTS WITH LIVE LESSONS ==

Conduct live video sessions with students within Tutor LMS courses & lessons and increase interaction with students and connect with them. Instructors can schedule real-time video meetings using Google Meet, Zoom, etc. Just share the meeting link within the lesson, and students can join your live classes with one click.

Video sessions enable interactive class lectures, discussions, and collaboration. During the live lessons, instructors can use all of the required features to conduct a live class. For example, screen sharing, live question answering, engaging students face-to-face online, etc. 

== 🧑‍🎓 STUDENT QUESTION AND ANSWERS (Q&A) ==

Students can submit questions about courses from their respective profiles even before enrolling in a course. This feature of the Tutor LMS plugin encourages student interactions and helps boost conversion rates.

== ✉️ BUILT-IN EMAIL MARKETING AND AUTOMATION ==

Tutor LMS supports automatic email notifications for admins, instructors, and students. It is equipped with 40+ email triggers and numerous placeholders. You can use those placeholders to send highly customizable and personalized emails right from your LMS plugin. 

The built-in email editor of Tutor LMS lets you use those placeholders in both the subject lines and the body of your customized email. A few common automated email triggers are user registration, course enrollment, inactivity reminders, quiz completions, course completions, assignment grading, announcements, eCommerce orders, subscriptions, etc. This comprehensive system ensures that all user interactions are met with relevant and timely email notifications.

== 💵 MULTIPLE COURSE MONETIZATION TOOLS ==

You can monetize your eLearning courses using the Tutor LMS native eCommerce. Besides, this WordPress LMS plugin has supports for various monetization plugins such as WooCommerce, Easy Digital Downloads, Paid Membership Pro, and Restrict Content Pro. These integrations streamline the course selling and commission allocation process, making it easy to manage your financial transactions and track revenue.

With the native eCommerce engine, you can not only sell courses but also generate coupons and manage taxation too. It will help you to offer discounts and set taxes with detailed billing information. Additionally, you can monitor sales data and analytics to track your business growth and make informed decisions.

== 🏗️ PAGE BUILDER COMPATIBILITY ==

Tutor LMS is a versatile WordPress learning management system plugin that is compatible with popular page builders like Gutenberg, Elementor, Divi, Oxygen Builder, etc. It also has a dedicated [Elementor addons plugin](https://wordpress.org/plugins/tutor-lms-elementor-addons/), [Divi modules](https://wordpress.org/plugins/tutor-lms-divi-modules/), and [Oxygen builder plugin](https://wordpress.org/plugins/oxygen-tutor-lms/).

These integrations ensure seamless design capabilities, enabling you to use each builder's unique tools and templates. Thus, you can craft a visually appealing and highly functional eLearning platform that aligns with your brand and effectively engages your students using this free LMS plugin.

== 🛡️ CONTENT SECURITY ==

Tutor LMS provides top-notch content security to safeguard your eLearning website. With features like copy protection to prevent unauthorized duplication, hotlink prevention to block external sites from misusing your files, and active login session management, you can easily monitor and control user access.

Besides, email verification ensures that only legitimate users can enroll in your courses. Additionally, Tutor LMS includes security measures such as honeypot and reCAPTCHA integration to prevent bots and spam, two-factor authentication for enhanced login security, and regular updates to patch any vulnerabilities. All of these robust security features work together to safeguard your content and make it the best WordPress LMS plugin regarding content security.

== 🚀 POWERFUL ADD-ONS ==

This WordPress online course plugin has lots of [advanced add-ons](https://tutorlms.com/addons/) to enhance your eLearning platform. A few notable add-ons are Course Bundle, Subscriptions, Content Drip, Certificate, Reports, Social Login, Email, Quiz Export/Import, H5P, Gradebook, Prerequisite, Google Meet, etc.

== 📅 CONTENT DRIP TO SCHEDULE COURSE CONTENT ==

Tutor LMS's Content Drip feature allows you to schedule when your course content will be released. This means you can unlock lessons, quizzes, and other materials based on certain prerequisites or specific future dates. By gradually providing access to new content over time rather than all at once, Content Drip helps keep students engaged and motivated.

== 🧑🏻‍🏫 INSTRUCTOR COLLABORATION ==

With Tutor LMS's Multi-Instructor addon, multiple instructors can work together on a single course. They can collaborate to create and manage lectures and quizzes of a single course. Each instructor can track learner progress from their own account.

This teamwork allows for a richer learning experience as instructors bring their unique expertise to the course. It also makes managing the course easier, as responsibilities are shared among several instructors. This feature is perfect for large courses that benefit from diverse teaching styles and knowledge areas.

== 📜 DRAG AND DROP CERTIFICATE BUILDER ==

Tutor LMS offers an advanced drag-and-drop Certificate Builder. You can design a certificate from scratch or use a pre-designed template. It comes with multiple pre-designed templates, plenty of design elements, backdrops, media files, etc. You can also add a QR code to the certificates to ease the verification process. Overall, this tool makes it easy to create stunning and professional-looking certificates for your students.

Besides, students can also share their certificates on social media to showcase their educational achievements. This feature not only motivates students but also helps promote your courses to a wider audience. The Certificate Builder is user-friendly and flexible, allowing you to customize certificates to match your brand and course requirements.

== 🔗 SINGLE CLICK SOCIAL LOGIN WITH Google, Facebook, and Twitter accounts ==

Tutor LMS offers one-click login functionality using the Social Login addon. Students can register and log into your eLearning website using their existing Google, Facebook, and Twitter accounts. 

== 📊 INSIGHTFUL REPORTS ==

The Reports addon of Tutor LMS offers comprehensive data on student progress, course performance, analytics, and more. Administrators can access detailed information about Courses, Reviews, Sales, Student data, Earning data, etc right from their LMS platform. These reports provide valuable insights into course effectiveness, helping instructors implement more informed teaching strategies.

By analyzing student engagement, course completion rates, and other metrics, instructors can identify areas for improvement and optimize their courses for better learning outcomes. With Tutor LMS Reports, instructors have the tools they need to track progress, measure success, and continually enhance their students' learning experience.

== 🌐 MULTILINGUAL SUPPORT ==

Tutor LMS eLearning plugin provides multilingual support using WPML, Weglot, TranslatePress, etc. Multilingual courses allow students to access courses in their preferred language. 

== 🔄 MIGRATION FROM OTHER LMS ==

Tutor LMS provides a seamless [migration tool](https://wordpress.org/plugins/tutor-lms-migration-tool/) to migrate courses from LearnDash, LearnPress, and Lifter LMS. You can effortlessly transfer all course data, sales data, student data, and relevant information to Tutor LMS for a smooth transition.


== Other Notable Features ==

Here are a few other notable features of this free WordPress LMS plugin.

*   Google Classroom integration
    
*   Zoom integration
    
*   Quiz Export/Import

*   Course Preview
    
*   Course Attachments
    
*   Notifications
    
*   Calendar
    
*   BuddyPress support
    
*   H5P integration
    

All of these Tutor LMS features allow the users to design a more powerful and diverse learning management system.

== Frequently Asked Questions ==

= Is Tutor LMS free? =  
Yes, Tutor LMS is available for free with a wide range of features to help you create and manage courses. For access to additional advanced features, you can upgrade to the Pro version.

= How do I sell courses with Tutor LMS? =
Tutor LMS comes with a Native eCommerce system that'll allow you to sell your courses directly on your WordPress site. You can also manage payments, discounts, and taxes within the platform.

= Can I create quizzes with Tutor LMS? =
Yes, Tutor LMS offers an advanced quiz builder that allows you to create a variety of quiz types including multiple-choice, true/false, matching, and more. You can also set time limits, restrict attempts, and define passing grades.

= Can I use Tutor LMS for membership sites? =
Yes, Tutor LMS has a built-in subscription feature which will allow you to create membership-based eLearning platforms.

= Can I offer certifications with Tutor LMS? =
Tutor LMS allows you to offer certificates to your students upon course completion. You can create and customize the certificate design and include student information, course details, and your branding using the Tutor LMS Certificate Builder.


== Screenshots ==

1. Single course page: Get a detailed course details page to instantly grab students' attention
2. Dashboard: Intuitive & personalized dashboard for instructors and students
3. Course Builder: More organized and intuitive interface for seamless course creation
4. Course Curriculum: Create topics, lessons, quizzes, assignments, live classes, etc
5. Quiz Builder: A powerful Quiz Builder revived with a new look and feel
6. Assignments: Assess students using the handy Assignment feature 
7. AI Studio: Generate entire course including lessons, quizzes, thumbnails, etc with AI
8. Native eCommerce: Sell courses directly within Tutor LMS, supports popular payment gateways
9. Order Management: Manage orders, update order status, initiate refunds, etc
10. Coupon Management: Create custom coupon codes or automatic discounts, set validity, etc
11. Tax Management: Set up regional tax rates for seamless tax management
12. Subscriptions: Create recurring revenue with built-in subscriptions feature
13. Advanced Analytics: Get next-level report insights with detailed advanced analytics
14. Email Templates: Edit email template content right from the settings to make life easy
15. Course Bundle: Sell multiple courses in bundles as a single product
16. Multi-Instructor: Multiple instructors can collaborate on a single course 
17. Certificate Builder: Fully customizable certificates using drag & drop Certificate Builder
18. Q&A Section: Engaging Q&A functionality to encourage better communication
19. Q&A List: Use the Q&A page to easily manage and filter all queries
20. Ratings: Share thoughts with the community through Rate & Review
21. Spotlight Mode: Get in the zone by disabling all distractions using Spotlight Mode
22. Mobile Responsive: Highly responsive UI designed for quality user experience


== Changelog ==

= 3.3.1 - March 05, 2025

Fix: Resolved theme style conflicts with the Tutor LMS Course Builder.
Fix: Fixed password reset email content for guest checkout. (Pro)
Fix: Ensured accurate deduction of decimal point values from earnings.
Fix: Fixed the issue of WooCommerce course price appearing on the listing page when monetization is set to Paid Memberships Pro.

= 3.3.0 - March 03, 2025

New: Introduced custom field slots in the course builder for adding custom fields.
New: Added a setting to sell courses exclusively via membership. (Pro)
New: Enabled course-specific selling models, allowing sales via single purchase, subscription, membership, or a combination. (Pro)
New: Added auto-renewal controls for students to manage subscriptions. (Pro)
New: Added membership analytics to Tutor LMS Reports. (Pro)
New: Implemented guest checkout, allowing course purchases without an account. (Pro)
New: Added bulk enrollment via CSV import. (Pro)
New: Introduced "Coming Soon" courses with custom thumbnails. (Pro)
New: Added custom enrollment dates and instant pause options. (Pro)
New: Integrated the 2Checkout payment gateway. (Pro)
Update: Restored the admin bar in the course builder.
Fix: Resolved issues with tag-based filtering.
Fix: Fixed a database error in subscription table creation. (Pro)
Fix: Corrected quiz randomization settings.
Fix: Fixed imported settings not applying from JSON files.

= 3.2.2 - February 05, 2025

Update: Added missing translations in both the free and pro versions.
Update: Improved email template compatibility with WPML. (Pro)
Fix: Resolved special character display issues in Stripe payments. (Pro)
Fix: Fixed the "Edit with Elementor" button for Course Bundles. (Pro)
Fix: Resolved the Zoom meetings auto-activation issue. (Pro)
Fix: Fixed enrollment issues for password-protected courses.
Fix: Resolved REST API errors in Tutor LMS Pro. (Pro)
Fix: Fixed Google Meet authorization issues for instructors. (Pro)
Fix: Fixed an issue where removed payment methods were still appearing as installed.

= 3.2.1 - January 21, 2024

Fix: Resolved the "Page not found" issue for multilingual courses.

= 3.2.0 - January 20, 2024

New: Added Membership support (Phase 1) in the Native Subscription system. (Pro)
New: Redesigned the Native Subscription and introduced Subscriptions for Course Bundles. (Pro)
New: Integrated Authorize.net payment gateway. (Pro)
New: Added re-subscription option upon cancellation or expiration. (Pro)
New: Added a setting to allow or restrict instructors from changing course authors.
Update: Added Support for Internationalised Characters when converting slug.
Update: Implemented a licensing system with OAuth authentication. (Pro)
Update: Redesigned the Addon page and included a plugin installation feature.
Update: Improved responsive design for the course builder.
Update: Email templates now support RTL (Right-to-Left) languages. (Pro)
Fix: Resolved issue with Gutenberg WooCommerce checkout page not enrolling users after guest checkout.
Fix: Adjusted instructor earnings calculation to account for discounted prices when WooCommerce coupons are applied. (Pro)
Fix: Corrected access issue where students retained course access after subscription expiration in Paid Membership Pro. (Pro)
Fix: Fixed error message for H5P quiz ID in Tutor quiz descriptions. (Pro)
Fix: Resolved PHP warning in legacy mode on the course builder.
Fix: Fixed WPML course duplication issue in the new course builder. (Pro)
Fix: The Tutor report graph now displays correctly after translation with Loco Translate. (Pro)
Fix: Email verification links are now correctly encoded. (Pro)
Fix: Fixed enrollment counter not updating for private courses in the student dashboard. (Pro)
Fix: Resolved issue with course thumbnails not being added to WooCommerce products.

= 3.1.0 - December 12, 2024

New: Students can manage email notification preferences from the frontend dashboard
New: Process refunds automatically from the order history page for Stripe and PayPal
Update: Added consent/alert pop-up for quiz/assignment deletions
Fix: Resolved WPML compatibility issue
Fix: Fixed translation errors
Fix: Fixed quiz display time inconsistency related to hour calculations
Fix: Resolved responsive issue on the course archive page for iPad
Fix: Fixed sidebar toggle button visibility issue on mobile devices

= 3.0.2 - December 03, 2024

Fixed: Issue with selecting certificates when only one portrait-type certificate is available.
Fixed: H5P fatal error caused by a version mismatch between Tutor LMS Free and Pro.
Fixed: Canceled enrollments not appearing on the manual enrollment page.
Fixed: Vimeo videos aren’t resuming playback from the last played position.

= 3.0.1 - November 22, 2024

Update: Added "Pay" and "Invoice" buttons to Subscriptions payment history (Pro).
Fix: Fixed WooCommerce bundle courses price incorrectly showing as free (Pro).
Fix: Removed the extra True/False quiz which is appearing in the quiz questions.
Fix: Fixed course description editor not loading due to invalid MIME type.
Fix: Resolved RTL alignment issues in the course builder.

= 3.0.0 - November 20, 2024

New: Redesigned course and quiz builders with an intuitive interface for easier course creation.
New: Native eCommerce to sell courses directly within Tutor LMS.
New: Added popular payment gateways support: PayPal, Stripe, Mollie, Klarna, Razorpay, Paystack, and Alipay.
New: Native Subscription system with recurring payments, certificate control, enrollment fees, sale pricing, and featured plans. (Pro)
New: Introduced advanced Tax management with region-based tax calculations. 
New: Added all-new Coupon management feature to create and manage discount coupons.
New: Streamlined Order management for easy order tracking, updates, completion, cancellation, and refunds.
New: AI Studio for generating courses, lessons, quizzes, thumbnails, and more with AI. (Pro)
New: Added Tutor LMS Cart and Checkout pages for a seamless course purchase experience.
New: Notebook feature for instructors to jot down their course ideas. 
New: H5P integration for creating interactive quizzes. (Pro)
New: Customizable order and subscription email templates. (Pro)
Update: Added course scheduling options for better control over course availability.
Update: Password-protected courses for private access.
Update: Improved manual enrollment process.
Update: Added Legacy Mode support for course and lesson descriptions.
Update: Dynamic permalink generation based on course names.

= 3.0.0-rc - October 25, 2024

Update: Improved the user experience of Tutor LMS Subscription feature. (Pro)
Update: Introduced the functionality to install additional payment gateways. (Pro)
Update: Show tax included text on course listings and detail pages when tax settings are enabled.
Update: Redesigned the checkout page for better user experience.
Update: Implemented dynamic permalink generation based on course name.
Update: Payment Status option is hidden in the manual enrollment tab when using WooCommerce monetization.
Fix: Fixed an issue where users could set unrealistic tax percentages.
Fix: Resolved the issue that caused paid course price to drop to 0 during pagination.
Fix: Fixed 'Mark as Paid' button malfunction when description contains single quotes.
Fix: Ensured that mp3 and mp4 files are displayed correctly in the quiz description.
Fix: Resolved a bug preventing Certificates, Prerequisites, Attachment, and Live Class from appearing even when addons were enabled. (Pro)
Fix: Corrected "Edit with Builder" button functionality on report page. (Pro)
Fix: Flat discount amounts now distribute proportionally among applicable courses.
Fix: Fixed quiz attempt details not showing correct/given answers for ordering questions. (Pro)
Fix: Fixed Frontend Builder loading inappropriately in student and instructor sites.
Fix: Removed BuddyPress tab from builder when the addon is deactivated. (Pro)

= 3.0.0-beta4 - October 14, 2024

Update: Implemented tax calculation on the checkout page.
Update: Added clear discount breakdown in order details.
Update: Displayed payment gateway names on frontend and backend order pages.
Update: Checkout page payment method list design updated.
Fix: Resolved issue preventing permanent order deletion.
Fix: Addressed course/bundle assignment issue when updating automatic coupons.
Fix: Fixed PHP fatal error related to course intro video source.

= 3.0.0-beta3 - October 08, 2024

New: H5P integration added
New: Advanced tax management
New: Password-protected course modal added
New: Certificate control on subscription plans
New: Stripe payment gateway added
Fix: Courses cannot be removed from coupons
Fix: Quiz active question not resetting after discarding changes
Fix: Instructors unable to create AI-generated courses

= 3.0.0-beta2 - September 30, 2024

Update: Added Legacy Mode support for course and lesson descriptions.
Update: Enhanced subscription creation process and resolved issues with subscription expiration and renewal dates. (Pro)
Fix: Automatic WooCommerce product creation for courses, and resolved issues with applying coupon codes for subscription-based courses. (Pro)
Fix: Addressed course bundle behavior, preventing access without completing payment. (Pro)
Fix: Spotlight mode issues in quizzes have been resolved.
Fix: Enhanced course bundle compatibility with Tutor’s native payment. (Pro)
Fix: Fixed subscription trash and delete functionality. (Pro)
Fix: Fatal errors related to course bundles and subscription pages have been resolved. (Pro)
Fix: Corrected warnings and inaccurate order status count for subscriptions. (Pro)
Fix: Addressed issues with lesson prerequisites and full-screen mode.
Fix: AI Studio functionality is enhanced with image generation fixes. (Pro)

= 3.0.0-beta1 - September 10, 2024

New: Brand new course and quiz builder with a fresh, intuitive interface for a smoother course creation experience.
New: Introducing groundbreaking AI Studio in Tutor LMS for generating complete courses, lessons, quizzes, thumbnails, etc with AI. (Pro)
New: Seamlessly sell courses directly using Native Payment within Tutor LMS.
New: Native Subscription system for recurring payments, perfect for membership-style eLearning programs. (Pro)
New: Automated order emails to keep learners and instructors informed at every step of the payment process. (Pro)
New: Customizable order and subscription emails for effortless communications. (Pro)
New: Lesson note feature for instructors to jot down their course or lesson descriptions.
New: Deep integration with Droip no-code website builder to design beautiful course single and listing pages.
Update: Enhanced course creation process for faster, more efficient performance.
Update: Optimized for improved scalability to ensure a smooth experience as your platform grows.

= 2.7.6 - September 19, 2024

New: Filter hook added to alter the redirect URL after course purchase.
Fix: Instructor not able to delete their announcement or student quiz attempts from Admin panel.

= 2.7.5 - September 04, 2024

Fix: Fixed the malfunctioning export feature for Tutor LMS settings.
Fix: Enhanced the user role capabilities verification.

= 2.7.4 - July 31, 2024

Fix: Enhanced security by solving a few vulnerabilities

= 2.7.3 - July 09, 2024

New: Settings for instructors regarding course deletion.
Update: Removed the "Delete Permanently" option from the "All" tab on the course page.
Update: Security enhancement.
Fix: Fixed the course shortcode parameter functionality.

= 2.7.2 - June 6, 2024

Fix: An invalid revenue sharing percentage could be set in the tutor settings
Fix: 'Commission & Fees' tab renamed to 'Commission' to reduce confusion
Fix: Error on Course List page with Restrict Content Pro
Fix: Warning on quiz attempt details page
Fix: Bundle course products were showing on the shop page despite 'Hide Course Products on Shop Page' being enabled (Pro)
Fix: Answer Required option not working for Image Matching, Matching, and Ordering quiz types
Fix: Course progress resetting when WC Subscriptions expired (Pro)
Fix: Assignment submission time was not showing WP timezone on the frontend dashboard (Pro)
Fix: HTML text appearing in enrollment box with Paid Membership Pro (Pro)
Fix: Course content access not working for instructors and admins when a course has prerequisites (Pro)
Fix: Error on first-time installation
Fix: Enhanced plugin security

= 2.7.1 - May 14, 2024

New: Added Quiz Details API
Update: Updated several API endpoints and fortified the API infrastructure
Update: Enriched user experience through multiple enhancements
Fix: Fixed WooCommerce conflicts with Tutor LMS API
Fix: Resolved critical security vulnerabilities
Fix: Fixed “Class Not Found” errors in some scenarios
Fix: Resolved various translation-related issues

= 2.7.0 - April 24, 2024

New: Introduced API for accessing course content
New: Added API for student dashboard functionality (Pro)
New: Implemented API for student calendar event list (Pro)
New: Added API for accessing the student's enrolled courses (Pro)
New: Introduced API for retrieving quiz attempt lists (Pro)
New: Added API for accessing enrolled student lists on a course (Pro)
New: Implemented API for accepting instructor registration applications (Pro)
New: Added API for viewing student order history (Pro)
New: Introduced APIs for profile management (Pro)
New: Implemented APIs for Q&A management (Pro)
Update: Compatibility with WordPress 6.5
Update: Implemented various enhancements to improve the overall user experience
Fix: Fixed the duplicate H1 tags issue on every single page
Fix: Resolved various translation-related issues
Fix: Enhanced security by solving a few vulnerabilities

= 2.6.2 - March 11, 2024

New: APIs for enabling students to submit assignments (Pro)
New: APIs allowing students to add courses to their wishlists (Pro)
New: APIs enabling students to review and rate courses (Pro)
Update: Some enhancements to improve the overall experience
Fix: Strengthened security to prevent data loss

= 2.6.1 - February 19, 2024

New: Added API functionality for submitting and retrieving list of quizzes (Pro)
Update: Improved security to ensure safe submission of questions and answers
Update: Improved response data, extendability, and performance across all APIs within the Tutor LMS Free plugin
Fix: Fixed issue causing "Resource not found" error related to ChatGPT (Pro)

= 2.6.0 - January 11, 2024

New: Added Write and Delete permissions in REST API (Pro)
New: Automatic permalink updates when required
New: Support for quiz base permalink updates
New: Support for assignment base permalink updates (Pro)
New: Added placeholder support on email heading (Pro)
Update: Added PHP 8.1 and 8.2 compatibility
Update: Updated compatibility with the latest WooCommerce database update
Update: The "Restore Default" option for the email trigger's data (Pro)
Fix: Resolved assignment file upload option disappearance when the max file upload option is set to zero (Pro)
Fix: Resolved issue with JS files translation not working properly (Pro)
Fix: Fixed email logo blurriness and logo not appear on email when hotlink protection is enabled (Pro)
Fix: Resolved the category filters not working on the backend course bundle page (Pro)
Fix: Resolved password strength not showing on registration pages
Fix: Fixed Easy Digital Downloads (EDD) Pro not appearing on the Monetization dropdown menu
Fix: Fixed the issues with blank assignment submissions caused by delayed page loading (Pro)
Fix: Resolved design conflict with block themes in the course content area
Fix: Fixed email not triggering when updating course status from the backend course list page (Pro)
Fix: "All fields required" messages when creating a Zoom meeting with non-English time settings (Pro)


[View the full changelog](https://tutorlms.com/releases/)

== Upgrade Notice ==

It’s recommended to have a backup of your website before hitting the update button.